.sl-map {
  display: flex;
  align-items: center;
}

@media (max-width: 959px) {
  .sl-map {
    display: block;
  }
}

.sl-map__block-wrapper {
  flex: 1 0 0;
  position: relative;
  align-self: stretch;
}

.sl-map__block-wrapper--full {
  padding-top: 50%;
}

@media (max-width: 959px) {
  .sl-map__block-wrapper--full {
    padding-top: 100%;
  }
}

.sl-map__block-wrapper--full .sl-map__block {
  position: absolute;
  z-index: 1;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

.sl-map__block {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
  justify-content: center;
  overflow: hidden;
}

.sl-map__info {
  padding: 30px;
  text-align: center;
}

@media (max-width: 959px) {
  .sl-map__info {
    padding: 40px;
  }
}

.sl-map__title {
  margin-bottom: 15px;
}

.sl-map__store-info {
  margin-bottom: 30px;
}

.sl-map__btn {
  display: inline-flex !important;
  align-items: center;
}

.sl-map__btn .btn-icon {
  margin-right: 10px;
}

.sl-map__btn .btn-text {
  flex: 1 0 0;
  word-break: break-word;
}

.sl-map__display {
  height: 100%;
}

.sl-map__display .image-position-80\%-0 {
  object-position: 80% 0 !important;
}

.sl-map__display .image-position-20\%-0 {
  object-position: 20% 0 !important;
}

.sl-map__display .image-position-20\%-50\% {
  object-position: 20% 50% !important;
}

.sl-map__display .image-position-80\%-50\% {
  object-position: 80% 50% !important;
}

.sl-map__display .image-position-20\%-100\% {
  object-position: 20% 100% !important;
}

.sl-map__display .image-position-80\%-100\% {
  object-position: 80% 100% !important;
}

.sl-map__display .image-position-center-center {
  object-position: center center !important;
}

.sl-map__display .image-position-top-center {
  object-position: center top !important;
}

.sl-map__display .image-position-bottom-center {
  object-position: center bottom !important;
}

.sl-map__iframe {
  border: 0;
  width: 100%;
  height: 100%;
  background-color: #fff;
}

.sl-map__empty-img {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200%;
  height: 200%;
  background-color: rgba(var(--color-text), 0.05);
}

.sl-map__image {
  position: relative;
  width: 100%;
  height: 100%;
}

.sl-map__image img {
  object-fit: cover;
  width: 100%;
  height: 100%;
  display: block;
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
