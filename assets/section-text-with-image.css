.text-with-image__image__container1 {
  display: flex;
  align-items: center;
}

.text-with-image__label1,
text-with-image__label2 {
  align-items: flex-start;
}

.text-with-image__label1 {
  display: flex;
  flex-direction: column;
  margin-left: 87px;
  margin-top: -162px;
}

.text-with-image__subtitle {
  margin-bottom: 10px;
}

.text-with-image__title {
  margin-bottom: 15px;
}

.text-with-image__image {
  position: relative;
  overflow: hidden;
}

.text-with-image__image-line {
  border: 1px solid #ffffff;
  position: absolute;
  left: 40px;
  bottom: 40px;
  width: 100%;
  height: 100%;
  pointer-events: none;
}

.text-with-image__image__container2 {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  position: relative;
}

.text-with-image__label2 {
  margin-right: 132.75px;
}

.text-with-image__image2 {
  margin-top: -162px;
}

.text-with-image__image-adapt {
  min-width: 45%;
  width: 45%;
}

.text-with-image__image-adapt img {
  width: 100%;
  height: auto;
}

.text-with-image__image-lg {
  min-width: 45%;
  width: 45%;
}

.text-with-image__image-lg img {
  width: 100%;
  height: 700px;
}

.text-with-image__image-sm {
  min-width: 45%;
  width: 45%;
}

.text-with-image__image-sm img {
  width: 100%;
  height: 500px;
}

.text-with-image__image-lg .placeholder,
.text-with-image__image-adapt .placeholder {
  width: 100%;
  height: 700px;
  background-color: rgb(var(--color-image-background));
}

.text-with-image__image-sm .placeholder {
  width: 100%;
  height: 500px;
  background-color: rgb(var(--color-image-background));
}

.text-with-image__image img,
.text-with-image__image2 img {
  object-fit: cover;
}

.text-with-image__product-container {
  position: absolute;
  left: 40px;
  bottom: 40px;
  background-color: rgba(var(--color-page-background));
  width: 300px;
  height: 80px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-with-image__product-container2 {
  position: absolute;
  right: 40px;
  bottom: 40px;
  background-color: rgba(var(--color-page-background));
  width: 300px;
  height: 80px;
  padding: 4px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.text-with-image__product-label {
  padding: 10px 8px;
}

.text-with-image__product-label-name {
  width: 210px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.text-with-image__product-image {
  width: 72px;
  height: 72px;
  object-fit: cover;
}

.text-with-image__product-image img {
  width: 100%;
  height: 100%;
}

@media (max-width: 959px) {
  .text-with-image__image-line {
    left: 16px;
    bottom: 16px;
  }

  .text-with-image__image__container1 {
    flex-direction: column;
    align-items: flex-start;
  }

  .text-with-image__image__container1 .text-with-image__label1 {
    justify-content: flex-start;
    margin-left: 0;
    margin-bottom: 20px;
    margin-top: 0;
  }

  .text-with-image__image__container1 .text-with-image__image {
    width: 100%;
    line-height: 0;
  }

  .text-with-image__product-container {
    width: auto;
    left: 16px;
    bottom: 16px;
  }

  .text-with-image__product-container2 {
    width: auto;
    right: 16px;
    bottom: 16px;
  }

  .text-with-image__image__container2 {
    flex-direction: column;
    margin-top: 20px;
  }

  .text-with-image__image__container2 .text-with-image__image2 {
    margin-top: 0;
    line-height: 0;
  }

  .text-with-image__image__container2 .text-with-image__label2 {
    width: 100%;
    margin-left: 0;
    margin-top: 20px;
    margin-right: 0;
  }

  .text-with-image__image__container2 .text-with-image__image2 {
    width: 100%;
  }
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
