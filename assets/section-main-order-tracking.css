.order-tracking {
  max-width: 430px;
  margin: 0 auto;
}

.order-tracking .submit {
  width: 100%;
  text-transform: capitalize;
}

.order-tracking-form-title {
  margin-bottom: 20px;
}

@media (max-width: 959px) {
  .order-tracking-form-title {
    margin-bottom: 15px;
  }
}

@media (max-width: 959px) {
  .order-tracking {
    padding: 40px 20px;
    width: 100%;
  }
}

.order-tracking .tab {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 32px;
  gap: 40px;
}

.order-tracking .tab a {
  width: 50%;
  color: rgb(var(--color-light-text));
}

.order-tracking .tab a:first-child {
  position: relative;
  text-align: right;
}

.order-tracking .tab a:first-child::after {
  content: " ";
  height: 10px;
  width: 1px;
  position: absolute;
  right: -20px;
  top: 50%;
  transform: translateY(-50%);
  background-color: rgb(var(--color-entry-line));
}

.order-tracking .tab > a {
  cursor: pointer;
}

.order-tracking .tab .active {
  color: rgb(var(--color-text));
  text-decoration: underline;
  text-underline-offset: 6px;
}

.order-tracking .error-message {
  color: #f04949;
  font-weight: 400;
  font-size: 12px;
}

.order-tracking .error-message:not(:empty) {
  margin-top: -14px;
  margin-bottom: 20px;
}

.order-tracking .field[data-type="mobile"] .field__suffix {
  padding-top: 0;
  padding-bottom: 0;
}

.order-tracking .country-select-wrapper {
  display: flex;
  padding-left: 8px;
  position: relative;
}

.order-tracking .country-select-wrapper .country-select {
  appearance: none;
  border: none;
  bottom: 0;
  cursor: pointer;
  left: 0;
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
}

.order-tracking .country-select-wrapper .country-select-trigger {
  padding-left: 16px;
  display: flex;
  align-items: center;
  position: relative;
}

.order-tracking .country-select-wrapper .country-select-trigger span {
  color: rgb(var(--color-text));
  padding-right: 4px;
}

.order-tracking .country-select-wrapper .country-select-trigger::before {
  border-left: 1px solid rgb(var(--color-entry-line));
  content: " ";
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
