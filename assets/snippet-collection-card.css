.collection__item__image {
  position: relative;
}
.collection__item__image img {
  width: 100%;
  height: auto;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}
.collection__item__image svg {
  width: 100%;
  height: auto;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
}

.collection__item__name {
  margin-top: 8px;
}

.collection__scroll__item {
  width: 100%;
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
