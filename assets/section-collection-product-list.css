.collection.loading::before {
  content: "";
  position: fixed;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 50;
  opacity: 0;
}

.collection .empty-tips {
  text-align: center;
}

.collection .facets-loading {
  display: none;
  position: fixed;
  z-index: 51;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 92px;
  height: 92px;
  border-radius: 12px;
  background-color: rgb(var(--color-page-background));
  box-shadow: 0px 10px 50px 0px rgba(var(--color-text), 0.08);
}

.collection .facets-loading > div {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
}

.collection .facets-loading > div svg {
  width: 60px;
  height: 60px;
}

.collection.loading .facets-loading {
  display: block;
}

.infinite-scroll-button-tip {
  margin-top: 48px;
  margin-bottom: 12px;
  text-align: center;
}

.infinite-scroll-button-wrapper {
  text-align: center;
}

.infinite-scroll-button-wrapper.hidden {
  display: none;
}

.infinite-scroll-loading {
  display: none;
  color: rgb(var(--color-text));
  margin-top: 48px;
}

.infinite-scroll-loading.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
  gap: 4px;
}

.infinite-scroll-loading .infinite-scroll-loading-tip {
  margin-left: 18px;
}

@media (max-width: 959px) {
  .infinite-scroll-loading {
    margin-top: 20px;
  }
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
