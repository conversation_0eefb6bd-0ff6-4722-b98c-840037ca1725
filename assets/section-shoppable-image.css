/*product-tooltip start*/
.product-tooltip__image-wrapper {
  width: 72px;
  min-height: 72px;
  position: relative;
  background-color: rgb(var(--color-image-background));
  border-radius: 4px;
  overflow: hidden;
}
.product-tooltip__image-wrapper > svg {
  width: 72px;
  height: 72px;
}
.product-tooltip__image-wrapper .product-tooltip__image-padding {
  padding-top: 100%;
  height: 100%;
  position: relative;
  width: 100%;
}
.product-tooltip__image-wrapper .product-tooltip__image-padding > img {
  height: 100%;
  left: 0;
  object-fit: cover;
  object-position: center;
  position: absolute;
  top: 0;
  width: 100%;
  display: block;
}
.product-tooltip__content {
  flex: 1 0 0;
  padding-left: 12px;
  overflow: hidden;
}
.product-tooltip__title {
  margin: 0;
  display: -webkit-box;
  overflow: hidden;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  font-weight: 600;
}
.product-tooltip__link {
  margin-top: 8px;
  display: inline-flex;
  align-items: center;
  word-break: break-word;
}
.product-tooltip__link-icon {
  margin-left: 5px;
  width: 8px;
  height: 8px;
  flex-shrink: 0;
}
/*end product-tooltip*/
@keyframes fade-slide-in {
  0% {
    opacity: 0;
    transform: translateY(120%);
  }

  100% {
    opacity: 1;
    transform: translateY(0%);
  }
}
.shoppable-image .fade-show {
  animation: fade-slide-in 0.8s;
}
.shoppable-image {
  overflow: hidden;
  position: relative;
  display: block;
}
.shoppable-image__wrapper {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  overflow: hidden;
}
.shoppable-image__image-wrapper {
  position: relative;
  width: 100%;
  background-color: rgba(0, 0, 0, 0.3);
}
@media (min-width: 960px) {
  .shoppable-image__image-wrapper--pc_empty {
    min-height: 500px;
  }
}
@media (max-width: 959px) {
  .shoppable-image__image-wrapper {
    /* When both are empty */
  }
  .shoppable-image__image-wrapper--mobile_empty.shoppable-image__image-wrapper--pc_empty {
    min-height: 450px;
  }
}
@media (max-width: 959px) {
  .shoppable-image__image-wrapper {
    /* &:not(.shoppable-image__image-wrapper--mobile_empty) .placeholder_svg {
      display: none;
    } */
  }
}
.shoppable-image__image-wrapper > img {
  display: inline-block;
  vertical-align: middle;
  width: 100%;
  height: auto;
}
.shoppable-image__hotspot {
  position: absolute;
  width: 30px;
  height: 30px;
  cursor: pointer;
  border-radius: 50%;
  outline: 0;
  z-index: 2;
}
.shoppable-image__hotspot:hover::before,
.shoppable-image__hotspot:hover::after {
  transform: scale(1.1);
}
.shoppable-image__hotspot::before,
.shoppable-image__hotspot::after {
  content: "";
  height: 15px;
  width: 15px;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  background-color: rgb(var(--color__hotspot));
  transition: transform 0.2s;
}
.shoppable-image__hotspot::after {
  animation: pulse 2s infinite;
  opacity: 0.5;
  box-shadow: 0 0 0 rgb(var(--color__hotspot));
}
@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgb(var(--color__hotspot));
    opacity: 0.5;
  }

  70% {
    box-shadow: 0 0 0 10px rgb(var(--color__hotspot));
    opacity: 0.5;
  }

  95% {
    box-shadow: 0 0 0 10px rgb(var(--color__hotspot));
    opacity: 0;
  }

  100% {
    box-shadow: 0 0 0 0 rgb(var(--color__hotspot));
    opacity: 0;
  }
}
.shoppable-image__hotspot .hotspot__tooltip-wrapper {
  position: absolute;
  top: 0px;
  left: 46px;
  background-color: rgb(var(--color-page-background));
  border: 1px solid rgb(var(--color-entry-line));
  padding: 16px;
  text-decoration: none;
  pointer-events: none;
  opacity: 0;
  transition: opacity 0.2s, transform 0.2s;
  transition-delay: 300ms;
  transform: translateY(-16px);
  box-shadow: 0px 10px 20px rgba(var(--color-text), 0.1);
  width: 280px;
  border-radius: 8px;
}
.shoppable-image__hotspot--active {
  z-index: 3;
}
.shoppable-image__hotspot--active::after {
  animation: none;
  box-shadow: 0 0 0 10px rgb(var(--color__hotspot, 255, 255, 255));
  opacity: 0.5;
}
.shoppable-image__hotspot--active .hotspot__tooltip-wrapper {
  opacity: 1;
  transform: translateY(0);
  pointer-events: initial;
}
@keyframes hotspotShow {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}
shoppable-image[data-show-type="fixed"] .shoppable-image__hotspot {
  position: absolute;
  width: 20px;
  height: 20px;
  cursor: pointer;
  border-radius: 50%;
  outline: 0;
  z-index: 2;
  opacity: 0;

  animation-duration: 0.2s;
  animation-name: hotspotShow;
  animation-fill-mode: forwards;
}
@media (min-width: 960px) {
  shoppable-image[data-show-type="fixed"]
    .shoppable-image__hotspot:hover::before,
  shoppable-image[data-show-type="fixed"]
    .shoppable-image__hotspot:hover::after {
    transform: unset;
  }

  shoppable-image[data-show-type="fixed"]
    .shoppable-image__hotspot:hover::before {
    border: 2px solid rgb(var(--color__hotspot));
  }

  shoppable-image[data-show-type="fixed"]
    .shoppable-image__hotspot:hover
    .hotspot__tooltip-wrapper {
    background-color: rgba(0, 0, 0, 0.7);
  }

  shoppable-image[data-show-type="fixed"]
    .shoppable-image__hotspot:hover--active {
    z-index: 4;
  }
}
shoppable-image[data-show-type="fixed"] .shoppable-image__hotspot::before,
shoppable-image[data-show-type="fixed"] .shoppable-image__hotspot::after {
  content: "";
  height: 20px;
  width: 20px;
  border-radius: 50%;
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  right: 0;
  margin: auto;
  background-color: rgba(var(--color__hotspot), 0.4);
}
shoppable-image[data-show-type="fixed"] .shoppable-image__hotspot::after {
  animation: none;
  box-shadow: unset;
  opacity: unset;
  height: 8px;
  width: 8px;
  background-color: rgb(var(--color__hotspot));
}
shoppable-image[data-show-type="fixed"]
  .shoppable-image__hotspot
  .hotspot__tooltip-wrapper {
  position: absolute;
  top: 26px;
  left: 0px;
  background-color: rgba(0, 0, 0, 0.5);
  border: 1px solid rgba(var(--color-entry-line), 0.4);
  padding: 10px;
  text-decoration: none;
  opacity: 0;
  transform: translateY(-16px);
  transition: opacity 0.2s, transform 0.2s;
  transition-delay: 300ms;
  width: 200px;
  border-radius: 8px;
}
shoppable-image[data-show-type="fixed"]
  .shoppable-image__hotspot
  .hotspot__tooltip-wrapper
  .fixed-card-content {
  display: flex;
  color: rgb(var(--color-page-background));
}
shoppable-image[data-show-type="fixed"]
  .shoppable-image__hotspot
  .hotspot__tooltip-wrapper
  .fixed-card-content
  .fixed-card-text {
  font-family: var(--body-font);
  flex: 1;
}
shoppable-image[data-show-type="fixed"]
  .shoppable-image__hotspot
  .hotspot__tooltip-wrapper
  .fixed-card-content
  .fixed-card-text
  .fixed-card-title {
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  font-size: 13px;
  font-weight: 600;
  line-height: 140%;
}
@media (max-width: 959px) {
  shoppable-image[data-show-type="fixed"]
    .shoppable-image__hotspot
    .hotspot__tooltip-wrapper
    .fixed-card-content
    .fixed-card-text
    .fixed-card-title {
    font-size: 10px;
  }
}
shoppable-image[data-show-type="fixed"]
  .shoppable-image__hotspot
  .hotspot__tooltip-wrapper
  .fixed-card-content
  .fixed-card-price {
  margin-top: 8px;
  font-size: 14px;
  font-weight: 600;
  line-height: 160%;
}
@media (max-width: 959px) {
  shoppable-image[data-show-type="fixed"]
    .shoppable-image__hotspot
    .hotspot__tooltip-wrapper
    .fixed-card-content
    .fixed-card-price {
    font-size: 10px;
    margin-top: 0px;
  }
}
shoppable-image[data-show-type="fixed"]
  .shoppable-image__hotspot
  .hotspot__tooltip-wrapper
  .fixed-card-content
  .fixed-card-price
  .price-item-origin {
  text-decoration: line-through;
  opacity: 0.6;
  font-size: 13px;
  font-weight: 400;
}
@media (max-width: 959px) {
  shoppable-image[data-show-type="fixed"]
    .shoppable-image__hotspot
    .hotspot__tooltip-wrapper
    .fixed-card-content
    .fixed-card-price
    .price-item-origin {
    font-size: 10px;
  }
}
shoppable-image[data-show-type="fixed"]
  .shoppable-image__hotspot
  .hotspot__tooltip-wrapper
  .fixed-card-icon {
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: rgb(var(--color-page-background));
}
shoppable-image[data-show-type="fixed"]
  .shoppable-image__hotspot
  .hotspot__tooltip-wrapper
  .fixed-card-icon
  svg {
  width: 14px;
  height: 14px;
  position: static;
  left: unset;
  top: unset;
  transform: unset;
  color: rgb(var(--color-button-background));
}
shoppable-image[data-show-type="fixed"] .shoppable-image__hotspot--active {
  z-index: 3;
}
shoppable-image[data-show-type="fixed"]
  .shoppable-image__hotspot--active
  .hotspot__tooltip-wrapper {
  opacity: 1;
  transform: translateY(0);
}
.shoppable-image-quick-add-btn {
  display: block;
  background: none;
  border: none;
  padding: 0;
  text-align: left;
}
.shoppable-image__info {
  flex: 1 0 0;
  padding: 80px;
}
@media (max-width: 959px) {
  .shoppable-image__info {
    padding: 40px;
  }
}
.shoppable-image__info.is-text-left {
  text-align: left;
}
.shoppable-image__info.is-text-center {
  text-align: center;
}
.shoppable-image__info.is-text-right {
  text-align: right;
}
.shoppable-image__info .info__row {
  width: 100%;
}
.shoppable-image__info .info__title {
  margin-top: 0;
  text-transform: uppercase;
}
.shoppable-image__info .info__description {
  margin-top: 8px;
  color: var(--description-text-color, #fff);
}
.shoppable-image__info .info__description.rte a {
  color: inherit;
}
.shoppable-image__info .info__btn {
  text-transform: uppercase;
  margin-top: 30px;
  position: relative;
  display: inline-block;
}
@media (max-width: 959px) {
  .shoppable-image__info .info__btn {
    margin-top: 15px;
  }
}
.shoppable-image-text-container {
  width: 280px;
  padding: 10px 8px;
  display: block;
}
.shoppable-image-text-container .shoppable-image-text-title {
  margin-bottom: 10px;
  font-weight: 600;
}
.shoppable-image-text-container .shoppable-image-text-desc {
  margin-bottom: 10px;
}
/*Full-screen*/
.shoppable-image.full_width {
  max-width: 100%;
  padding-left: 0;
  padding-right: 0;
}
.shoppable-image__image-wrapper {
  width: 100%;
}
.shoppable-image .text-position--left-top {
  flex-direction: unset;
  align-items: flex-start;
  justify-content: flex-start;
}
.shoppable-image .text-position--top {
  flex-direction: unset;
  align-items: flex-start;
  justify-content: center;
}
.shoppable-image .text-position--right-top {
  flex-direction: unset;
  align-items: flex-start;
  justify-content: flex-end;
}
.shoppable-image .text-position--left {
  flex-direction: unset;
  align-items: center;
  justify-content: flex-start;
}
.shoppable-image .text-position--center {
  flex-direction: unset;
  align-items: center;
  justify-content: center;
}
.shoppable-image .text-position--right {
  flex-direction: unset;
  align-items: center;
  justify-content: flex-end;
}
.shoppable-image .text-position--left-bottom {
  flex-direction: unset;
  align-items: flex-end;
  justify-content: flex-start;
}
.shoppable-image .text-position--bottom {
  flex-direction: unset;
  align-items: flex-end;
  justify-content: center;
}
.shoppable-image .text-position--right-bottom {
  flex-direction: unset;
  align-items: flex-end;
  justify-content: flex-end;
}
.shoppable-image svg {
  display: block;
  height: 100%;
  max-height: 800px;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}
@media (max-width: 959px) {
  .shoppable-image svg {
    height: 80%;
  }
}
.shoppable-image .shoppable-image__info {
  position: absolute;
  max-width: 350px;
  padding: 0;
  margin: 80px;
}
@media (max-width: 959px) {
  .shoppable-image .shoppable-image__info {
    margin: 40px;
  }
}
.shoppable-image .product-tooltip {
  display: flex;
  color: rgb(var(--color-text));
  align-items: flex-start;
}
.shoppable-image .product-tooltip--empty {
  min-width: auto;
}
.shoppable-image .product-tooltip:hover .product-tooltip__link {
  text-decoration: underline;
}
.product-tooltip__btn {
  margin-top: 2px;
  display: flex;
  align-items: center;
}
.product-tooltip__btn svg {
  width: 10px;
  height: 10px;
  position: static;
  left: unset;
  top: unset;
  transform: rotate(-90deg);
}
/* The ipad end responds to the mobile end in vertical screen */
/* @custom-media --tablet (max-width: 959px); */
/* @custom-media --gt-mobile (min-width: 751px); */
/* detectingScreen need to consider the configuration of the tablet */
