!(function (e, t) {
  'object' == typeof exports && 'object' == typeof module
    ? (module.exports = t())
    : 'function' == typeof define && define.amd
    ? define([], t)
    : 'object' == typeof exports
    ? (exports.AirDatepicker = t())
    : (e.AirDatepicker = t());
})(this, function () {
  return (function () {
    'use strict';
    var e = {
        d: function (t, i) {
          for (var s in i) e.o(i, s) && !e.o(t, s) && Object.defineProperty(t, s, { enumerable: !0, get: i[s] });
        },
        o: function (e, t) {
          return Object.prototype.hasOwnProperty.call(e, t);
        },
      },
      t = {};
    e.d(t, {
      default: function () {
        return K;
      },
    });
    var i = {
        days: 'days',
        months: 'months',
        years: 'years',
        day: 'day',
        month: 'month',
        year: 'year',
        eventChangeViewDate: 'changeViewDate',
        eventChangeCurrentView: 'changeCurrentView',
        eventChangeFocusDate: 'changeFocusDate',
        eventChangeSelectedDate: 'changeSelectedDate',
        eventChangeTime: 'changeTime',
        eventChangeLastSelectedDate: 'changeLastSelectedDate',
        actionSelectDate: 'selectDate',
        actionUnselectDate: 'unselectDate',
        cssClassWeekend: '-weekend-',
      },
      s = {
        classes: '',
        inline: !1,
        locale: {
          days: ['Воскресенье', 'Понедельник', 'Вторник', 'Среда', 'Четверг', 'Пятница', 'Суббота'],
          daysShort: ['Вос', 'Пон', 'Вто', 'Сре', 'Чет', 'Пят', 'Суб'],
          daysMin: ['Вс', 'Пн', 'Вт', 'Ср', 'Чт', 'Пт', 'Сб'],
          months: [
            'Январь',
            'Февраль',
            'Март',
            'Апрель',
            'Май',
            'Июнь',
            'Июль',
            'Август',
            'Сентябрь',
            'Октябрь',
            'Ноябрь',
            'Декабрь',
          ],
          monthsShort: ['Янв', 'Фев', 'Мар', 'Апр', 'Май', 'Июн', 'Июл', 'Авг', 'Сен', 'Окт', 'Ноя', 'Дек'],
          today: 'Сегодня',
          clear: 'Очистить',
          dateFormat: 'dd.MM.yyyy',
          timeFormat: 'HH:mm',
          firstDay: 1,
        },
        startDate: new Date(),
        firstDay: '',
        weekends: [6, 0],
        dateFormat: '',
        altField: '',
        altFieldDateFormat: 'T',
        toggleSelected: !0,
        keyboardNav: !0,
        selectedDates: !1,
        container: '',
        isMobile: !1,
        visible: !1,
        position: 'bottom left',
        offset: 12,
        view: i.days,
        minView: i.days,
        showOtherMonths: !0,
        selectOtherMonths: !0,
        moveToOtherMonthsOnSelect: !0,
        showOtherYears: !0,
        selectOtherYears: !0,
        moveToOtherYearsOnSelect: !0,
        minDate: '',
        maxDate: '',
        disableNavWhenOutOfRange: !0,
        multipleDates: !1,
        multipleDatesSeparator: ', ',
        range: !1,
        dynamicRange: !0,
        buttons: !1,
        monthsField: 'monthsShort',
        showEvent: 'focus',
        autoClose: !1,
        prevHtml: '<svg><path d="M 17,12 l -5,5 l 5,5"></path></svg>',
        nextHtml: '<svg><path d="M 14,12 l 5,5 l -5,5"></path></svg>',
        navTitles: { days: 'MMMM, <i>yyyy</i>', months: 'yyyy', years: 'yyyy1 - yyyy2' },
        timepicker: !1,
        onlyTimepicker: !1,
        dateTimeSeparator: ' ',
        timeFormat: '',
        minHours: 0,
        maxHours: 24,
        minMinutes: 0,
        maxMinutes: 59,
        hoursStep: 1,
        minutesStep: 1,
        onSelect: !1,
        onChangeViewDate: !1,
        onChangeView: !1,
        onRenderCell: !1,
        onShow: !1,
        onHide: !1,
        onClickDayName: !1,
      };
    function a(e) {
      let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : document;
      return 'string' == typeof e ? t.querySelector(e) : e;
    }
    function n() {
      let {
          tagName: e = 'div',
          className: t = '',
          innerHtml: i = '',
          id: s = '',
          attrs: a = {},
        } = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {},
        n = document.createElement(e);
      if ((t && n.classList.add(...t.split(' ')), s && (n.id = s), i && (n.innerHTML = i), a))
        for (let e in a) n.setAttribute(e, a[e]);
      return n;
    }
    function r(e, t) {
      for (let [i, s] of Object.entries(t)) e.setAttribute(i, s);
      return e;
    }
    function h(e) {
      return new Date(e.getFullYear(), e.getMonth() + 1, 0).getDate();
    }
    function o(e) {
      let t = e.getHours(),
        i = t % 12 == 0 ? 12 : t % 12;
      return {
        year: e.getFullYear(),
        month: e.getMonth(),
        fullMonth: e.getMonth() + 1 < 10 ? '0' + (e.getMonth() + 1) : e.getMonth() + 1,
        date: e.getDate(),
        fullDate: e.getDate() < 10 ? '0' + e.getDate() : e.getDate(),
        day: e.getDay(),
        hours: t,
        fullHours: l(t),
        hours12: i,
        fullHours12: l(i),
        minutes: e.getMinutes(),
        fullMinutes: e.getMinutes() < 10 ? '0' + e.getMinutes() : e.getMinutes(),
      };
    }
    function l(e) {
      return e < 10 ? '0' + e : e;
    }
    function d(e) {
      let t = 10 * Math.floor(e.getFullYear() / 10);
      return [t, t + 9];
    }
    function c() {
      let e = [];
      for (var t = arguments.length, i = new Array(t), s = 0; s < t; s++) i[s] = arguments[s];
      return (
        i.forEach((t) => {
          if ('object' == typeof t) for (let i in t) t[i] && e.push(i);
          else t && e.push(t);
        }),
        e.join(' ')
      );
    }
    function u(e, t) {
      let s = arguments.length > 2 && void 0 !== arguments[2] ? arguments[2] : i.days;
      if (!e || !t) return !1;
      let a = o(e),
        n = o(t),
        r = {
          [i.days]: a.date === n.date && a.month === n.month && a.year === n.year,
          [i.months]: a.month === n.month && a.year === n.year,
          [i.years]: a.year === n.year,
        };
      return r[s];
    }
    function p(e, t, i) {
      let s = g(e, !1).getTime(),
        a = g(t, !1).getTime();
      return i ? s >= a : s > a;
    }
    function m(e, t) {
      return !p(e, t, !0);
    }
    function g(e) {
      let t = !(arguments.length > 1 && void 0 !== arguments[1]) || arguments[1],
        i = new Date(e.getTime());
      return 'boolean' != typeof t || t || D(i), i;
    }
    function D(e) {
      return e.setHours(0, 0, 0, 0), e;
    }
    function v(e, t, i) {
      e.length
        ? e.forEach((e) => {
            e.addEventListener(t, i);
          })
        : e.addEventListener(t, i);
    }
    function y(e, t) {
      return !(!e || e === document || e instanceof DocumentFragment) && (e.matches(t) ? e : y(e.parentNode, t));
    }
    function f(e, t, i) {
      return e > i ? i : e < t ? t : e;
    }
    function w(e) {
      for (var t = arguments.length, i = new Array(t > 1 ? t - 1 : 0), s = 1; s < t; s++) i[s - 1] = arguments[s];
      return (
        i
          .filter((e) => e)
          .forEach((t) => {
            for (let [i, s] of Object.entries(t))
              if (void 0 !== s && '[object Object]' === s.toString()) {
                let t = void 0 !== e[i] ? e[i].toString() : void 0,
                  a = s.toString(),
                  n = Array.isArray(s) ? [] : {};
                (e[i] = e[i] ? (t !== a ? n : e[i]) : n), w(e[i], s);
              } else e[i] = s;
          }),
        e
      );
    }
    function b(e) {
      let t = e;
      return (
        e instanceof Date || (t = new Date(e)),
        isNaN(t.getTime()) && (console.log('Unable to convert value "'.concat(e, '" to Date object')), (t = !1)),
        t
      );
    }
    function k(e) {
      let t = '\\s|\\.|-|/|\\\\|,|\\$|\\!|\\?|:|;';
      return new RegExp('(^|>|' + t + ')(' + e + ')($|<|' + t + ')', 'g');
    }
    function C(e, t, i) {
      return (
        t in e ? Object.defineProperty(e, t, { value: i, enumerable: !0, configurable: !0, writable: !0 }) : (e[t] = i),
        e
      );
    }
    class _ {
      constructor() {
        let {
          type: e,
          date: t,
          dp: i,
          opts: s,
          body: a,
        } = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        C(this, 'focus', () => {
          this.$cell.classList.add('-focus-'), (this.focused = !0);
        }),
          C(this, 'removeFocus', () => {
            this.$cell.classList.remove('-focus-'), (this.focused = !1);
          }),
          C(this, 'select', () => {
            this.$cell.classList.add('-selected-'), (this.selected = !0);
          }),
          C(this, 'removeSelect', () => {
            this.$cell.classList.remove('-selected-', '-range-from-', '-range-to-'), (this.selected = !1);
          }),
          C(this, 'onChangeSelectedDate', () => {
            this.isDisabled || (this._handleSelectedStatus(), this.opts.range && this._handleRangeStatus());
          }),
          C(this, 'onChangeFocusDate', (e) => {
            if (!e) return void (this.focused && this.removeFocus());
            let t = u(e, this.date, this.type);
            t ? this.focus() : !t && this.focused && this.removeFocus(), this.opts.range && this._handleRangeStatus();
          }),
          C(this, 'render', () => ((this.$cell.innerHTML = this._getHtml()), (this.$cell.adpCell = this), this.$cell)),
          (this.type = e),
          (this.singleType = this.type.slice(0, -1)),
          (this.date = t),
          (this.dp = i),
          (this.opts = s),
          (this.body = a),
          (this.customData = !1),
          this.init();
      }
      init() {
        let { range: e, onRenderCell: t } = this.opts;
        t && (this.customData = t({ date: this.date, cellType: this.singleType, datepicker: this.dp })),
          this._createElement(),
          this._bindDatepickerEvents(),
          this._handleInitialFocusStatus(),
          this.dp.hasSelectedDates && (this._handleSelectedStatus(), e && this._handleRangeStatus());
      }
      _bindDatepickerEvents() {
        this.dp.on(i.eventChangeSelectedDate, this.onChangeSelectedDate),
          this.dp.on(i.eventChangeFocusDate, this.onChangeFocusDate);
      }
      unbindDatepickerEvents() {
        this.dp.off(i.eventChangeSelectedDate, this.onChangeSelectedDate),
          this.dp.off(i.eventChangeFocusDate, this.onChangeFocusDate);
      }
      _createElement() {
        let { year: e, month: t, date: i } = o(this.date);
        this.$cell = n({ className: this._getClassName(), attrs: { 'data-year': e, 'data-month': t, 'data-date': i } });
      }
      _getClassName() {
        var e, t;
        let s = new Date(),
          { selectOtherMonths: a, selectOtherYears: n } = this.opts,
          { minDate: r, maxDate: h } = this.dp,
          { day: l } = o(this.date),
          d = this._isOutOfMinMaxRange(),
          p = null === (e = this.customData) || void 0 === e ? void 0 : e.disabled,
          m = c('air-datepicker-cell', '-'.concat(this.singleType, '-'), {
            '-current-': u(s, this.date, this.type),
            '-min-date-': r && u(r, this.date, this.type),
            '-max-date-': h && u(h, this.date, this.type),
          }),
          g = '';
        switch (this.type) {
          case i.days:
            g = c({
              '-weekend-': this.dp.isWeekend(l),
              '-other-month-': this.isOtherMonth,
              '-disabled-': (this.isOtherMonth && !a) || d || p,
            });
            break;
          case i.months:
            g = c({ '-disabled-': d || p });
            break;
          case i.years:
            g = c({ '-other-decade-': this.isOtherDecade, '-disabled-': d || (this.isOtherDecade && !n) || p });
        }
        return c(m, g, null === (t = this.customData) || void 0 === t ? void 0 : t.classes);
      }
      _getHtml() {
        var e;
        let { year: t, month: s, date: a } = o(this.date),
          { showOtherMonths: n, showOtherYears: r } = this.opts;
        if (null !== (e = this.customData) && void 0 !== e && e.html) return this.customData.html;
        switch (this.type) {
          case i.days:
            return !n && this.isOtherMonth ? '' : a;
          case i.months:
            return this.dp.locale[this.opts.monthsField][s];
          case i.years:
            return !r && this.isOtherDecade ? '' : t;
        }
      }
      _isOutOfMinMaxRange() {
        let { minDate: e, maxDate: t } = this.dp,
          { type: s, date: a } = this,
          { month: n, year: r, date: h } = o(a),
          l = s === i.days,
          d = s === i.years,
          c = !!e && new Date(r, d ? e.getMonth() : n, l ? h : e.getDate()),
          u = !!t && new Date(r, d ? t.getMonth() : n, l ? h : t.getDate());
        return e && t ? m(c, e) || p(u, t) : e ? m(c, e) : t ? p(u, t) : void 0;
      }
      destroy() {
        this.unbindDatepickerEvents();
      }
      _handleRangeStatus() {
        let { rangeDateFrom: e, rangeDateTo: t } = this.dp,
          i = c({
            '-in-range-': e && t && ((s = this.date), (a = e), (n = t), p(s, a) && m(s, n)),
            '-range-from-': e && u(this.date, e, this.type),
            '-range-to-': t && u(this.date, t, this.type),
          });
        var s, a, n;
        this.$cell.classList.remove('-range-from-', '-range-to-', '-in-range-'),
          i && this.$cell.classList.add(...i.split(' '));
      }
      _handleSelectedStatus() {
        let e = this.dp._checkIfDateIsSelected(this.date, this.type);
        e ? this.select() : !e && this.selected && this.removeSelect();
      }
      _handleInitialFocusStatus() {
        u(this.dp.focusDate, this.date, this.type) && this.focus();
      }
      get isDisabled() {
        return this.$cell.matches('.-disabled-');
      }
      get isOtherMonth() {
        return this.dp.isOtherMonth(this.date);
      }
      get isOtherDecade() {
        return this.dp.isOtherDecade(this.date);
      }
    }
    function M(e, t, i) {
      return (
        t in e ? Object.defineProperty(e, t, { value: i, enumerable: !0, configurable: !0, writable: !0 }) : (e[t] = i),
        e
      );
    }
    let $ = {
      [i.days]:
        '<div class="air-datepicker-body--day-names"></div>' +
        '<div class="air-datepicker-body--cells -'.concat(i.days, '-"></div>'),
      [i.months]: '<div class="air-datepicker-body--cells -'.concat(i.months, '-"></div>'),
      [i.years]: '<div class="air-datepicker-body--cells -'.concat(i.years, '-"></div>'),
    };
    class S {
      constructor(e) {
        let { dp: t, type: s, opts: a } = e;
        M(this, 'handleClick', (e) => {
          let t = e.target.adpCell;
          if (t.isDisabled) return;
          if (!this.dp.isMinViewReached) return void this.dp.down();
          let i = this.dp._checkIfDateIsSelected(t.date, t.type);
          i ? this.dp._handleAlreadySelectedDates(i, t.date) : this.dp.selectDate(t.date);
        }),
          M(this, 'handleDayNameClick', (e) => {
            let t = e.target.getAttribute('data-day-index');
            this.opts.onClickDayName({ dayIndex: Number(t), datepicker: this.dp });
          }),
          M(this, 'onChangeCurrentView', (e) => {
            e !== this.type ? this.hide() : (this.show(), this.render());
          }),
          M(this, 'onMouseOverCell', (e) => {
            let t = y(e.target, '.air-datepicker-cell');
            this.dp.setFocusDate(!!t && t.adpCell.date);
          }),
          M(this, 'onMouseOutCell', () => {
            this.dp.setFocusDate(!1);
          }),
          M(this, 'onClickBody', (e) => {
            let { onClickDayName: t } = this.opts,
              i = e.target;
            i.closest('.air-datepicker-cell') && this.handleClick(e),
              t && i.closest('.air-datepicker-body--day-name') && this.handleDayNameClick(e);
          }),
          M(this, 'onMouseDown', (e) => {
            this.pressed = !0;
            let t = y(e.target, '.air-datepicker-cell'),
              i = t && t.adpCell;
            u(i.date, this.dp.rangeDateFrom) && (this.rangeFromFocused = !0),
              u(i.date, this.dp.rangeDateTo) && (this.rangeToFocused = !0);
          }),
          M(this, 'onMouseMove', (e) => {
            if (!this.pressed || !this.dp.isMinViewReached) return;
            e.preventDefault();
            let t = y(e.target, '.air-datepicker-cell'),
              i = t && t.adpCell,
              { selectedDates: s, rangeDateTo: a, rangeDateFrom: n } = this.dp;
            if (!i || i.isDisabled) return;
            let { date: r } = i;
            if (2 === s.length) {
              if (this.rangeFromFocused && !p(r, a)) {
                let { hours: e, minutes: t } = o(n);
                r.setHours(e), r.setMinutes(t), (this.dp.rangeDateFrom = r), this.dp.replaceDate(n, r);
              }
              if (this.rangeToFocused && !m(r, n)) {
                let { hours: e, minutes: t } = o(a);
                r.setHours(e), r.setMinutes(t), (this.dp.rangeDateTo = r), this.dp.replaceDate(a, r);
              }
            }
          }),
          M(this, 'onMouseUp', () => {
            (this.pressed = !1), (this.rangeFromFocused = !1), (this.rangeToFocused = !1);
          }),
          M(this, 'onChangeViewDate', (e, t) => {
            if (!this.isVisible) return;
            let s = d(e),
              a = d(t);
            switch (this.dp.currentView) {
              case i.days:
                if (u(e, t, i.months)) return;
                break;
              case i.months:
                if (u(e, t, i.years)) return;
                break;
              case i.years:
                if (s[0] === a[0] && s[1] === a[1]) return;
            }
            this.render();
          }),
          M(this, 'render', () => {
            this.destroyCells(),
              this._generateCells(),
              this.cells.forEach((e) => {
                this.$cells.appendChild(e.render());
              });
          }),
          (this.dp = t),
          (this.type = s),
          (this.opts = a),
          (this.cells = []),
          (this.$el = ''),
          (this.pressed = !1),
          (this.isVisible = !0),
          this.init();
      }
      init() {
        this._buildBaseHtml(),
          this.type === i.days && this.renderDayNames(),
          this.render(),
          this._bindEvents(),
          this._bindDatepickerEvents();
      }
      _bindEvents() {
        let { range: e, dynamicRange: t } = this.opts;
        v(this.$el, 'mouseover', this.onMouseOverCell),
          v(this.$el, 'mouseout', this.onMouseOutCell),
          v(this.$el, 'click', this.onClickBody),
          e &&
            t &&
            (v(this.$el, 'mousedown', this.onMouseDown),
            v(this.$el, 'mousemove', this.onMouseMove),
            v(window.document, 'mouseup', this.onMouseUp));
      }
      _bindDatepickerEvents() {
        this.dp.on(i.eventChangeViewDate, this.onChangeViewDate),
          this.dp.on(i.eventChangeCurrentView, this.onChangeCurrentView);
      }
      _buildBaseHtml() {
        (this.$el = n({ className: 'air-datepicker-body -'.concat(this.type, '-'), innerHtml: $[this.type] })),
          (this.$names = a('.air-datepicker-body--day-names', this.$el)),
          (this.$cells = a('.air-datepicker-body--cells', this.$el));
      }
      _getDayNamesHtml() {
        let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.dp.locale.firstDay,
          t = '',
          s = this.dp.isWeekend,
          { onClickDayName: a } = this.opts,
          n = e,
          r = 0;
        for (; r < 7; ) {
          let e = n % 7,
            h = c('air-datepicker-body--day-name', { [i.cssClassWeekend]: s(e), '-clickable-': !!a }),
            o = this.dp.locale.daysMin[e];
          (t += '<div class="'.concat(h, '" data-day-index=\'').concat(e, "'>").concat(o, '</div>')), r++, n++;
        }
        return t;
      }
      _getDaysCells() {
        let {
            viewDate: e,
            locale: { firstDay: t },
          } = this.dp,
          i = h(e),
          { year: s, month: a } = o(e),
          n = new Date(s, a, 1),
          r = new Date(s, a, i),
          l = n.getDay() - t,
          d = 6 - r.getDay() + t;
        (l = l < 0 ? l + 7 : l), (d = d > 6 ? d - 7 : d);
        let c = (function (e, t) {
            let { year: i, month: s, date: a } = o(e);
            return new Date(i, s, a - t);
          })(n, l),
          u = i + l + d,
          p = c.getDate(),
          { year: m, month: g } = o(c),
          D = 0;
        for (; D < u; ) {
          let e = new Date(m, g, p + D);
          this._generateCell(e), D++;
        }
      }
      _generateCell(e) {
        let { type: t, dp: i, opts: s } = this,
          a = new _({ type: t, dp: i, opts: s, date: e, body: this });
        return this.cells.push(a), a;
      }
      _generateDayCells() {
        this._getDaysCells();
      }
      _generateMonthCells() {
        let { year: e } = this.dp.parsedViewDate,
          t = 0;
        for (; t < 12; ) this.cells.push(this._generateCell(new Date(e, t))), t++;
      }
      _generateYearCells() {
        let e = d(this.dp.viewDate),
          t = e[0] - 1,
          i = e[1] + 1,
          s = t;
        for (; s <= i; ) this.cells.push(this._generateCell(new Date(s, 0))), s++;
      }
      renderDayNames() {
        this.$names.innerHTML = this._getDayNamesHtml();
      }
      _generateCells() {
        switch (this.type) {
          case i.days:
            this._generateDayCells();
            break;
          case i.months:
            this._generateMonthCells();
            break;
          case i.years:
            this._generateYearCells();
        }
      }
      show() {
        (this.isVisible = !0), this.$el.classList.remove('-hidden-');
      }
      hide() {
        (this.isVisible = !1), this.$el.classList.add('-hidden-');
      }
      destroyCells() {
        this.cells.forEach((e) => e.destroy()), (this.cells = []), (this.$cells.innerHTML = '');
      }
      destroy() {
        this.destroyCells(),
          this.dp.off(i.eventChangeViewDate, this.onChangeViewDate),
          this.dp.off(i.eventChangeCurrentView, this.onChangeCurrentView);
      }
    }
    function T(e, t, i) {
      return (
        t in e ? Object.defineProperty(e, t, { value: i, enumerable: !0, configurable: !0, writable: !0 }) : (e[t] = i),
        e
      );
    }
    class F {
      constructor(e) {
        let { dp: t, opts: i } = e;
        T(this, 'onClickNav', (e) => {
          let t = y(e.target, '.air-datepicker-nav--action');
          if (!t) return;
          let i = t.dataset.action;
          this.dp[i]();
        }),
          T(this, 'onChangeViewDate', () => {
            this.render(), this._resetNavStatus(), this.handleNavStatus();
          }),
          T(this, 'onChangeCurrentView', () => {
            this.render(), this._resetNavStatus(), this.handleNavStatus();
          }),
          T(this, 'onClickNavTitle', () => {
            this.dp.isFinalView || this.dp.up();
          }),
          T(this, 'update', () => {
            let { prevHtml: e, nextHtml: t } = this.opts;
            (this.$prev.innerHTML = e),
              (this.$next.innerHTML = t),
              this._resetNavStatus(),
              this.render(),
              this.handleNavStatus();
          }),
          T(this, 'renderDelay', () => {
            setTimeout(this.render);
          }),
          T(this, 'render', () => {
            (this.$title.innerHTML = this._getTitle()),
              (function (e, t) {
                for (let i in t) t[i] ? e.classList.add(i) : e.classList.remove(i);
              })(this.$title, { '-disabled-': this.dp.isFinalView });
          }),
          (this.dp = t),
          (this.opts = i),
          this.init();
      }
      init() {
        this._createElement(),
          this._buildBaseHtml(),
          this._defineDOM(),
          this.render(),
          this.handleNavStatus(),
          this._bindEvents(),
          this._bindDatepickerEvents();
      }
      _defineDOM() {
        (this.$title = a('.air-datepicker-nav--title', this.$el)),
          (this.$prev = a('[data-action="prev"]', this.$el)),
          (this.$next = a('[data-action="next"]', this.$el));
      }
      _bindEvents() {
        this.$el.addEventListener('click', this.onClickNav),
          this.$title.addEventListener('click', this.onClickNavTitle);
      }
      _bindDatepickerEvents() {
        this.dp.on(i.eventChangeViewDate, this.onChangeViewDate),
          this.dp.on(i.eventChangeCurrentView, this.onChangeCurrentView),
          this.isNavIsFunction &&
            (this.dp.on(i.eventChangeSelectedDate, this.renderDelay),
            this.dp.opts.timepicker && this.dp.on(i.eventChangeTime, this.render));
      }
      destroy() {
        this.dp.off(i.eventChangeViewDate, this.onChangeViewDate),
          this.dp.off(i.eventChangeCurrentView, this.onChangeCurrentView),
          this.isNavIsFunction &&
            (this.dp.off(i.eventChangeSelectedDate, this.renderDelay),
            this.dp.opts.timepicker && this.dp.off(i.eventChangeTime, this.render));
      }
      _createElement() {
        this.$el = n({ tagName: 'nav', className: 'air-datepicker-nav' });
      }
      _getTitle() {
        let { dp: e, opts: t } = this,
          i = t.navTitles[e.currentView];
        return 'function' == typeof i ? i(e) : e.formatDate(e.viewDate, i);
      }
      handleNavStatus() {
        let { disableNavWhenOutOfRange: e } = this.opts,
          { minDate: t, maxDate: s } = this.dp;
        if ((!t && !s) || !e) return;
        let { year: a, month: n } = this.dp.parsedViewDate,
          r = !!t && o(t),
          h = !!s && o(s);
        switch (this.dp.currentView) {
          case i.days:
            t && r.month >= n && r.year >= a && this._disableNav('prev'),
              s && h.month <= n && h.year <= a && this._disableNav('next');
            break;
          case i.months:
            t && r.year >= a && this._disableNav('prev'), s && h.year <= a && this._disableNav('next');
            break;
          case i.years: {
            let e = d(this.dp.viewDate);
            t && r.year >= e[0] && this._disableNav('prev'), s && h.year <= e[1] && this._disableNav('next');
            break;
          }
        }
      }
      _disableNav(e) {
        a('[data-action="' + e + '"]', this.$el).classList.add('-disabled-');
      }
      _resetNavStatus() {
        !(function (e) {
          for (var t = arguments.length, i = new Array(t > 1 ? t - 1 : 0), s = 1; s < t; s++) i[s - 1] = arguments[s];
          e.length
            ? e.forEach((e) => {
                e.classList.remove(...i);
              })
            : e.classList.remove(...i);
        })(this.$el.querySelectorAll('.air-datepicker-nav--action'), '-disabled-');
      }
      _buildBaseHtml() {
        let { prevHtml: e, nextHtml: t } = this.opts;
        this.$el.innerHTML =
          '<div class="air-datepicker-nav--action" data-action="prev">'.concat(e, '</div>') +
          '<div class="air-datepicker-nav--title"></div>' +
          '<div class="air-datepicker-nav--action" data-action="next">'.concat(t, '</div>');
      }
      get isNavIsFunction() {
        let { navTitles: e } = this.opts;
        return Object.keys(e).find((t) => 'function' == typeof e[t]);
      }
    }
    var V = {
      today: { content: (e) => e.locale.today, onClick: (e) => e.setViewDate(new Date()) },
      clear: { content: (e) => e.locale.clear, onClick: (e) => e.clear() },
    };
    class x {
      constructor(e) {
        let { dp: t, opts: i } = e;
        (this.dp = t), (this.opts = i), this.init();
      }
      init() {
        this.createElement(), this.render();
      }
      createElement() {
        this.$el = n({ className: 'air-datepicker-buttons' });
      }
      destroy() {
        this.$el.parentNode.removeChild(this.$el);
      }
      clearHtml() {
        return (this.$el.innerHTML = ''), this;
      }
      generateButtons() {
        let { buttons: e } = this.opts;
        Array.isArray(e) || (e = [e]),
          e.forEach((e) => {
            let t = e;
            'string' == typeof e && V[e] && (t = V[e]);
            let i = this.createButton(t);
            t.onClick && this.attachEventToButton(i, t.onClick), this.$el.appendChild(i);
          });
      }
      attachEventToButton(e, t) {
        e.addEventListener('click', () => {
          t(this.dp);
        });
      }
      createButton(e) {
        let { content: t, className: i, tagName: s = 'button', attrs: a = {} } = e,
          r = 'function' == typeof t ? t(this.dp) : t;
        return n({
          tagName: s,
          innerHtml: "<span tabindex='-1'>".concat(r, '</span>'),
          className: c('air-datepicker-button', i),
          attrs: a,
        });
      }
      render() {
        this.generateButtons();
      }
    }
    function H(e, t, i) {
      return (
        t in e ? Object.defineProperty(e, t, { value: i, enumerable: !0, configurable: !0, writable: !0 }) : (e[t] = i),
        e
      );
    }
    class L {
      constructor() {
        let { opts: e, dp: t } = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        H(this, 'toggleTimepickerIsActive', (e) => {
          this.dp.timepickerIsActive = e;
        }),
          H(this, 'onChangeSelectedDate', (e) => {
            let { date: t, updateTime: i = !1 } = e;
            t && (this.setMinMaxTime(t), this.setCurrentTime(!!i && t), this.addTimeToDate(t));
          }),
          H(this, 'onChangeLastSelectedDate', (e) => {
            e && (this.setTime(e), this.render());
          }),
          H(this, 'onChangeInputRange', (e) => {
            let t = e.target;
            (this[t.getAttribute('name')] = t.value),
              this.updateText(),
              this.dp.trigger(i.eventChangeTime, { hours: this.hours, minutes: this.minutes });
          }),
          H(this, 'onMouseEnterLeave', (e) => {
            let t = e.target.getAttribute('name'),
              i = this.$minutesText;
            'hours' === t && (i = this.$hoursText), i.classList.toggle('-focus-');
          }),
          H(this, 'onFocus', () => {
            this.toggleTimepickerIsActive(!0);
          }),
          H(this, 'onBlur', () => {
            this.toggleTimepickerIsActive(!1);
          }),
          (this.opts = e),
          (this.dp = t);
        let { timeFormat: s } = this.dp.locale;
        s && (s.match(k('h')) || s.match(k('hh'))) && (this.ampm = !0), this.init();
      }
      init() {
        this.setTime(this.dp.lastSelectedDate || this.dp.viewDate),
          this.createElement(),
          this.buildHtml(),
          this.defineDOM(),
          this.render(),
          this.bindDatepickerEvents(),
          this.bindDOMEvents();
      }
      bindDatepickerEvents() {
        this.dp.on(i.eventChangeSelectedDate, this.onChangeSelectedDate),
          this.dp.on(i.eventChangeLastSelectedDate, this.onChangeLastSelectedDate);
      }
      bindDOMEvents() {
        let e = 'input';
        navigator.userAgent.match(/trident/gi) && (e = 'change'),
          v(this.$ranges, e, this.onChangeInputRange),
          v(this.$ranges, 'mouseenter', this.onMouseEnterLeave),
          v(this.$ranges, 'mouseleave', this.onMouseEnterLeave),
          v(this.$ranges, 'focus', this.onFocus),
          v(this.$ranges, 'mousedown', this.onFocus),
          v(this.$ranges, 'blur', this.onBlur);
      }
      createElement() {
        this.$el = n({ className: c('air-datepicker-time', { '-am-pm-': this.dp.ampm }) });
      }
      destroy() {
        this.dp.off(i.eventChangeSelectedDate, this.onChangeSelectedDate),
          this.dp.off(i.eventChangeLastSelectedDate, this.onChangeLastSelectedDate),
          this.$el.parentNode.removeChild(this.$el);
      }
      buildHtml() {
        let {
          ampm: e,
          hours: t,
          displayHours: i,
          minutes: s,
          minHours: a,
          minMinutes: n,
          maxHours: r,
          maxMinutes: h,
          dayPeriod: o,
          opts: { hoursStep: d, minutesStep: c },
        } = this;
        this.$el.innerHTML =
          '<div class="air-datepicker-time--current">' +
          '   <span class="air-datepicker-time--current-hours">'.concat(l(i), '</span>') +
          '   <span class="air-datepicker-time--current-colon">:</span>' +
          '   <span class="air-datepicker-time--current-minutes">'.concat(l(s), '</span>') +
          '   '.concat(e ? "<span class='air-datepicker-time--current-ampm'>".concat(o, '</span>') : '') +
          '</div><div class="air-datepicker-time--sliders">   <div class="air-datepicker-time--row">' +
          '      <input type="range" name="hours" value="'
            .concat(t, '" min="')
            .concat(a, '" max="')
            .concat(r, '" step="')
            .concat(d, '"/>') +
          '   </div>   <div class="air-datepicker-time--row">' +
          '      <input type="range" name="minutes" value="'
            .concat(s, '" min="')
            .concat(n, '" max="')
            .concat(h, '" step="')
            .concat(c, '"/>') +
          '   </div></div>';
      }
      defineDOM() {
        let e = (e) => a(e, this.$el);
        (this.$ranges = this.$el.querySelectorAll('[type="range"]')),
          (this.$hours = e('[name="hours"]')),
          (this.$minutes = e('[name="minutes"]')),
          (this.$hoursText = e('.air-datepicker-time--current-hours')),
          (this.$minutesText = e('.air-datepicker-time--current-minutes')),
          (this.$ampm = e('.air-datepicker-time--current-ampm'));
      }
      setTime(e) {
        this.setMinMaxTime(e), this.setCurrentTime(e);
      }
      addTimeToDate(e) {
        e && (e.setHours(this.hours), e.setMinutes(this.minutes));
      }
      setMinMaxTime(e) {
        if ((this.setMinMaxTimeFromOptions(), e)) {
          let { minDate: t, maxDate: i } = this.dp;
          t && u(e, t) && this.setMinTimeFromMinDate(t), i && u(e, i) && this.setMaxTimeFromMaxDate(i);
        }
      }
      setCurrentTime(e) {
        let { hours: t, minutes: i } = e ? o(e) : this;
        (this.hours = f(t, this.minHours, this.maxHours)), (this.minutes = f(i, this.minMinutes, this.maxMinutes));
      }
      setMinMaxTimeFromOptions() {
        let { minHours: e, minMinutes: t, maxHours: i, maxMinutes: s } = this.opts;
        (this.minHours = f(e, 0, 23)),
          (this.minMinutes = f(t, 0, 59)),
          (this.maxHours = f(i, 0, 23)),
          (this.maxMinutes = f(s, 0, 59));
      }
      setMinTimeFromMinDate(e) {
        let { lastSelectedDate: t } = this.dp;
        (this.minHours = e.getHours()),
          t && t.getHours() > e.getHours()
            ? (this.minMinutes = this.opts.minMinutes)
            : (this.minMinutes = e.getMinutes());
      }
      setMaxTimeFromMaxDate(e) {
        let { lastSelectedDate: t } = this.dp;
        (this.maxHours = e.getHours()),
          t && t.getHours() < e.getHours()
            ? (this.maxMinutes = this.opts.maxMinutes)
            : (this.maxMinutes = e.getMinutes());
      }
      getDayPeriod(e, t) {
        let i = e,
          s = Number(e);
        e instanceof Date && ((i = o(e)), (s = Number(i.hours)));
        let a = 'am';
        if (t || this.ampm) {
          switch (!0) {
            case 12 === s:
            case s > 11:
              a = 'pm';
          }
          s = s % 12 == 0 ? 12 : s % 12;
        }
        return { hours: s, dayPeriod: a };
      }
      updateSliders() {
        (r(this.$hours, { min: this.minHours, max: this.maxHours }).value = this.hours),
          (r(this.$minutes, { min: this.minMinutes, max: this.maxMinutes }).value = this.minutes);
      }
      updateText() {
        (this.$hoursText.innerHTML = l(this.displayHours)),
          (this.$minutesText.innerHTML = l(this.minutes)),
          this.ampm && (this.$ampm.innerHTML = this.dayPeriod);
      }
      set hours(e) {
        this._hours = e;
        let { hours: t, dayPeriod: i } = this.getDayPeriod(e);
        (this.displayHours = t), (this.dayPeriod = i);
      }
      get hours() {
        return this._hours;
      }
      render() {
        this.updateSliders(), this.updateText();
      }
    }
    function O(e, t, i) {
      return (
        t in e ? Object.defineProperty(e, t, { value: i, enumerable: !0, configurable: !0, writable: !0 }) : (e[t] = i),
        e
      );
    }
    class A {
      constructor(e) {
        let { dp: t, opts: i } = e;
        O(this, 'pressedKeys', new Set()),
          O(
            this,
            'hotKeys',
            new Map([
              [
                [
                  ['Control', 'ArrowRight'],
                  ['Control', 'ArrowUp'],
                ],
                (e) => e.month++,
              ],
              [
                [
                  ['Control', 'ArrowLeft'],
                  ['Control', 'ArrowDown'],
                ],
                (e) => e.month--,
              ],
              [
                [
                  ['Shift', 'ArrowRight'],
                  ['Shift', 'ArrowUp'],
                ],
                (e) => e.year++,
              ],
              [
                [
                  ['Shift', 'ArrowLeft'],
                  ['Shift', 'ArrowDown'],
                ],
                (e) => e.year--,
              ],
              [
                [
                  ['Alt', 'ArrowRight'],
                  ['Alt', 'ArrowUp'],
                ],
                (e) => (e.year += 10),
              ],
              [
                [
                  ['Alt', 'ArrowLeft'],
                  ['Alt', 'ArrowDown'],
                ],
                (e) => (e.year -= 10),
              ],
              [['Control', 'Shift', 'ArrowUp'], (e, t) => t.up()],
            ]),
          ),
          O(this, 'handleHotKey', (e) => {
            let t = this.hotKeys.get(e),
              i = o(this.getInitialFocusDate());
            t(i, this.dp);
            let { year: s, month: a, date: n } = i,
              r = h(new Date(s, a));
            r < n && (n = r);
            let l = this.dp.getClampedDate(new Date(s, a, n));
            this.dp.setFocusDate(l, { viewDateTransition: !0 });
          }),
          O(this, 'isHotKeyPressed', () => {
            let e = !1,
              t = this.pressedKeys.size,
              i = (e) => this.pressedKeys.has(e);
            for (let [s] of this.hotKeys) {
              if (e) break;
              if (Array.isArray(s[0]))
                s.forEach((a) => {
                  e || t !== a.length || (e = a.every(i) && s);
                });
              else {
                if (t !== s.length) continue;
                e = s.every(i) && s;
              }
            }
            return e;
          }),
          O(this, 'isArrow', (e) => e >= 37 && e <= 40),
          O(this, 'onKeyDown', (e) => {
            let { key: t, which: i } = e,
              {
                dp: s,
                dp: { focusDate: a },
                opts: n,
              } = this;
            this.registerKey(t);
            let r = this.isHotKeyPressed();
            if (r) return e.preventDefault(), void this.handleHotKey(r);
            if (this.isArrow(i)) return e.preventDefault(), void this.focusNextCell(t);
            if ('Enter' === t) {
              if (s.currentView !== n.minView) return void s.down();
              if (a) {
                let e = s._checkIfDateIsSelected(a);
                return void (e ? s._handleAlreadySelectedDates(e, a) : s.selectDate(a));
              }
            }
            'Escape' === t && this.dp.hide();
          }),
          O(this, 'onKeyUp', (e) => {
            this.removeKey(e.key);
          }),
          (this.dp = t),
          (this.opts = i),
          this.init();
      }
      init() {
        this.bindKeyboardEvents();
      }
      bindKeyboardEvents() {
        let { $el: e } = this.dp;
        e.addEventListener('keydown', this.onKeyDown), e.addEventListener('keyup', this.onKeyUp);
      }
      destroy() {
        let { $el: e } = this.dp;
        e.removeEventListener('keydown', this.onKeyDown),
          e.removeEventListener('keyup', this.onKeyUp),
          (this.hotKeys = null),
          (this.pressedKeys = null);
      }
      getInitialFocusDate() {
        let {
            focusDate: e,
            currentView: t,
            selectedDates: s,
            parsedViewDate: { year: a, month: n },
          } = this.dp,
          r = e || s[s.length - 1];
        if (!r)
          switch (t) {
            case i.days:
              r = new Date(a, n, new Date().getDate());
              break;
            case i.months:
              r = new Date(a, n, 1);
              break;
            case i.years:
              r = new Date(a, 0, 1);
          }
        return r;
      }
      focusNextCell(e) {
        let t = this.getInitialFocusDate(),
          { currentView: s } = this.dp,
          { days: a, months: n, years: r } = i,
          h = o(t),
          l = h.year,
          d = h.month,
          c = h.date;
        switch (e) {
          case 'ArrowLeft':
            s === a && (c -= 1), s === n && (d -= 1), s === r && (l -= 1);
            break;
          case 'ArrowUp':
            s === a && (c -= 7), s === n && (d -= 3), s === r && (l -= 4);
            break;
          case 'ArrowRight':
            s === a && (c += 1), s === n && (d += 1), s === r && (l += 1);
            break;
          case 'ArrowDown':
            s === a && (c += 7), s === n && (d += 3), s === r && (l += 4);
        }
        let u = this.dp.getClampedDate(new Date(l, d, c));
        this.dp.setFocusDate(u, { viewDateTransition: !0 });
      }
      registerKey(e) {
        this.pressedKeys.add(e);
      }
      removeKey(e) {
        this.pressedKeys.delete(e);
      }
    }
    let E = {
      on(e, t) {
        this.__events || (this.__events = {}), this.__events[e] ? this.__events[e].push(t) : (this.__events[e] = [t]);
      },
      off(e, t) {
        this.__events && this.__events[e] && (this.__events[e] = this.__events[e].filter((e) => e !== t));
      },
      removeAllEvents() {
        this.__events = {};
      },
      trigger(e) {
        for (var t = arguments.length, i = new Array(t > 1 ? t - 1 : 0), s = 1; s < t; s++) i[s - 1] = arguments[s];
        this.__events &&
          this.__events[e] &&
          this.__events[e].forEach((e) => {
            e(...i);
          });
      },
    };
    function N(e, t, i) {
      return (
        t in e ? Object.defineProperty(e, t, { value: i, enumerable: !0, configurable: !0, writable: !0 }) : (e[t] = i),
        e
      );
    }
    let I = '',
      R = '',
      P = '',
      B = !1;
    class K {
      constructor(e, t) {
        var r = this;
        if (
          (N(this, 'viewIndexes', [i.days, i.months, i.years]),
          N(this, 'next', () => {
            let { year: e, month: t } = this.parsedViewDate;
            switch (this.currentView) {
              case i.days:
                this.setViewDate(new Date(e, t + 1, 1));
                break;
              case i.months:
                this.setViewDate(new Date(e + 1, t, 1));
                break;
              case i.years:
                this.setViewDate(new Date(e + 10, 0, 1));
            }
          }),
          N(this, 'prev', () => {
            let { year: e, month: t } = this.parsedViewDate;
            switch (this.currentView) {
              case i.days:
                this.setViewDate(new Date(e, t - 1, 1));
                break;
              case i.months:
                this.setViewDate(new Date(e - 1, t, 1));
                break;
              case i.years:
                this.setViewDate(new Date(e - 10, 0, 1));
            }
          }),
          N(this, '_finishHide', () => {
            (this.hideAnimation = !1), this._destroyComponents(), this.$container.removeChild(this.$datepicker);
          }),
          N(this, 'setPosition', function (e) {
            let t = arguments.length > 1 && void 0 !== arguments[1] && arguments[1];
            if ('function' == typeof (e = e || r.opts.position))
              return void (r.customHide = e({
                $datepicker: r.$datepicker,
                $target: r.$el,
                $pointer: r.$pointer,
                isViewChange: t,
                done: r._finishHide,
              }));
            let i,
              s,
              { isMobile: a } = r.opts,
              n = r.$el.getBoundingClientRect(),
              h = r.$el.getBoundingClientRect(),
              o = r.$datepicker.offsetParent,
              l = r.$el.offsetParent,
              d = r.$datepicker.getBoundingClientRect(),
              c = e.split(' '),
              u = window.scrollY,
              p = window.scrollX,
              m = r.opts.offset,
              g = c[0],
              D = c[1];
            if (a) r.$datepicker.style.cssText = 'left: 50%; top: 50%';
            else {
              if (
                (o === l &&
                  o !== document.body &&
                  ((h = { top: r.$el.offsetTop, left: r.$el.offsetLeft, width: n.width, height: r.$el.offsetHeight }),
                  (u = 0),
                  (p = 0)),
                o !== l && o !== document.body)
              ) {
                let e = o.getBoundingClientRect();
                (h = { top: n.top - e.top, left: n.left - e.left, width: n.width, height: n.height }), (u = 0), (p = 0);
              }
              switch (g) {
                case 'top':
                  i = h.top - d.height - m;
                  break;
                case 'right':
                  s = h.left + h.width + m;
                  break;
                case 'bottom':
                  i = h.top + h.height + m;
                  break;
                case 'left':
                  s = h.left - d.width - m;
              }
              switch (D) {
                case 'top':
                  i = h.top;
                  break;
                case 'right':
                  s = h.left + h.width - d.width;
                  break;
                case 'bottom':
                  i = h.top + h.height - d.height;
                  break;
                case 'left':
                  s = h.left;
                  break;
                case 'center':
                  /left|right/.test(g)
                    ? (i = h.top + h.height / 2 - d.height / 2)
                    : (s = h.left + h.width / 2 - d.width / 2);
              }
              r.$datepicker.style.cssText = 'left: '.concat(s + p, 'px; top: ').concat(i + u, 'px');
            }
          }),
          N(this, '_setInputValue', () => {
            let {
                opts: e,
                $altField: t,
                locale: { dateFormat: i },
              } = this,
              { altFieldDateFormat: s, altField: a } = e;
            a && t && (t.value = this._getInputValue(s)), (this.$el.value = this._getInputValue(i));
          }),
          N(this, '_getInputValue', (e) => {
            let { selectedDates: t, opts: i } = this,
              { multipleDates: s, multipleDatesSeparator: a } = i;
            if (!t.length) return '';
            let n = 'function' == typeof e,
              r = n ? e(s ? t : t[0]) : t.map((t) => this.formatDate(t, e));
            return (r = n ? r : r.join(a)), r;
          }),
          N(this, '_checkIfDateIsSelected', function (e) {
            let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : i.days,
              s = !1;
            return (
              r.selectedDates.some((i) => {
                let a = u(e, i, t);
                return (s = a && i), a;
              }),
              s
            );
          }),
          N(this, '_scheduleCallAfterTransition', (e) => {
            this._cancelScheduledCall(),
              e && e(!1),
              (this._onTransitionEnd = () => {
                e && e(!0);
              }),
              this.$datepicker.addEventListener('transitionend', this._onTransitionEnd, { once: !0 });
          }),
          N(this, '_cancelScheduledCall', () => {
            this.$datepicker.removeEventListener('transitionend', this._onTransitionEnd);
          }),
          N(this, 'setViewDate', (e) => {
            if (!((e = b(e)) instanceof Date)) return;
            if (u(e, this.viewDate)) return;
            let t = this.viewDate;
            this.viewDate = e;
            let { onChangeViewDate: s } = this.opts;
            if (s) {
              let { month: e, year: t } = this.parsedViewDate;
              s({ month: e, year: t, decade: this.curDecade });
            }
            this.trigger(i.eventChangeViewDate, e, t);
          }),
          N(this, 'setFocusDate', function (e) {
            let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
            (!e || (e = b(e)) instanceof Date) &&
              ((r.focusDate = e),
              r.opts.range && e && r._handleRangeOnFocus(),
              r.trigger(i.eventChangeFocusDate, e, t));
          }),
          N(this, 'setCurrentView', (e) => {
            if (this.viewIndexes.includes(e)) {
              if (
                ((this.currentView = e),
                this.elIsInput && this.visible && this.setPosition(void 0, !0),
                this.trigger(i.eventChangeCurrentView, e),
                !this.views[e])
              ) {
                let t = (this.views[e] = new S({ dp: this, opts: this.opts, type: e }));
                this.shouldUpdateDOM && this.$content.appendChild(t.$el);
              }
              this.opts.onChangeView && this.opts.onChangeView(e);
            }
          }),
          N(this, '_updateLastSelectedDate', (e) => {
            (this.lastSelectedDate = e), this.trigger(i.eventChangeLastSelectedDate, e);
          }),
          N(this, 'destroy', () => {
            let { showEvent: e, isMobile: t } = this.opts,
              i = this.$datepicker.parentNode;
            i && i.removeChild(this.$datepicker),
              this.$el.removeEventListener(e, this._onFocus),
              this.$el.removeEventListener('blur', this._onBlur),
              window.removeEventListener('resize', this._onResize),
              t && this._removeMobileAttributes(),
              this.keyboardNav && this.keyboardNav.destroy(),
              (this.views = null),
              (this.nav = null),
              (this.$datepicker = null),
              (this.opts = null),
              (this.$customContainer = null),
              (this.viewDate = null),
              (this.focusDate = null),
              (this.selectedDates = null),
              (this.rangeDateFrom = null),
              (this.rangeDateTo = null);
          }),
          N(this, 'update', (e) => {
            let t = w({}, this.opts);
            w(this.opts, e);
            let { timepicker: s, buttons: a, range: n, selectedDates: r, isMobile: h } = this.opts,
              o = this.visible || this.treatAsInline;
            this._createMinMaxDates(),
              this._limitViewDateByMaxMinDates(),
              this._handleLocale(),
              !t.selectedDates && r && this.selectDate(r),
              e.view && this.setCurrentView(e.view),
              this._setInputValue(),
              t.range && !n
                ? ((this.rangeDateTo = !1), (this.rangeDateFrom = !1))
                : !t.range &&
                  n &&
                  this.selectedDates.length &&
                  ((this.rangeDateFrom = this.selectedDates[0]), (this.rangeDateTo = this.selectedDates[1])),
              t.timepicker && !s
                ? (o && this.timepicker.destroy(),
                  (this.timepicker = !1),
                  this.$timepicker.parentNode.removeChild(this.$timepicker))
                : !t.timepicker && s && this._addTimepicker(),
              !t.buttons && a
                ? this._addButtons()
                : t.buttons && !a
                ? (this.buttons.destroy(), this.$buttons.parentNode.removeChild(this.$buttons))
                : o && t.buttons && a && this.buttons.clearHtml().render(),
              !t.isMobile && h
                ? (this.treatAsInline || P || this._createMobileOverlay(),
                  this._addMobileAttributes(),
                  this.visible && this._showMobileOverlay())
                : t.isMobile &&
                  !h &&
                  (this._removeMobileAttributes(),
                  this.visible &&
                    (P.classList.remove('-active-'), 'function' != typeof this.opts.position && this.setPosition())),
              o &&
                (this.nav.update(),
                this.views[this.currentView].render(),
                this.currentView === i.days && this.views[this.currentView].renderDayNames());
          }),
          N(this, 'isOtherMonth', (e) => {
            let { month: t } = o(e);
            return t !== this.parsedViewDate.month;
          }),
          N(this, 'isOtherYear', (e) => {
            let { year: t } = o(e);
            return t !== this.parsedViewDate.year;
          }),
          N(this, 'isOtherDecade', (e) => {
            let { year: t } = o(e),
              [i, s] = d(this.viewDate);
            return t < i || t > s;
          }),
          N(this, '_onChangeSelectedDate', (e) => {
            let { silent: t } = e;
            setTimeout(() => {
              this._setInputValue(), this.opts.onSelect && !t && this._triggerOnSelect();
            });
          }),
          N(this, '_onChangeFocusedDate', function (e) {
            let { viewDateTransition: t } = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {};
            if (!e) return;
            let i = !1;
            t && (i = r.isOtherMonth(e) || r.isOtherYear(e) || r.isOtherDecade(e)), i && r.setViewDate(e);
          }),
          N(this, '_onChangeTime', (e) => {
            let { hours: t, minutes: i } = e,
              s = new Date(),
              {
                lastSelectedDate: a,
                opts: { onSelect: n },
              } = this,
              r = a;
            a || (r = s);
            let h = this.getCell(r, this.currentViewSingular),
              o = h && h.adpCell;
            (o && o.isDisabled) ||
              (r.setHours(t),
              r.setMinutes(i),
              a ? (this._setInputValue(), n && this._triggerOnSelect()) : this.selectDate(r));
          }),
          N(this, '_onFocus', (e) => {
            this.visible || this.show();
          }),
          N(this, '_onBlur', (e) => {
            this.inFocus || !this.visible || this.opts.isMobile || this.hide();
          }),
          N(this, '_onMouseDown', (e) => {
            this.inFocus = !0;
          }),
          N(this, '_onMouseUp', (e) => {
            (this.inFocus = !1), this.$el.focus();
          }),
          N(this, '_onResize', () => {
            this.visible && 'function' != typeof this.opts.position && this.setPosition();
          }),
          N(this, '_onClickOverlay', () => {
            this.visible && this.hide();
          }),
          N(this, 'isWeekend', (e) => this.opts.weekends.includes(e)),
          N(this, 'getClampedDate', (e) => {
            let { minDate: t, maxDate: i } = this,
              s = e;
            return i && p(e, i) ? (s = i) : t && m(e, t) && (s = t), s;
          }),
          (this.$el = a(e)),
          !this.$el)
        )
          return;
        (this.$datepicker = n({ className: 'air-datepicker' })),
          (this.opts = w({}, s, t)),
          (this.$customContainer = !!this.opts.container && a(this.opts.container)),
          (this.$altField = a(this.opts.altField || !1)),
          I || (I = a('body'));
        let { view: h, startDate: l } = this.opts;
        l || (this.opts.startDate = new Date()),
          'INPUT' === this.$el.nodeName && (this.elIsInput = !0),
          (this.inited = !1),
          (this.visible = !1),
          (this.viewDate = b(this.opts.startDate)),
          (this.focusDate = !1),
          (this.initialReadonly = this.$el.getAttribute('readonly')),
          (this.customHide = !1),
          (this.currentView = h),
          (this.selectedDates = []),
          (this.views = {}),
          (this.keys = []),
          (this.rangeDateFrom = ''),
          (this.rangeDateTo = ''),
          (this.timepickerIsActive = !1),
          (this.treatAsInline = this.opts.inline || !this.elIsInput),
          this.init();
      }
      init() {
        let {
          opts: e,
          treatAsInline: t,
          opts: { inline: i, isMobile: s, selectedDates: a, keyboardNav: r, onlyTimepicker: h },
        } = this;
        var o;
        B ||
          i ||
          !this.elIsInput ||
          ((B = !0), (R = n({ className: (o = K.defaultContainerId), id: o })), I.appendChild(R)),
          !s || P || t || this._createMobileOverlay(),
          this._handleLocale(),
          this._bindSubEvents(),
          this._createMinMaxDates(),
          this._limitViewDateByMaxMinDates(),
          this.elIsInput && (i || this._bindEvents(), r && !h && (this.keyboardNav = new A({ dp: this, opts: e }))),
          a && this.selectDate(a, { silent: !0 }),
          this.opts.visible && !t && this.show(),
          s && !t && this.$el.setAttribute('readonly', !0),
          t && this._createComponents();
      }
      _createMobileOverlay() {
        (P = n({ className: 'air-datepicker-overlay' })), R.appendChild(P);
      }
      _createComponents() {
        let {
          opts: e,
          treatAsInline: t,
          opts: { inline: i, buttons: s, timepicker: a, position: n, classes: r, onlyTimepicker: h, isMobile: o },
        } = this;
        this._buildBaseHtml(),
          this.elIsInput && (i || this._setPositionClasses(n)),
          (!i && this.elIsInput) || this.$datepicker.classList.add('-inline-'),
          r && this.$datepicker.classList.add(...r.split(' ')),
          h && this.$datepicker.classList.add('-only-timepicker-'),
          o && !t && this._addMobileAttributes(),
          (this.views[this.currentView] = new S({ dp: this, type: this.currentView, opts: e })),
          (this.nav = new F({ dp: this, opts: e })),
          a && this._addTimepicker(),
          s && this._addButtons(),
          this.$content.appendChild(this.views[this.currentView].$el),
          this.$nav.appendChild(this.nav.$el);
      }
      _destroyComponents() {
        for (let e in this.views) this.views[e].destroy();
        (this.views = {}), this.nav.destroy(), this.timepicker && this.timepicker.destroy();
      }
      _addMobileAttributes() {
        P.addEventListener('click', this._onClickOverlay),
          this.$datepicker.classList.add('-is-mobile-'),
          this.$el.setAttribute('readonly', !0);
      }
      _removeMobileAttributes() {
        P.removeEventListener('click', this._onClickOverlay),
          this.$datepicker.classList.remove('-is-mobile-'),
          this.initialReadonly || '' === this.initialReadonly || this.$el.removeAttribute('readonly');
      }
      _createMinMaxDates() {
        let { minDate: e, maxDate: t } = this.opts;
        (this.minDate = !!e && b(e)), (this.maxDate = !!t && b(t));
      }
      _addTimepicker() {
        (this.$timepicker = n({ className: 'air-datepicker--time' })),
          this.$datepicker.appendChild(this.$timepicker),
          (this.timepicker = new L({ dp: this, opts: this.opts })),
          this.$timepicker.appendChild(this.timepicker.$el);
      }
      _addButtons() {
        (this.$buttons = n({ className: 'air-datepicker--buttons' })),
          this.$datepicker.appendChild(this.$buttons),
          (this.buttons = new x({ dp: this, opts: this.opts })),
          this.$buttons.appendChild(this.buttons.$el);
      }
      _bindSubEvents() {
        this.on(i.eventChangeSelectedDate, this._onChangeSelectedDate),
          this.on(i.eventChangeFocusDate, this._onChangeFocusedDate),
          this.on(i.eventChangeTime, this._onChangeTime);
      }
      _buildBaseHtml() {
        let { inline: e } = this.opts;
        var t, i;
        this.elIsInput
          ? e
            ? ((t = this.$datepicker), (i = this.$el).parentNode.insertBefore(t, i.nextSibling))
            : this.$container.appendChild(this.$datepicker)
          : this.$el.appendChild(this.$datepicker),
          (this.$datepicker.innerHTML =
            '<i class="air-datepicker--pointer"></i><div class="air-datepicker--navigation"></div><div class="air-datepicker--content"></div>'),
          (this.$content = a('.air-datepicker--content', this.$datepicker)),
          (this.$pointer = a('.air-datepicker--pointer', this.$datepicker)),
          (this.$nav = a('.air-datepicker--navigation', this.$datepicker));
      }
      _handleLocale() {
        let {
          locale: e,
          dateFormat: t,
          firstDay: i,
          timepicker: s,
          onlyTimepicker: a,
          timeFormat: n,
          dateTimeSeparator: r,
        } = this.opts;
        var h;
        (this.locale = ((h = e), JSON.parse(JSON.stringify(h)))),
          t && (this.locale.dateFormat = t),
          void 0 !== n && '' !== n && (this.locale.timeFormat = n);
        let { timeFormat: o } = this.locale;
        if (('' !== i && (this.locale.firstDay = i), s && 'function' != typeof t)) {
          let e = o ? r : '';
          this.locale.dateFormat = [this.locale.dateFormat, o || ''].join(e);
        }
        a && (this.locale.dateFormat = this.locale.timeFormat);
      }
      _setPositionClasses(e) {
        if ('function' == typeof e) return void this.$datepicker.classList.add('-custom-position-');
        let t = (e = e.split(' '))[0],
          i = e[1],
          s = 'air-datepicker -'.concat(t, '-').concat(i, '- -from-').concat(t, '-');
        this.$datepicker.classList.add(...s.split(' '));
      }
      _bindEvents() {
        this.$el.addEventListener(this.opts.showEvent, this._onFocus),
          this.$el.addEventListener('blur', this._onBlur),
          this.$datepicker.addEventListener('mousedown', this._onMouseDown),
          this.$datepicker.addEventListener('mouseup', this._onMouseUp),
          window.addEventListener('resize', this._onResize);
      }
      _limitViewDateByMaxMinDates() {
        let { viewDate: e, minDate: t, maxDate: i } = this;
        i && p(e, i) && this.setViewDate(i), t && m(e, t) && this.setViewDate(t);
      }
      formatDate() {
        let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : this.viewDate,
          t = arguments.length > 1 ? arguments[1] : void 0;
        if (((e = b(e)), !(e instanceof Date))) return;
        let i = t,
          s = this.locale,
          a = o(e),
          n = d(e),
          r = K.replacer,
          h = 'am';
        this.opts.timepicker && this.timepicker && (h = this.timepicker.getDayPeriod(e).dayPeriod);
        let l = {
          T: e.getTime(),
          m: a.minutes,
          mm: a.fullMinutes,
          h: a.hours12,
          hh: a.fullHours12,
          H: a.hours,
          HH: a.fullHours,
          aa: h,
          AA: h.toUpperCase(),
          E: s.daysShort[a.day],
          EEEE: s.days[a.day],
          d: a.date,
          dd: a.fullDate,
          M: a.month + 1,
          MM: a.fullMonth,
          MMM: s.monthsShort[a.month],
          MMMM: s.months[a.month],
          yy: a.year.toString().slice(-2),
          yyyy: a.year,
          yyyy1: n[0],
          yyyy2: n[1],
        };
        for (let [e, t] of Object.entries(l)) i = r(i, k(e), t);
        return i;
      }
      down(e) {
        this._handleUpDownActions(e, 'down');
      }
      up(e) {
        this._handleUpDownActions(e, 'up');
      }
      selectDate(e) {
        let t,
          s = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : {},
          { currentView: a, parsedViewDate: n, selectedDates: r } = this,
          { updateTime: h } = s,
          {
            moveToOtherMonthsOnSelect: o,
            moveToOtherYearsOnSelect: l,
            multipleDates: d,
            range: c,
            autoClose: u,
          } = this.opts,
          m = r.length;
        if (Array.isArray(e))
          return (
            e.forEach((e) => {
              this.selectDate(e, s);
            }),
            new Promise((e) => {
              setTimeout(e);
            })
          );
        if ((e = b(e)) instanceof Date) {
          if (
            (a === i.days && e.getMonth() !== n.month && o && (t = new Date(e.getFullYear(), e.getMonth(), 1)),
            a === i.years && e.getFullYear() !== n.year && l && (t = new Date(e.getFullYear(), 0, 1)),
            t && this.setViewDate(t),
            d && !c)
          ) {
            if (m === d) return;
            this._checkIfDateIsSelected(e) || r.push(e);
          } else if (c)
            switch (m) {
              case 1:
                r.push(e),
                  this.rangeDateTo || (this.rangeDateTo = e),
                  p(this.rangeDateFrom, this.rangeDateTo) &&
                    ((this.rangeDateTo = this.rangeDateFrom), (this.rangeDateFrom = e)),
                  (this.selectedDates = [this.rangeDateFrom, this.rangeDateTo]);
                break;
              case 2:
                (this.selectedDates = [e]), (this.rangeDateFrom = e), (this.rangeDateTo = '');
                break;
              default:
                (this.selectedDates = [e]), (this.rangeDateFrom = e);
            }
          else this.selectedDates = [e];
          return (
            this.trigger(i.eventChangeSelectedDate, {
              action: i.actionSelectDate,
              silent: null == s ? void 0 : s.silent,
              date: e,
              updateTime: h,
            }),
            this._updateLastSelectedDate(e),
            u && !this.timepickerIsActive && this.visible && (d || c ? c && 1 === m && this.hide() : this.hide()),
            new Promise((e) => {
              setTimeout(e);
            })
          );
        }
      }
      unselectDate(e) {
        let t = this.selectedDates,
          s = this;
        if ((e = b(e)) instanceof Date)
          return t.some((a, n) => {
            if (u(a, e))
              return (
                t.splice(n, 1),
                s.selectedDates.length
                  ? s._updateLastSelectedDate(s.selectedDates[s.selectedDates.length - 1])
                  : ((s.rangeDateFrom = ''), (s.rangeDateTo = ''), s._updateLastSelectedDate(!1)),
                this.trigger(i.eventChangeSelectedDate, { action: i.actionUnselectDate, date: e }),
                !0
              );
          });
      }
      replaceDate(e, t) {
        let s = this.selectedDates.find((t) => u(t, e, this.currentView)),
          a = this.selectedDates.indexOf(s);
        a < 0 ||
          u(this.selectedDates[a], t, this.currentView) ||
          ((this.selectedDates[a] = t),
          this.trigger(i.eventChangeSelectedDate, { action: i.actionSelectDate, date: t, updateTime: !0 }),
          this._updateLastSelectedDate(t));
      }
      clear() {
        let e = arguments.length > 0 && void 0 !== arguments[0] ? arguments[0] : {};
        return (
          (this.selectedDates = []),
          (this.rangeDateFrom = !1),
          (this.rangeDateTo = !1),
          this.trigger(i.eventChangeSelectedDate, { action: i.actionUnselectDate, silent: e.silent }),
          new Promise((e) => {
            setTimeout(e);
          })
        );
      }
      show() {
        let { onShow: e, isMobile: t } = this.opts;
        this._cancelScheduledCall(),
          this.visible || this.hideAnimation || this._createComponents(),
          this.setPosition(this.opts.position),
          this.$datepicker.classList.add('-active-'),
          (this.visible = !0),
          e && this._scheduleCallAfterTransition(e),
          t && this._showMobileOverlay();
      }
      hide() {
        let { onHide: e, isMobile: t } = this.opts,
          i = this._hasTransition();
        (this.visible = !1),
          (this.hideAnimation = !0),
          this.$datepicker.classList.remove('-active-'),
          this.customHide && this.customHide(),
          this.elIsInput && this.$el.blur(),
          this._scheduleCallAfterTransition((t) => {
            !this.customHide && ((t && i) || (!t && !i)) && this._finishHide(), e && e(t);
          }),
          t && P.classList.remove('-active-');
      }
      _triggerOnSelect() {
        let e = [],
          t = [],
          {
            selectedDates: i,
            locale: s,
            opts: { onSelect: a, multipleDates: n, range: r },
          } = this,
          h = n || r,
          o = 'function' == typeof s.dateFormat;
        i.length &&
          ((e = i.map(g)),
          (t = o
            ? n
              ? s.dateFormat(e)
              : e.map((e) => s.dateFormat(e))
            : e.map((e) => this.formatDate(e, s.dateFormat)))),
          a({ date: h ? e : e[0], formattedDate: h ? t : t[0], datepicker: this });
      }
      _handleAlreadySelectedDates(e, t) {
        let { range: i, toggleSelected: s } = this.opts;
        i
          ? s
            ? this.unselectDate(t)
            : 2 !== this.selectedDates.length && this.selectDate(t)
          : s && this.unselectDate(t),
          s || this._updateLastSelectedDate(e);
      }
      _handleUpDownActions(e, t) {
        if (!((e = b(e || this.focusDate || this.viewDate)) instanceof Date)) return;
        let i = 'up' === t ? this.viewIndex + 1 : this.viewIndex - 1;
        i > 2 && (i = 2),
          i < 0 && (i = 0),
          this.setViewDate(new Date(e.getFullYear(), e.getMonth(), 1)),
          this.setCurrentView(this.viewIndexes[i]);
      }
      _handleRangeOnFocus() {
        1 === this.selectedDates.length &&
          (p(this.selectedDates[0], this.focusDate)
            ? ((this.rangeDateTo = this.selectedDates[0]), (this.rangeDateFrom = this.focusDate))
            : ((this.rangeDateTo = this.focusDate), (this.rangeDateFrom = this.selectedDates[0])));
      }
      getCell(e) {
        let t = arguments.length > 1 && void 0 !== arguments[1] ? arguments[1] : i.day;
        if (!((e = b(e)) instanceof Date)) return;
        let { year: s, month: a, date: n } = o(e),
          r = '[data-year="'.concat(s, '"]'),
          h = '[data-month="'.concat(a, '"]'),
          l = '[data-date="'.concat(n, '"]'),
          d = { [i.day]: ''.concat(r).concat(h).concat(l), [i.month]: ''.concat(r).concat(h), [i.year]: ''.concat(r) };
        return this.views[this.currentView].$el.querySelector(d[t]);
      }
      _showMobileOverlay() {
        P.classList.add('-active-');
      }
      _hasTransition() {
        return (
          window
            .getComputedStyle(this.$datepicker)
            .getPropertyValue('transition-duration')
            .split(', ')
            .reduce((e, t) => parseFloat(t) + e, 0) > 0
        );
      }
      get shouldUpdateDOM() {
        return this.visible || this.treatAsInline;
      }
      get parsedViewDate() {
        return o(this.viewDate);
      }
      get currentViewSingular() {
        return this.currentView.slice(0, -1);
      }
      get curDecade() {
        return d(this.viewDate);
      }
      get viewIndex() {
        return this.viewIndexes.indexOf(this.currentView);
      }
      get isFinalView() {
        return this.currentView === i.years;
      }
      get hasSelectedDates() {
        return this.selectedDates.length > 0;
      }
      get isMinViewReached() {
        return this.currentView === this.opts.minView || this.currentView === i.days;
      }
      get $container() {
        return this.$customContainer || R;
      }
      static replacer(e, t, i) {
        return e.replace(t, function (e, t, s, a) {
          return t + i + a;
        });
      }
    }
    var j;
    return (
      N(K, 'defaults', s),
      N(K, 'version', '3.2.0'),
      N(K, 'defaultContainerId', 'air-datepicker-global-container'),
      (j = K.prototype),
      Object.assign(j, E),
      t.default
    );
  })();
});
