.section-image-banner-pc {
  position: relative;
}
.image-banner--container {
  display: flex;
  width: 100%;
}
.banner-item-wrapper {
  width: 50%;
  flex-shrink: 0;
  flex-grow: 1;
  position: relative;
  overflow: hidden;
}
.banner-item-wrapper > div {
  width: 100%;
}
.banner-item-wrapper > div img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center center;
  position: absolute;
  top: 0;
}
.banner-item-wrapper--size-low {
  height: 500px;
}
@media screen and (max-width: 959px) {
  .banner-item-wrapper--size-low {
    height: 300px;
  }
}
.banner-item-wrapper--size-middle {
  height: 650px;
}
@media screen and (max-width: 959px) {
  .banner-item-wrapper--size-middle {
    height: 450px;
  }
}
.banner-item-wrapper--size-high {
  height: 800px;
}
@media screen and (max-width: 959px) {
  .banner-item-wrapper--size-high {
    height: 600px;
  }
}
.section-image-banner .content {
  bottom: 0;
  display: flex;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  width: 100%;
  z-index: 3;
}
.section-image-banner .content .wrapper .desc {
  font-weight: 400;
  margin-bottom: 16px;
  margin-top: 8px;
  position: relative;
  z-index: 2;
}
.section-image-banner .content .wrapper {
  box-sizing: border-box;
  display: inline-block;
  max-width: 800px;
  min-width: 0;
  padding: 40px;
  position: relative;
}
.section-image-banner .content .wrapper--default-scheme .desc,
.section-image-banner .content .wrapper--default-scheme .title {
  color: #fff;
}
.section-image-banner .content .wrapper--default-scheme .control .button {
  --btn-border-background-color: #fff;
  background-color: #fff;
  color: #000;
}
.section-image-banner .content .wrapper--default-scheme .control .button:after {
  box-shadow: 0 0 0 var(--btn-border-thickness)
      rgba(0, 0, 0, calc(100% - var(--btn-border-opacity))),
    0 0 0 var(--btn-border-thickness) var(--btn-border-background-color);
}
.section-image-banner
  .content
  .wrapper--default-scheme
  .control
  .button.disabled:hover:after {
  box-shadow: 0 0 0 var(--btn-border-thickness)
    rgba(0, 0, 0, calc(100% - var(--btn-border-opacity)));
}
.section-image-banner
  .content
  .wrapper--default-scheme
  .control
  .button:hover:after {
  box-shadow: 0 0 0 calc(var(--btn-border-thickness) + 1px)
      rgba(0, 0, 0, calc(100% - var(--btn-border-opacity))),
    0 0 0 calc(var(--btn-border-thickness) + 1px)
      var(--btn-border-background-color);
}
.section-image-banner
  .content
  .wrapper--default-scheme
  .control
  .button.button--secondary {
  --btn-border-background-color: transparent;
  background-color: transparent;
  color: #fff;
}
.section-image-banner
  .content
  .wrapper--default-scheme
  .control
  .button.button--secondary:after {
  box-shadow: 0 0 0 var(--btn-border-thickness)
      rgba(255, 255, 255, var(--btn-border-opacity)),
    0 0 0 var(--btn-border-thickness) var(--btn-border-background-color);
}
.section-image-banner
  .content
  .wrapper--default-scheme
  .control
  .button.button--secondary.disabled:hover:after {
  box-shadow: 0 0 0 var(--btn-border-thickness)
    rgba(255, 255, 255, var(--btn-border-opacity));
}
.section-image-banner
  .content
  .wrapper--default-scheme
  .control
  .button.button--secondary:hover:after {
  box-shadow: 0 0 0 calc(var(--btn-border-thickness) + 1px)
      rgba(255, 255, 255, var(--btn-border-opacity)),
    0 0 0 calc(var(--btn-border-thickness) + 1px)
      var(--btn-border-background-color);
}
.section-image-banner .content--pos-left-top {
  align-items: flex-start;
  text-align: left;
}
.section-image-banner .content--pos-top {
  align-items: flex-start;
  text-align: center;
}
.section-image-banner .content--pos-right-top {
  align-items: flex-start;
  text-align: right;
}
.section-image-banner .content--pos-left {
  align-items: center;
  text-align: left;
}
.section-image-banner .content--pos-center {
  align-items: center;
  text-align: center;
}
.section-image-banner .content--pos-right {
  align-items: center;
  text-align: right;
}
.section-image-banner .content--pos-left-bottom {
  align-items: flex-end;
  text-align: left;
}
.section-image-banner .content--pos-bottom {
  align-items: flex-end;
  text-align: center;
}
.section-image-banner .content--pos-right-bottom {
  align-items: flex-end;
  text-align: right;
}
.section-image-banner .mask {
  background-color: rgba(var(--color-mask));
  height: 100%;
  left: 0;
  opacity: 0.3;
  position: absolute;
  top: 0;
  width: 100%;
  z-index: 1;
  display: block;
}
.section-image-banner--m-flatten .image-banner--container {
  flex-wrap: wrap;
}
.section-image-banner--m-flatten .banner-item-wrapper {
  width: 100%;
}
.section-image-banner-mobile .content {
  width: 100%;
}
.section-image-banner-mobile {
  position: relative;
}
.section-image-banner-mobile .image-banner--container {
  flex-wrap: wrap;
}
.content-m-no-bg .title2 {
  color: #fff;
}
.content-m-no-bg .body1 {
  color: #fff;
}
.content-m--normal-flow {
  position: relative !important;
}
.section-image-banner--m-normal .content-m--normal-flow {
  order: 3;
}
.section-image-banner .content .wrapper.color-scheme-none {
  background-color: rgba(var(--color-page-background));
  color: rgba(var(--color-text));
}
.section-image-banner .default-image {
  height: 650px;
  order: 0;
}
@media (max-width: 959px) {
  .section-image-banner .default-image {
    height: 450px;
  }
}
.pc-content-text-center,
.mobile-content-text-center {
  text-align: center;
}
.pc-content-text-left,
.mobile-content-text-left {
  text-align: left;
}
.pc-content-text-right,
.mobile-content-text-right {
  text-align: right;
}
.control-wrap .button {
  margin: 0 7px;
}
/* The ipad end responds to the mobile end in vertical screen */
/* @custom-media --tablet (max-width: 959px); */
/* @custom-media --gt-mobile (min-width: 751px); */
/* detectingScreen need to consider the configuration of the tablet */
