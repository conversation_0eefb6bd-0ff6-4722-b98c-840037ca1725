/*.main-password*/
.main-password {
  margin: 0;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}
.main-password .password-section {
  flex-grow: 1;
}
.main-password .password-header {
  padding: 20px 15px 25px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-direction: column;
  color: rgb(var(--color-text));
  max-width: var(--page-width);
  width: 100%;
  margin: 0 auto;
  text-align: center;
}
.main-password .password-header img {
  display: block;
  width: var(--header-logo-pc-width, 200px);
  height: auto;
  max-width: var(--header-logo-pc-width);
  max-height: var(--header-logo-pc-height);
  object-fit: contain;
  -webkit-user-drag: none;
}
.main-password .password-header .title5 {
  margin-top: 13.4px;
  margin-bottom: 13.4px;
}
.main-password .password-header .modal__toggle-open {
  cursor: pointer;
  display: flex;
}
.main-password .password-header .modal__toggle-open svg {
  width: 18px;
  height: 18px;
  margin-right: 10px;
}
.main-password .password-header password-modal {
  flex-shrink: 0;
}
/*modal*/
.main-password .password-header .modal__close-button {
  background-color: rgb(var(--color-page-background));
  border: 1px solid rgba(var(--color-text), 0.05);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 13px;
  z-index: 5;
  position: absolute;
  top: 21px;
  right: 21px;
}
.main-password .password-header .password-modal__content {
  padding: 45px 20px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  width: 100%;
  height: 100%;
  box-sizing: border-box;
  max-width: 430px;
}
.main-password .password-header .password-modal__content form {
  width: 100%;
  text-align: left;
  margin-bottom: 20px;
}
.main-password .password-header .password-modal__content form .field__input {
  height: 50px;
}
.main-password .password-header .password-modal__content-desc {
  margin-bottom: 40px;
}
.main-password .password-header .password-modal__content-tip {
  margin-bottom: 20px;
}
.main-password .password-header password-modal {
  justify-self: flex-end;
  grid-column: 3;
}
.main-password .password-header .link:hover {
  text-decoration: underline;
  text-underline-offset: 6px;
  color: rgba(var(--color-text));
}
@media only screen and (min-width: 960px) {
  .main-password .password-header {
    display: grid;
    gap: 30px;
    grid-template-columns: 1fr 1.5fr 1fr;
    padding: 20px 50px 25px;
    text-align: left;
  }
}
@media only screen and (max-width: 959px) {
  .main-password .password-header img {
    margin-top: 13.4px;
    margin-bottom: 13.4px;
    width: var(--header-logo-mobile-width, 100px);
    max-width: var(--header-logo-mobile-width);
    max-height: var(--header-logo-mobile-height);
  }
}
/* The ipad end responds to the mobile end in vertical screen */
/* @custom-media --tablet (max-width: 959px); */
/* @custom-media --gt-mobile (min-width: 751px); */
/* detectingScreen need to consider the configuration of the tablet */
