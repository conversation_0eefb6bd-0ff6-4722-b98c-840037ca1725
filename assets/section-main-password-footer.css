/*.main-password*/
.main-password .password__footer__hr {
  border: none;
  height: 1px;
  background-color: rgba(var(--color-text), 0.2);
  display: block;
  margin: 0;
  width: 100%;
}
.main-password .password__footer {
  padding: 40px 15px;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}
.main-password .password__footer .link:hover {
  text-decoration: underline;
  text-underline-offset: 6px;
  color: rgba(var(--color-text));
}
.main-password .password__footer .password-share {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 40px;
}
.main-password .password__footer .password-share .share-item {
  display: inline-block;
  color: inherit;
  margin-right: 8px;
  transform: scale(1);
  transition: transform 125ms cubic-bezier(0.4, 0, 0.2, 1);
  opacity: 1;
}
.main-password .password__footer .password-share .share-item svg,
.main-password .password__footer .password-share .share-item img {
  width: 28px !important;
  height: 28px !important;
}
.main-password .password__footer .password-share .share-item:last-child {
  margin-right: 0;
}
.main-password .password__footer .password-share .share-item :active {
  opacity: 0.8;
}
.main-password .password__footer .password-share .share-item:hover {
  color: inherit;
  transform: scale(1.1);
}
.main-password
  .password__footer
  .password-share
  .password-share__social-icons:first-child
  a:first-child {
  margin-left: 0;
}
/* The ipad end responds to the mobile end in vertical screen */
/* @custom-media --tablet (max-width: 959px); */
/* @custom-media --gt-mobile (min-width: 751px); */
/* detectingScreen need to consider the configuration of the tablet */
