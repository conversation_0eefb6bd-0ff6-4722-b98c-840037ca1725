/*!
 * PhotoSwipe Lightbox 5.4.3 - https://photoswipe.com
 * (c) 2023 <PERSON><PERSON><PERSON>/
!(function (t, i) {
  'object' == typeof exports && 'undefined' != typeof module
    ? (module.exports = i())
    : 'function' == typeof define && define.amd
    ? define(i)
    : ((t = 'undefined' != typeof globalThis ? globalThis : t || self).PhotoSwipeLightbox = i());
})(this, function () {
  'use strict';
  function t(t, i, s) {
    const h = document.createElement(i);
    return t && (h.className = t), s && s.appendChild(h), h;
  }
  function i(t, i, s) {
    (t.style.width = 'number' == typeof i ? `${i}px` : i), (t.style.height = 'number' == typeof s ? `${s}px` : s);
  }
  const s = 'idle',
    h = 'loading',
    e = 'loaded',
    n = 'error';
  function o(t, i, s = document) {
    let h = [];
    if (t instanceof Element) h = [t];
    else if (t instanceof NodeList || Array.isArray(t)) h = Array.from(t);
    else {
      const e = 'string' == typeof t ? t : i;
      e && (h = Array.from(s.querySelectorAll(e)));
    }
    return h;
  }
  function r() {
    return !(!navigator.vendor || !navigator.vendor.match(/apple/i));
  }
  class l {
    constructor(t, i) {
      (this.type = t), (this.defaultPrevented = !1), i && Object.assign(this, i);
    }
    preventDefault() {
      this.defaultPrevented = !0;
    }
  }
  class a {
    constructor(i, s) {
      if (((this.element = t('pswp__img pswp__img--placeholder', i ? 'img' : 'div', s)), i)) {
        const t = this.element;
        (t.decoding = 'async'), (t.alt = ''), (t.src = i), t.setAttribute('role', 'presentation');
      }
      this.element.setAttribute('aria-hidden', 'true');
    }
    setDisplayedSize(t, s) {
      this.element &&
        ('IMG' === this.element.tagName
          ? (i(this.element, 250, 'auto'),
            (this.element.style.transformOrigin = '0 0'),
            (this.element.style.transform = (function (t, i, s) {
              let h = `translate3d(${t}px,${i || 0}px,0)`;
              return void 0 !== s && (h += ` scale3d(${s},${s},1)`), h;
            })(0, 0, t / 250)))
          : i(this.element, t, s));
    }
    destroy() {
      var t;
      null !== (t = this.element) && void 0 !== t && t.parentNode && this.element.remove(), (this.element = null);
    }
  }
  class d {
    constructor(t, i, h) {
      (this.instance = i),
        (this.data = t),
        (this.index = h),
        (this.element = void 0),
        (this.placeholder = void 0),
        (this.slide = void 0),
        (this.displayedImageWidth = 0),
        (this.displayedImageHeight = 0),
        (this.width = Number(this.data.w) || Number(this.data.width) || 0),
        (this.height = Number(this.data.h) || Number(this.data.height) || 0),
        (this.isAttached = !1),
        (this.hasSlide = !1),
        (this.isDecoding = !1),
        (this.state = s),
        this.data.type ? (this.type = this.data.type) : this.data.src ? (this.type = 'image') : (this.type = 'html'),
        this.instance.dispatch('contentInit', { content: this });
    }
    removePlaceholder() {
      this.placeholder &&
        !this.keepPlaceholder() &&
        setTimeout(() => {
          this.placeholder && (this.placeholder.destroy(), (this.placeholder = void 0));
        }, 1e3);
    }
    load(i, s) {
      if (this.slide && this.usePlaceholder())
        if (this.placeholder) {
          const t = this.placeholder.element;
          t && !t.parentElement && this.slide.container.prepend(t);
        } else {
          const t = this.instance.applyFilters(
            'placeholderSrc',
            !(!this.data.msrc || !this.slide.isFirstSlide) && this.data.msrc,
            this,
          );
          this.placeholder = new a(t, this.slide.container);
        }
      (this.element && !s) ||
        this.instance.dispatch('contentLoad', { content: this, isLazy: i }).defaultPrevented ||
        (this.isImageContent()
          ? ((this.element = t('pswp__img', 'img')), this.displayedImageWidth && this.loadImage(i))
          : ((this.element = t('pswp__content', 'div')), (this.element.innerHTML = this.data.html || '')),
        s && this.slide && this.slide.updateContentSize(!0));
    }
    loadImage(t) {
      var i, s;
      if (
        !this.isImageContent() ||
        !this.element ||
        this.instance.dispatch('contentLoadImage', { content: this, isLazy: t }).defaultPrevented
      )
        return;
      const e = this.element;
      this.updateSrcsetSizes(),
        this.data.srcset && (e.srcset = this.data.srcset),
        (e.src = null !== (i = this.data.src) && void 0 !== i ? i : ''),
        (e.alt = null !== (s = this.data.alt) && void 0 !== s ? s : ''),
        (this.state = h),
        e.complete
          ? this.onLoaded()
          : ((e.onload = () => {
              this.onLoaded();
            }),
            (e.onerror = () => {
              this.onError();
            }));
    }
    setSlide(t) {
      (this.slide = t), (this.hasSlide = !0), (this.instance = t.pswp);
    }
    onLoaded() {
      (this.state = e),
        this.slide &&
          this.element &&
          (this.instance.dispatch('loadComplete', { slide: this.slide, content: this }),
          this.slide.isActive &&
            this.slide.heavyAppended &&
            !this.element.parentNode &&
            (this.append(), this.slide.updateContentSize(!0)),
          (this.state !== e && this.state !== n) || this.removePlaceholder());
    }
    onError() {
      (this.state = n),
        this.slide &&
          (this.displayError(),
          this.instance.dispatch('loadComplete', { slide: this.slide, isError: !0, content: this }),
          this.instance.dispatch('loadError', { slide: this.slide, content: this }));
    }
    isLoading() {
      return this.instance.applyFilters('isContentLoading', this.state === h, this);
    }
    isError() {
      return this.state === n;
    }
    isImageContent() {
      return 'image' === this.type;
    }
    setDisplayedSize(t, s) {
      if (
        this.element &&
        (this.placeholder && this.placeholder.setDisplayedSize(t, s),
        !this.instance.dispatch('contentResize', { content: this, width: t, height: s }).defaultPrevented &&
          (i(this.element, t, s), this.isImageContent() && !this.isError()))
      ) {
        const i = !this.displayedImageWidth && t;
        (this.displayedImageWidth = t),
          (this.displayedImageHeight = s),
          i ? this.loadImage(!1) : this.updateSrcsetSizes(),
          this.slide &&
            this.instance.dispatch('imageSizeChange', { slide: this.slide, width: t, height: s, content: this });
      }
    }
    isZoomable() {
      return this.instance.applyFilters('isContentZoomable', this.isImageContent() && this.state !== n, this);
    }
    updateSrcsetSizes() {
      if (!this.isImageContent() || !this.element || !this.data.srcset) return;
      const t = this.element,
        i = this.instance.applyFilters('srcsetSizesWidth', this.displayedImageWidth, this);
      (!t.dataset.largestUsedSize || i > parseInt(t.dataset.largestUsedSize, 10)) &&
        ((t.sizes = i + 'px'), (t.dataset.largestUsedSize = String(i)));
    }
    usePlaceholder() {
      return this.instance.applyFilters('useContentPlaceholder', this.isImageContent(), this);
    }
    lazyLoad() {
      this.instance.dispatch('contentLazyLoad', { content: this }).defaultPrevented || this.load(!0);
    }
    keepPlaceholder() {
      return this.instance.applyFilters('isKeepingPlaceholder', this.isLoading(), this);
    }
    destroy() {
      (this.hasSlide = !1),
        (this.slide = void 0),
        this.instance.dispatch('contentDestroy', { content: this }).defaultPrevented ||
          (this.remove(),
          this.placeholder && (this.placeholder.destroy(), (this.placeholder = void 0)),
          this.isImageContent() &&
            this.element &&
            ((this.element.onload = null), (this.element.onerror = null), (this.element = void 0)));
    }
    displayError() {
      if (this.slide) {
        var i, s;
        let h = t('pswp__error-msg', 'div');
        (h.innerText =
          null !== (i = null === (s = this.instance.options) || void 0 === s ? void 0 : s.errorMsg) && void 0 !== i
            ? i
            : ''),
          (h = this.instance.applyFilters('contentErrorElement', h, this)),
          (this.element = t('pswp__content pswp__error-msg-container', 'div')),
          this.element.appendChild(h),
          (this.slide.container.innerText = ''),
          this.slide.container.appendChild(this.element),
          this.slide.updateContentSize(!0),
          this.removePlaceholder();
      }
    }
    append() {
      if (this.isAttached || !this.element) return;
      if (((this.isAttached = !0), this.state === n)) return void this.displayError();
      if (this.instance.dispatch('contentAppend', { content: this }).defaultPrevented) return;
      const t = 'decode' in this.element;
      this.isImageContent()
        ? t && this.slide && (!this.slide.isActive || r())
          ? ((this.isDecoding = !0),
            this.element
              .decode()
              .catch(() => {})
              .finally(() => {
                (this.isDecoding = !1), this.appendImage();
              }))
          : this.appendImage()
        : this.slide && !this.element.parentNode && this.slide.container.appendChild(this.element);
    }
    activate() {
      !this.instance.dispatch('contentActivate', { content: this }).defaultPrevented &&
        this.slide &&
        (this.isImageContent() && this.isDecoding && !r() ? this.appendImage() : this.isError() && this.load(!1, !0),
        this.slide.holderElement && this.slide.holderElement.setAttribute('aria-hidden', 'false'));
    }
    deactivate() {
      this.instance.dispatch('contentDeactivate', { content: this }),
        this.slide && this.slide.holderElement && this.slide.holderElement.setAttribute('aria-hidden', 'true');
    }
    remove() {
      (this.isAttached = !1),
        this.instance.dispatch('contentRemove', { content: this }).defaultPrevented ||
          (this.element && this.element.parentNode && this.element.remove(),
          this.placeholder && this.placeholder.element && this.placeholder.element.remove());
    }
    appendImage() {
      this.isAttached &&
        (this.instance.dispatch('contentAppendImage', { content: this }).defaultPrevented ||
          (this.slide && this.element && !this.element.parentNode && this.slide.container.appendChild(this.element),
          (this.state !== e && this.state !== n) || this.removePlaceholder()));
    }
  }
  function c(t, i, s, h, e) {
    let n = 0;
    if (i.paddingFn) n = i.paddingFn(s, h, e)[t];
    else if (i.padding) n = i.padding[t];
    else {
      const s = 'padding' + t[0].toUpperCase() + t.slice(1);
      i[s] && (n = i[s]);
    }
    return Number(n) || 0;
  }
  class u {
    constructor(t, i, s, h) {
      (this.pswp = h),
        (this.options = t),
        (this.itemData = i),
        (this.index = s),
        (this.panAreaSize = null),
        (this.elementSize = null),
        (this.fit = 1),
        (this.fill = 1),
        (this.vFill = 1),
        (this.initial = 1),
        (this.secondary = 1),
        (this.max = 1),
        (this.min = 1);
    }
    update(t, i, s) {
      const h = { x: t, y: i };
      (this.elementSize = h), (this.panAreaSize = s);
      const e = s.x / h.x,
        n = s.y / h.y;
      (this.fit = Math.min(1, e < n ? e : n)),
        (this.fill = Math.min(1, e > n ? e : n)),
        (this.vFill = Math.min(1, n)),
        (this.initial = this.t()),
        (this.secondary = this.i()),
        (this.max = Math.max(this.initial, this.secondary, this.o())),
        (this.min = Math.min(this.fit, this.initial, this.secondary)),
        this.pswp && this.pswp.dispatch('zoomLevelsUpdate', { zoomLevels: this, slideData: this.itemData });
    }
    l(t) {
      const i = t + 'ZoomLevel',
        s = this.options[i];
      if (s) return 'function' == typeof s ? s(this) : 'fill' === s ? this.fill : 'fit' === s ? this.fit : Number(s);
    }
    i() {
      let t = this.l('secondary');
      return (
        t ||
        ((t = Math.min(1, 3 * this.fit)),
        this.elementSize && t * this.elementSize.x > 4e3 && (t = 4e3 / this.elementSize.x),
        t)
      );
    }
    t() {
      return this.l('initial') || this.fit;
    }
    o() {
      return this.l('max') || Math.max(1, 4 * this.fit);
    }
  }
  function p(t, i, s) {
    const h = i.createContentFromData(t, s);
    let e;
    const { options: n } = i;
    if (n) {
      let o;
      (e = new u(n, t, -1)),
        (o = i.pswp
          ? i.pswp.viewportSize
          : (function (t, i) {
              if (t.getViewportSizeFn) {
                const s = t.getViewportSizeFn(t, i);
                if (s) return s;
              }
              return { x: document.documentElement.clientWidth, y: window.innerHeight };
            })(n, i));
      const r = (function (t, i, s, h) {
        return {
          x: i.x - c('left', t, i, s, h) - c('right', t, i, s, h),
          y: i.y - c('top', t, i, s, h) - c('bottom', t, i, s, h),
        };
      })(n, o, t, s);
      e.update(h.width, h.height, r);
    }
    return h.lazyLoad(), e && h.setDisplayedSize(Math.ceil(h.width * e.initial), Math.ceil(h.height * e.initial)), h;
  }
  return class extends class extends class {
    constructor() {
      (this.u = {}), (this.p = {}), (this.pswp = void 0), (this.options = void 0);
    }
    addFilter(t, i, s = 100) {
      var h, e, n;
      this.p[t] || (this.p[t] = []),
        null === (h = this.p[t]) || void 0 === h || h.push({ fn: i, priority: s }),
        null === (e = this.p[t]) || void 0 === e || e.sort((t, i) => t.priority - i.priority),
        null === (n = this.pswp) || void 0 === n || n.addFilter(t, i, s);
    }
    removeFilter(t, i) {
      this.p[t] && (this.p[t] = this.p[t].filter((t) => t.fn !== i)), this.pswp && this.pswp.removeFilter(t, i);
    }
    applyFilters(t, ...i) {
      var s;
      return (
        null === (s = this.p[t]) ||
          void 0 === s ||
          s.forEach((t) => {
            i[0] = t.fn.apply(this, i);
          }),
        i[0]
      );
    }
    on(t, i) {
      var s, h;
      this.u[t] || (this.u[t] = []),
        null === (s = this.u[t]) || void 0 === s || s.push(i),
        null === (h = this.pswp) || void 0 === h || h.on(t, i);
    }
    off(t, i) {
      var s;
      this.u[t] && (this.u[t] = this.u[t].filter((t) => i !== t)),
        null === (s = this.pswp) || void 0 === s || s.off(t, i);
    }
    dispatch(t, i) {
      var s;
      if (this.pswp) return this.pswp.dispatch(t, i);
      const h = new l(t, i);
      return (
        null === (s = this.u[t]) ||
          void 0 === s ||
          s.forEach((t) => {
            t.call(this, h);
          }),
        h
      );
    }
  } {
    getNumItems() {
      var t;
      let i = 0;
      const s = null === (t = this.options) || void 0 === t ? void 0 : t.dataSource;
      s && 'length' in s
        ? (i = s.length)
        : s && 'gallery' in s && (s.items || (s.items = this.v(s.gallery)), s.items && (i = s.items.length));
      const h = this.dispatch('numItems', { dataSource: s, numItems: i });
      return this.applyFilters('numItems', h.numItems, s);
    }
    createContentFromData(t, i) {
      return new d(t, this, i);
    }
    getItemData(t) {
      var i;
      const s = null === (i = this.options) || void 0 === i ? void 0 : i.dataSource;
      let h = {};
      Array.isArray(s)
        ? (h = s[t])
        : s && 'gallery' in s && (s.items || (s.items = this.v(s.gallery)), (h = s.items[t]));
      let e = h;
      e instanceof Element && (e = this.m(e));
      const n = this.dispatch('itemData', { itemData: e || {}, index: t });
      return this.applyFilters('itemData', n.itemData, t);
    }
    v(t) {
      var i, s;
      return (null !== (i = this.options) && void 0 !== i && i.children) ||
        (null !== (s = this.options) && void 0 !== s && s.childSelector)
        ? o(this.options.children, this.options.childSelector, t) || []
        : [t];
    }
    m(t) {
      const i = { element: t },
        s = 'A' === t.tagName ? t : t.querySelector('a');
      if (s) {
        (i.src = s.dataset.pswpSrc || s.href),
          s.dataset.pswpSrcset && (i.srcset = s.dataset.pswpSrcset),
          (i.width = s.dataset.pswpWidth ? parseInt(s.dataset.pswpWidth, 10) : 0),
          (i.height = s.dataset.pswpHeight ? parseInt(s.dataset.pswpHeight, 10) : 0),
          (i.w = i.width),
          (i.h = i.height),
          s.dataset.pswpType && (i.type = s.dataset.pswpType);
        const e = t.querySelector('img');
        var h;
        if (e)
          (i.msrc = e.currentSrc || e.src), (i.alt = null !== (h = e.getAttribute('alt')) && void 0 !== h ? h : '');
        (s.dataset.pswpCropped || s.dataset.cropped) && (i.thumbCropped = !0);
      }
      return this.applyFilters('domItemData', i, t, s);
    }
    lazyLoadData(t, i) {
      return p(t, this, i);
    }
  } {
    constructor(t) {
      super(),
        (this.options = t || {}),
        (this.g = 0),
        (this.shouldOpen = !1),
        (this._ = void 0),
        (this.onThumbnailsClick = this.onThumbnailsClick.bind(this));
    }
    init() {
      o(this.options.gallery, this.options.gallerySelector).forEach((t) => {
        t.addEventListener('click', this.onThumbnailsClick, !1);
      });
    }
    onThumbnailsClick(t) {
      if (
        (function (t) {
          return ('button' in t && 1 === t.button) || t.ctrlKey || t.metaKey || t.altKey || t.shiftKey;
        })(t) ||
        window.pswp
      )
        return;
      let i = { x: t.clientX, y: t.clientY };
      i.x || i.y || (i = null);
      let s = this.getClickedIndex(t);
      s = this.applyFilters('clickedIndex', s, t, this);
      const h = { gallery: t.currentTarget };
      s >= 0 && (t.preventDefault(), this.loadAndOpen(s, h, i));
    }
    getClickedIndex(t) {
      if (this.options.getClickedIndexFn) return this.options.getClickedIndexFn.call(this, t);
      const i = t.target,
        s = o(this.options.children, this.options.childSelector, t.currentTarget).findIndex(
          (t) => t === i || t.contains(i),
        );
      return -1 !== s ? s : this.options.children || this.options.childSelector ? -1 : 0;
    }
    loadAndOpen(t, i, s) {
      if (window.pswp || !this.options) return !1;
      if (!i && this.options.gallery && this.options.children) {
        const t = o(this.options.gallery);
        t[0] && (i = { gallery: t[0] });
      }
      return (
        (this.options.index = t), (this.options.initialPointerPos = s), (this.shouldOpen = !0), this.preload(t, i), !0
      );
    }
    preload(t, i) {
      const { options: s } = this;
      i && (s.dataSource = i);
      const h = [],
        e = typeof s.pswpModule;
      if ('function' == typeof (n = s.pswpModule) && n.prototype && n.prototype.goTo)
        h.push(Promise.resolve(s.pswpModule));
      else {
        if ('string' === e) throw new Error('pswpModule as string is no longer supported');
        if ('function' !== e) throw new Error('pswpModule is not valid');
        h.push(s.pswpModule());
      }
      var n;
      'function' == typeof s.openPromise && h.push(s.openPromise()),
        !1 !== s.preloadFirstSlide &&
          t >= 0 &&
          (this._ = (function (t, i) {
            const s = i.getItemData(t);
            if (!i.dispatch('lazyLoadSlide', { index: t, itemData: s }).defaultPrevented) return p(s, i, t);
          })(t, this));
      const o = ++this.g;
      Promise.all(h).then((t) => {
        if (this.shouldOpen) {
          const i = t[0];
          this.I(i, o);
        }
      });
    }
    I(t, i) {
      if (i !== this.g && this.shouldOpen) return;
      if (((this.shouldOpen = !1), window.pswp)) return;
      const s = 'object' == typeof t ? new t.default(this.options) : new t(this.options);
      (this.pswp = s),
        (window.pswp = s),
        Object.keys(this.u).forEach((t) => {
          var i;
          null === (i = this.u[t]) ||
            void 0 === i ||
            i.forEach((i) => {
              s.on(t, i);
            });
        }),
        Object.keys(this.p).forEach((t) => {
          var i;
          null === (i = this.p[t]) ||
            void 0 === i ||
            i.forEach((i) => {
              s.addFilter(t, i.fn, i.priority);
            });
        }),
        this._ && (s.contentLoader.addToCache(this._), (this._ = void 0)),
        s.on('destroy', () => {
          (this.pswp = void 0), delete window.pswp;
        }),
        s.init();
    }
    destroy() {
      var t;
      null === (t = this.pswp) || void 0 === t || t.destroy(),
        (this.shouldOpen = !1),
        (this.u = {}),
        o(this.options.gallery, this.options.gallerySelector).forEach((t) => {
          t.removeEventListener('click', this.onThumbnailsClick, !1);
        });
    }
  };
});
