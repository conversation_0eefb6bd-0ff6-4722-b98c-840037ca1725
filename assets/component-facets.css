.facets {
  display: block;
}

.facets__wrapper {
  align-items: center;
  align-self: flex-start;
  display: flex;
  flex-wrap: wrap;
}

.facets__form {
  display: grid;
  gap: 0 35px;
  grid-template-columns: 1fr max-content max-content;
}

.facets__summary > div {
  display: flex;
  align-items: center;
}

.facets__summary span {
  padding-right: 10px;
}

.facets__summary .icon-arrow {
  position: revert;
}

.facets__header {
  display: flex;
  gap: 10px;
  justify-content: space-between;
  padding-bottom: 17px;
  border-bottom: 1px solid rgb(var(--color-entry-line));
}

.facets__header .facets__selected {
  font-size: 14px;
}

.facets__display {
  position: absolute;
  top: 46px;
  left: 0;
  max-width: 330px;
  max-height: 480px;
  overflow: auto;
  z-index: 10;
  background-color: rgb(var(--color-page-background));
  padding: 20px 20px;
}

.facets__display.auto-width .stock-filter-text {
  width: max-content;
  word-break: break-word;
}

.facets__display fieldset {
  border: 0;
  padding: 0;
}

.facets__display-vertical {
  padding-bottom: 10px;
}

.mobile-facets__list {
  overflow-y: auto;
}

.facets__list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.facets__item {
  padding: 5px 0;
}

.facets__disclosure {
  margin-right: 35px;
}

.facets__price-details .facets__price-content {
  width: 340px;
}

.facets__price-details .facets__price-content.facets__display-vertical {
  width: 100%;
}

.facets__price-details .facets__header {
  border-bottom: 0;
}

.facet-checkbox {
  width: 100%;
  display: inline-block;
  cursor: pointer;
}

.facet-checkbox > span {
  display: inline-flex;
  align-items: center;
}

.facet-checkbox > span::before {
  content: "";
  display: inline-block;
  width: 14px;
  height: 14px;
  border: 1px solid rgb(var(--color-entry-line));
  border-radius: 2px;
  margin-right: 10px;
  flex-shrink: 0;
}

.facet-checkbox > input[type="checkbox"] {
  display: none;
}

.facet-checkbox > input[type="checkbox"]:disabled ~ span {
  border-color: rgba(var(--color-text), 0.1);
  color: rgba(var(--color-text), 0.6);
  cursor: not-allowed;
}

.facet-checkbox > input[type="checkbox"]:checked ~ span {
  font-weight: 500;
}

.facet-checkbox > input[type="checkbox"]:checked ~ span::before {
  background: black;
  box-shadow: inset 0 0 0px 2px #ffffff;
}

.active-facets {
  width: 100%;
  display: flex;
  flex-wrap: wrap;
  column-gap: 10px;
}

.active-facets facet-remove {
  margin-top: 10px;
}

@media (max-width: 959px) {
  .active-facets {
    padding: 0 var(--page-padding);
  }
  .active-facets facet-remove {
    margin-top: 8px;
  }
}

.active-facets .active-facets__button {
  display: inline-flex;
}

.active-facets .active-facets__button-wrapper {
  display: flex;
  align-items: center;
}

.active-facets__button-inner {
  padding: 8px 14px;
  border-radius: 4px;
  border: 1px solid rgb(var(--color-text));
}

.active-facets__button-inner .icon-close {
  margin-right: 10px;
  flex-shrink: 0;
}

.active-facets facet-remove:only-child {
  display: none;
}

.facets__form-vertical {
  width: 256px;
}

.facets-vertical .product-list-container {
  width: 100%;
}

.disclosure-has-popup {
  position: relative;
}

.disclosure-has-popup[open] > summary:before {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 2;
  display: block;
  cursor: default;
  content: " ";
  background: transparent;
}

.facets-enabled-sticky {
  position: sticky;
  top: 0;
  left: 0;
  z-index: 1;
  background-color: rgba(var(--color-page-background));
}

.facets-enabled-sticky.improve-sticky-index {
  z-index: 100;
}

.product-list-container {
  margin-top: 20px;
}

.facets-vertical-sort .facets-vertical-form {
  display: flex;
  justify-content: space-between;
  padding: 15px 0;
}

@media (min-width: 960px) {
  .main-collection-container.facets-vertical
    .facets-wrapper.facets-enabled-sticky {
    position: static;
    z-index: 0;
  }
  .main-collection-container:not(.facets-vertical) .facets-wrapper {
    background-color: rgba(var(--color-page-background));
    margin-left: calc(-1 * var(--page-padding));
    margin-right: calc(-1 * var(--page-padding));
    padding: 8px var(--page-padding);
  }

  .product-list-container {
    margin-top: 15px;
  }
}

@media (max-width: 959px) {
  .facets-wrapper {
    background-color: rgba(var(--color-page-background));
    margin-left: calc(-1 * var(--page-padding));
    margin-right: calc(-1 * var(--page-padding));
    padding-bottom: 4px;
  }
  .facets-wrapper .product-count {
    color: rgba(var(--color-light-text));
    margin-top: 8px;
    padding: 0 var(--page-padding);
  }
  .product-list-container {
    margin-top: 20px;
  }
}

.facetSortDrawerFormPcWrapper {
  display: flex;
  align-items: center;
}

.facets-sort-filter__wrapper {
  display: flex;
  width: 100%;
  justify-content: space-between;
  align-items: center;
  position: relative;
}

@media (max-width: 959px) {
  .facets-sort-filter__wrapper {
    border-bottom: 1px solid rgba(var(--color-entry-line));
    padding: 10px 0;
  }
  .facets-sort-filter__wrapper.has-middle-line::after {
    content: "";
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: calc(100% - 20px);
    background-color: rgba(var(--color-entry-line));
  }
}

@media (max-width: 959px) {
  .facets-sort-filter__wrapper .mobile-facets__wrapper {
    flex: 1;
    padding: 0 8px;
  }
}

.facets-sort-filter__wrapper
  .mobile-facets__wrapper
  .disclosure-has-popup[open]
  > summary:before {
  z-index: 100;
}

.facets-sort-filter__wrapper .mobile-facets__wrapper .mobile-facets__open {
  display: flex;
  align-items: center;
  justify-content: center;
}

.facets-sort-filter__wrapper
  .mobile-facets__wrapper
  .mobile-facets__open
  .icon-filter {
  margin-right: 13px;
}

.facets-sort-filter__wrapper .mobile-facets__sort {
  flex: 1;
  padding: 0 8px;
}

.facets-sort-filter__wrapper
  .mobile-facets__sort
  .facets__sorting-details
  summary {
  display: flex;
  align-items: center;
  justify-content: center;
}

.facets-sort-filter__wrapper
  .mobile-facets__sort
  .facets__sorting-details
  summary
  > span {
  margin-left: 8px;
}

.facets__disclosure-vertical {
  border-bottom: 1px solid rgb(var(--color-entry-line));
}

.facets__disclosure-vertical .facets__summary {
  padding: 15px 0;
}

.facets__disclosure-vertical .facets__summary > div {
  justify-content: space-between;
}

.facets__disclosure-vertical fieldset {
  border: 0;
  margin: 0;
  padding: 0;
}

.mobile-facets {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 100;
  background-color: rgba(var(--color-mask), 0);
  pointer-events: none;
  transition: all 0.2s ease;
}

.mobile-facets .select {
  width: 100%;
}

.mobile-facets__disclosure.menu-opening .mobile-facets {
  background-color: rgba(var(--color-mask), 0.5);
}

.mobile-facets__header {
  padding: 0 30px;
}

.mobile-facets__header-inner {
  border-bottom: 1px solid rgb(var(--color-entry-line));
  padding: 30px 0 20px 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@media (max-width: 959px) {
  .mobile-facets__header-inner {
    padding: 20px 0;
  }
}

.mobile-facets__inner {
  width: 400px;
  background: rgb(var(--color-page-background));
  height: 100%;
  display: flex;
  flex-direction: column;
  pointer-events: all;
  transition: transform 0.4s ease;
}

@media screen and (max-width: 959px) {
  .mobile-facets__inner {
    width: 340px;
  }
}

.menu-opening .mobile-facets__inner {
  transform: translateX(0);
}

.disclosure-has-popup:not(.menu-opening) .mobile-facets__inner {
  transform: translateX(-105vw);
}

.mobile-facets__summary > div {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 0;
}

.mobile-facets__main {
  position: relative;
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0 30px;
}

.mobile-facets__main .mobile-facets__details {
  border-bottom: 1px solid rgb(var(--color-entry-line));
  padding-bottom: 10px;
}

.mobile-facets__footer {
  display: flex;
  align-items: stretch;
  gap: 10px;
  padding: 12px 20px 20px 20px;
  border-top: 1px solid rgb(var(--color-entry-line));
}

.mobile-facets__footer .mobile-facets__count {
  flex: 1;
  margin: 0;
  display: flex;
  align-items: center;
}

.mobile-facets__footer .button {
  width: 100px;
  word-break: break-all;
}

.mobile-facets__clear-wrapper {
  display: flex;
  align-items: stretch;
}

.mobile-facets__submenu {
  display: flex;
  flex-direction: column;
}

.mobile-facets__sort .icon-arrow {
  transform: rotate(-90deg);
}

details[open].menu-opening > .mobile-facets__submenu {
  transform: translate(0);
  visibility: visible;
}

.mobile-facets details[open] .icon-arrow,
.facets__form-vertical details[open] .icon-arrow {
  transform: rotate(180deg);
}

.facets__form details[open] .facets__summary span {
  text-decoration: underline;
}

.facets__form details[open] .icon-arrow {
  transform: rotate(180deg);
}

.mobile-facets__close {
  display: flex;
  padding: 8px;
  cursor: pointer;
}

.mobile-facets__close .icon-close {
  width: 12px;
  height: 12px;
}

.facets-container-drawer .facets__form {
  margin-bottom: 0;
}

.facets-container-drawer .facets__form facet-remove:not(:only-child) {
  display: inline-block;
  margin-bottom: 42px;
}

.mobile-facets__sort .icon-arrow {
  width: 10px;
}

.select {
  position: relative;
}

.select .icon-arrow {
  position: absolute;
  top: calc(50% - 5px);
  pointer-events: none;
}

.facet-filters {
  display: flex;
  justify-content: flex-end;
  align-items: flex-start;
}

/* custom sort select  */

.facets__sorting-details.drawer-pc-sorting {
  margin-right: 70px;
}

.facets__sorting-details .facets__sorting-content {
  width: 240px;
  padding: 20px;
  left: initial;
  right: 0;
}

.facets__sorting-details .facets__sorting-content .sort-option {
  position: relative;
  padding: 5px 0;
  margin-top: 8px;
  display: flex;
  align-items: center;
  cursor: pointer;
}

.facets__sorting-details .facets__sorting-content .sort-option > svg {
  color: inherit;
  margin-right: 12px;
  width: 16px;
  height: 16px;
  visibility: hidden;
}

.facets__sorting-details .facets__sorting-content .sort-option input {
  opacity: 0;
  width: 0;
  height: 0;
  margin: 0;
  padding: 0;
}

.facets__sorting-details
  .facets__sorting-content
  .sort-option
  input[type="radio"]:checked
  + svg {
  visibility: visible;
}

.facets__sorting-details .facets__sorting-content .sort-option:first-of-type {
  margin-top: 0;
}

/* mobile drawer mode */

.facets__sorting-details .modal__content {
  top: initial;
  transform: translateY(100%);
  --modal-animation-name: modalSlideInBottom;
}

.facets__sorting-details .modal__content .facets__sorting-content {
  width: 100%;
}

.facets__sorting-details
  .modal__content
  .facets__sorting-content
  .facets__sorting-title-line {
  height: 1px;
  width: 100%;
  background-color: rgb(var(--color-entry-line));
  margin: 20px 0 10px 0;
}

.facets__sorting-details .modal__content .facets__sorting-content .sort-option {
  margin-top: 10px;
}

.facets__sorting-details[open] .modal__content {
  transform: translateY(0);
}

.product-count.loading > span {
  display: none;
}

a.facets__reset,
a.facets__reset:hover {
  font-weight: 500;
  text-decoration: underline;
  text-underline-offset: 6px;
  white-space: nowrap;
}

@media screen and (min-width: 960px) {
  .facets-vertical {
    display: flex;
  }
  .facets-vertical .facets-wrapper {
    padding-right: 30px;
    word-break: break-all;
  }
  .facets-vertical .facets-wrapper.facets-wrapper--no-filters {
    display: none;
  }

  .facets-container-drawer {
    display: flex;
    flex-flow: row wrap;
    column-gap: 0;
  }
}

.stock-filter {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 5px 0;
}

/* width: fit-content; */

.stock-filter > .field-switch {
  flex: auto 0 0;
}

.price-range-container {
  width: 340px;
}

@media screen and (max-width: 959px) {
  .price-range-container {
    width: 280px;
  }
}

.price-range-container .price-range-input {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

.price-range-container .price-range-input .field {
  margin: 0;
  padding: 0 12px;
  line-height: 1;
}

.price-range-container .price-range-input .field-currency {
  opacity: 0.5;
  margin-right: 5px;
  flex-shrink: 0;
}

.price-range-container .price-range-input .line {
  width: 10px;
  background: rgb(var(--color-entry-line));
  height: 1px;
  margin: 0 8px;
  flex-shrink: 0;
}

.price-range-container .price-range-input .field__input {
  height: 42px;
  padding: 0;
}

.price-range-container .price-range-slider {
  position: relative;
  padding: 8px 0px;
}

.price-range-container .price-range-slider .price-range-track {
  position: relative;
  height: 4px;
  background-color: rgba(var(--color-text), 0.1);
}

.price-range-container .price-range-slider .price-range-bar {
  position: absolute;
  top: 0;
  width: 100%;
  height: 100%;
  background-color: rgb(var(--color-text));
}

.price-range-container .price-range-slider .price-range-dot {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 16px;
  height: 16px;
  background-color: rgb(var(--color-page-background));
  border: 3px solid rgb(var(--color-text));
  border-radius: 16px;
}

.price-range-container
  .price-range-slider
  .price-range-dot.price-range-dot--min {
  left: 0;
  transform: translate(0, -50%);
}

.price-range-container
  .price-range-slider
  .price-range-dot.price-range-dot--max {
  right: 0;
  transform: translate(0, -50%);
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
