.cart-notification-wrapper {
  position: relative;
  z-index: 10;
}

.cart-notification-wrapper .cart-notification {
  display: block;
}

.cart-notification {
  padding: 40px;
  position: absolute;
  right: 0;
  transform: translateY(-100%);
  visibility: hidden;
  width: 100%;
  box-shadow: 0px 10px 20px rgba(var(--color-text), 0.1);
  background-color: rgb(var(--color-page-background));
  max-width: 480px;
}

.cart-notification.animate {
  transition: transform 100ms ease, visibility 0s 100ms ease;
}

.cart-notification.active {
  transform: translateY(0);
  transition: transform 200ms ease, visibility 0s;
  visibility: visible;
}

.cart-notification__header {
  align-items: flex-start;
  display: flex;
}

.cart-notification__heading {
  align-items: center;
  display: flex;
  flex-grow: 1;
  margin-bottom: 0;
  margin-top: 0;
}

.cart-notification__heading .icon-check {
  color: rgb(var(--color-text));
  margin-right: 6px;
  width: 20px;
}

.cart-notification__close {
  border: none;
  box-shadow: none;
  text-decoration: underline;
  background: transparent;
  width: 20px;
  height: 20px;
  display: block;
  cursor: pointer;
  position: absolute;
  top: 20px;
  right: 20px;
}

#cart-notification-subtotal {
  display: flex;
  align-items: center;
  margin-bottom: 20px;
}

#cart-notification-subtotal em {
  margin-right: 10px;
  color: rgb(var(--color-light-text));
}

.cart-notification__close svg {
  width: 100%;
  height: 100%;
}

.cart-notification__links .button {
  width: 100%;
  height: 100%;
}

.cart-notification__links > * {
  margin-top: 10px;
}

.cart-notification-product {
  align-items: flex-start;
  display: flex;
  padding-bottom: 50px;
  padding-top: 35px;
}

.cart-notification-product__image {
  width: 100px;
  flex-shrink: 0;
  margin-right: 30px;
}

.cart-notification-product__image img {
  width: 100%;
  display: block;
  height: auto;
}

.cart-notification-product__image .placeholder {
  background-color: rgb(var(--color-image-background));
}

.cart-notification-product__image:after {
  content: none;
}

.cart-notification-product__name,
.product-option {
  margin-bottom: 8px;
  word-break: break-all;
}

.cart-notification-product__details .product-property {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 2px;
}

.cart-notification-product__details .product-property .product-property__name {
  flex-shrink: 0;
  max-width: 100%;
}

.cart-notification-product__details .product-property .product-property__value {
  display: flex;
  flex-wrap: wrap;
}

.cart-notification-product__details .product-property .product-property__link {
  display: block;
}

.cart-notification-product__details .product-property .product-property__image {
  display: block;
  margin-right: 5px;
  width: 30px;
  height: 30px;
  object-fit: contain;
}

@media (min-width: 960px) {
  .cart-notification {
    right: 30px;
  }
}

@media (max-width: 959px) {
  .cart-notification {
    max-width: 100vw;
    padding: 40px 20px;
  }
  #cart-notification-subtotal {
    justify-content: center;
  }
  .cart-notification__links > * {
    margin: 0 5px;
    flex: 1;
  }
  .cart-notification__links {
    display: flex;
    margin: 0 -5px;
  }
  .cart-notification__heading {
    justify-content: center;
  }
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
