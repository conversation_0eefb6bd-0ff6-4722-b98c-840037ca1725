.footer {
  background-color: rgb(var(--color-page-background));
}

.footer-block:empty {
  display: none;
}

.footer-block__heading {
  font-weight: 500;
}

.footer-block--full {
  margin-top: 40px;
}

@media (max-width: 959px) {
  .footer-block--full {
    margin-top: 20px;
  }
}

.footer-block--full {
  width: 100%;
}

.footer-block--full .footer-block-image img {
  width: 100%;
  height: auto;
  display: block;
}

.footer-block__details-content.rte,
.footer-block__details-content > li {
  margin-top: 15px;
}

.footer-block__details-content a {
  opacity: 0.7;
}

footer-menu {
  display: flex;
  flex-direction: column;
}

.footer-block__details-newsletter-form {
  margin-top: 15px;
}

.footer-block__details-newsletter-form .field__suffix .button {
  padding: 0;
  padding-bottom: 0;
}

.footer-block__details-newsletter-form .field__suffix .button:after {
  content: none;
}

.footer-block__details-newsletter-form:after {
  border-radius: 4px;
}

.footer-block__social_media-container {
  display: flex;
  gap: 8px;
  list-style: none;
  padding-left: 0;
  margin-top: 20px;
  flex-wrap: wrap;
}

@media (min-width: 960px) {
  .footer-block--list .footer-block__heading svg {
    display: none;
  }
}

@media (max-width: 959px) {
  footer-menu {
    overflow: hidden;
    position: relative;
    padding-bottom: var(--grid-vertical-space);
  }

  footer-menu .footer-block__heading {
    display: flex;
    align-items: center;
    justify-content: space-between;
    color: rgba(var(--color-text));
  }

  footer-menu ul {
    height: 0;
    opacity: 0;
  }

  footer-menu .icon-arrow {
    display: inline-block;
  }

  footer-menu .icon-minus {
    display: none;
  }

  footer-menu::after {
    position: absolute;
    content: " ";
    bottom: 0;
    left: 0;
    width: 100%;
    height: 1px;
    background-color: rgba(var(--color-text), 0.1);
  }
  footer-menu[open] ul {
    opacity: 1;
    height: auto;
  }

  footer-menu[open] .icon-arrow {
    display: none;
  }

  footer-menu[open] .icon-minus {
    display: inline-block;
  }
}

.footer-block__details-content .list-menu__item--active,
.footer-block__details-content .list-menu__item--link:hover {
  text-decoration: underline;
  text-underline-offset: 6px;
  color: rgba(var(--color-text));
  opacity: 1;
}

.footer-block-image > img {
  height: auto;
  object-fit: cover;
  object-fit: center center;
}

@media (max-width: 959px) {
  .footer-block-image {
    text-align: left !important;
  }
}

.footer__copyright {
  text-align: center;
  margin-top: 10px;
}

.footer-block--newsletter {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  align-items: center;
}

@media (min-width: 960px) {
  .footer-block--newsletter {
    margin-top: 40px;
    margin-bottom: 40px;
  }

  .footer-block--newsletter .field {
    width: 410px;
  }
}

@media (max-width: 959px) {
  .footer-block--newsletter {
    margin-top: 20px;
  }

  .footer-block--subscribe {
    width: 100%;
  }

  .footer-block--newsletter .field {
    width: 100%;
  }

  .footer__list-social {
    margin-bottom: 20px;
  }
}

.footer-block--subscribe .footer-block__heading {
  margin-bottom: 15px;
}

.footer-block__desc {
  color: rgba(var(--color-text));
  margin-top: 2px;
}

.footer__list-social {
  display: flex;
  gap: 8px;
}

.footer__list-social a svg {
  transition: transform 125ms cubic-bezier(0.4, 0, 0.2, 1);
}

.footer__list-social a:hover svg {
  transform: scale(1.1);
}

.footer__content-bottom {
  padding-top: 40px;
}

@media (min-width: 960px) {
  .footer__content-bottom {
    padding-top: 40px;
  }
}

@media (max-width: 959px) {
  .footer__content-bottom {
    padding-top: 20px;
  }
}

.footer__content-bottom-wrapper {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 20px;
}

.footer__column--info {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: flex-end;
}

@media (min-width: 960px) {
  .footer__column--info {
    max-width: 30%;
  }
}

@media (max-width: 959px) {
}

@keyframes animateLocalization {
  0% {
    opacity: 0;
    transform: translateY(0);
  }

  100% {
    opacity: 1;
    transform: translateY(-10px);
  }
}

.footer__localization form {
  display: flex;
  gap: 20px;
  justify-content: center;
  flex-wrap: wrap;
}

.footer__localization .dropdown-menu__list-wrapper {
  opacity: 1;
  animation: animateLocalization 200ms ease;
  transform: translateY(-10px);
}

.footer__payment {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.footer__payment > svg {
  height: 22px;
  width: 35px;
}

@media (max-width: 959px) {
  .footer__content-bottom-wrapper {
    flex-direction: column;
  }

  .footer__column--info {
    align-items: center;
  }

  .footer__copyright {
    margin-top: 20px;
  }

  .footer__payment {
    justify-content: center;
  }
  .footer__localization form {
    gap: 6px;
  }
  .footer__localization dropdown-menu:last-child .dropdown-menu__list-wrapper {
    right: 0;
  }
}

@media (max-width: 959px) {
  .footer-block {
    width: 100% !important;
  }
}

.footer .newsletter-form__button.newsletter-form__button {
  padding: 10px;
  padding-right: 0;
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
