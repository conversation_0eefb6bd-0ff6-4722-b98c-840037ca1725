.cart-item {
  display: flex;
  padding: 40px 0;
  border-bottom: 1px solid rgb(var(--color-entry-line));
}

.cart-item__media {
  width: 140px;
  flex-shrink: 0;
  margin-right: 30px;
}

.cart-item__media .cart-item__image-container {
  display: block;
  outline: none;
  width: 100%;
}

.cart-item__media .cart-item__image-container .placeholder {
  background-color: rgb(var(--color-image-background));
}

.cart-item__media img {
  width: 100%;
  height: auto;
  display: block;
}

.cart-item__name {
  text-decoration: none;
  color: rgb(var(--color-text));
  margin-bottom: 4px;
  display: inline-block;
}

.cart-item__details__wrapper {
  display: flex;
  flex-grow: 1;
}

.cart-item__details {
  /* min-width: 294px; */
  flex-grow: 1;
}

.cart-item__details .product-option {
  margin-bottom: 2px;
}

.cart-item__details .product-option span:first-child {
  font-weight: 600;
}

.cart-item__details .product-property {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 2px;
}

.cart-item__details .product-property .product-property__name {
  flex-shrink: 0;
  max-width: 100%;
}

.cart-item__details .product-property .product-property__value {
  display: flex;
  flex-wrap: wrap;
}

.cart-item__details .product-property .product-property__link {
  display: block;
}

.cart-item__details .product-property .product-property__image {
  display: block;
  margin-right: 5px;
  width: 30px;
  height: 30px;
  object-fit: contain;
}

.cart-item__quantity {
  width: 20.8%;
  flex-shrink: 0;
  margin: 0 20px;
}

.cart-item__quantity .cart-item__quantity-wrapper {
  display: flex;
  align-items: center;
  height: 41px;
}

.cart-item__quantity .quantity {
  width: 125px;
  height: 41px;
  margin-right: 22px;
  min-height: unset;
}

.cart-item__quantity cart-remove-button {
  line-height: 0;
}

.cart-item__quantity cart-remove-button svg {
  width: 16px;
  height: 16px;
  color: rgba(var(--color-text), 0.6);
  cursor: pointer;
  display: block;
}

.cart-item__quantity cart-remove-button:hover svg {
  color: rgba(var(--color-text), 1);
}

.cart-item__error {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.cart-item__error-text {
  order: 1;
}

.cart-item__error .icon-error {
  margin-right: 5px;
  margin-top: 1.5px;
  flex-shrink: 0;
}

.cart-item__error-text:empty + svg {
  display: none;
}

.cart-item__totals {
  width: 18.4%;
  flex-shrink: 0;
  position: relative;
}

.cart-item__price-wrapper {
  text-align: right;
}

.cart-item__totals .original__price {
  color: rgb(var(--color-light-text));
  font-size: 13px;
  line-height: 1.6;
  text-decoration: line-through;
}

.cart-item__totals .final__price {
  color: rgb(var(--color-text));
  word-break: break-all;
}

.cart-item__discounts {
  margin: 0;
  padding: 0;
}

.cart-item__discounts li {
  list-style: none;
  color: rgb(var(--color-discount));
  display: flex;
  align-items: center;
}

.cart-item__discounts li span {
  margin-left: 4px;
}

@media (max-width: 959px) {
  .cart-item {
    padding: 20px 0;
  }
  .cart-item__media {
    width: 80px;
    margin-right: 16px;
  }
  .cart-item__details__wrapper {
    display: block;
  }
  .cart-item__quantity {
    width: auto;
    margin: 12px 0;
  }
  .cart-item__totals {
    width: auto;
  }
  .cart-item__price-wrapper {
    text-align: left;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
  }
  .cart-item__price-wrapper .final__price {
    margin-right: 6px;
  }
  .original__price__desktop {
    display: none;
  }
  .cart-item .loading-overlay {
    left: 0;
  }
}

@media (min-width: 960px) {
  .original__price__mobile {
    display: none;
  }
  .cart-item .loading-overlay .loading-overlay__spinner svg {
    width: 25px;
    height: 25px;
  }
  .cart-item__quantity {
    min-width: 200px;
  }
  .cart-item__totals {
    min-width: 140px;
  }
}

/* loading */

.cart-item .loading-overlay {
  top: 0;
  right: 0;
  position: absolute;
  line-height: 0;
}

.cart-item .loading-overlay .loading-overlay__spinner {
  animation-duration: 1s;
}

.cart-item .loading-overlay.loading ~ * {
  visibility: hidden;
}

.cart__items--disabled {
  pointer-events: none;
}

/* cart drawer case */

.cart-drawer__items .cart-item {
  padding: 20px 0;
  border-bottom: none;
}

.cart-drawer__items .cart-item__media {
  width: 80px;
  margin-right: 16px;
}

.cart-drawer__items .cart-item__details__wrapper {
  display: block;
}

.cart-drawer__items .cart-item__quantity {
  max-width: none;
  margin: 0;
}

.cart-drawer__items .cart-item__totals {
  max-width: none;
}

.cart-drawer__items .cart-item__price-wrapper {
  text-align: left;
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.cart-drawer__items .cart-item__price-wrapper .final__price {
  margin-right: 6px;
}

.cart-drawer__items .original__price__desktop {
  display: none;
}

.cart-drawer__items .original__price__mobile {
  display: block;
}

.cart-drawer__items .cart-item .cart-item__totals .loading-overlay {
  left: 0;
}

.cart-drawer__items .cart-item__totals {
  width: auto;
}

@media (max-width: 959px) {
  .cart-drawer__items .cart-item__totals,
  .main-cart-items-wrapper .cart-item__totals {
    margin: 10px 0;
  }

  .cart-drawer__items .cart-item__quantity,
  .main-cart-items-wrapper .cart-item__quantity {
    margin: 0;
  }
  .cart-drawer__items .cart-item__discounts li + li,
  .main-cart-items-wrapper .cart-item__discounts li + li {
    margin-top: 2px;
  }

  .cart-drawer__items .cart-item__error,
  .main-cart-items-wrapper .cart-item__error {
    margin-top: 10px;
  }
  .cart-drawer__items .cart-item__details .product-option:last-child,
  .main-cart-items-wrapper .cart-item__details .product-option:last-child {
    margin-bottom: 0;
  }
}

@media (min-width: 960px) {
  .cart-drawer__items .cart-item__discounts {
    margin-bottom: 10px;
  }

  .cart-drawer__items .cart-item__discounts li + li {
    margin-top: 2px;
  }

  .cart-drawer__items .cart-item__quantity .cart-item__quantity-wrapper {
    margin: 12px 0;
  }
  .cart-drawer__items .cart-item__details .product-option:last-child {
    margin-bottom: 0;
  }
}

/* moq */

.cart-item__volume-pricing-wrapper {
  position: relative;
}

.cart-item__volume-pricing-title {
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.cart-item__volume-pricing-title > span {
  line-height: 1;
  margin-left: 8px;
}

.cart-item__quantity-rules {
  padding: 4px 20px;
}

cart-item-quantity volume-pricing {
  display: block;
  max-height: 240px;
  overflow-y: auto;
  -webkit-overflow-scrolling: touch;
  position: absolute;
  z-index: 2;
  left: 0;
  width: 100%;
  margin: 2px 0;
  padding: 10px 0;
  background-color: rgba(var(--color-page-background));
}

@media (min-width: 960px) {
  cart-item-quantity volume-pricing {
    width: 245px;
  }
}

cart-item-quantity volume-pricing ul li {
  padding: 8px 20px;
  display: flex;
  justify-content: space-between;
}

cart-item-quantity volume-pricing ul li:nth-child(odd) {
  background: rgba(var(--color-text), 0.03);
}

cart-item-quantity volume-pricing ul li:nth-child(even) {
  background: rgba(var(--color-text), 0.09);
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
