.icon-list__title {
  padding-bottom: 40px;
}

.icon-list__content__item img {
  object-fit: contain;
  width: 100%;
  height: 100%;
}

.icon-list__content__item-container {
  position: relative;
  padding-bottom: 100%;
  height: 0;
}

.icon-list__content__item-container > div {
  position: absolute;
  left: 0;
  right: 0;
  top: 0;
  bottom: 0;
}

.icon-list__content__item-container .placeholder {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: rgb(var(--color-image-background));
}

@media (max-width: 959px) {
  .icon-list__content__item-wrapper {
    flex-wrap: nowrap;
    overflow-x: auto;
    overflow-y: hidden;
  }
  .icon-list__content__item {
    width: calc((200vw - var(--page-padding)) / 5);
  }
  .icon-list {
    overflow: hidden;
    padding-left: 20px;
    padding-right: 20px;
  }
  .icon-list__title {
    padding-bottom: 12px;
  }
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
