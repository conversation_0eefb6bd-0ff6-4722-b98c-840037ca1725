/*.main-password*/
.main-password .password-body {
  height: 100%;
  flex-grow: 1;
}
/*Prevents flash when updating*/
.main-password .password-body .password .field {
  transition: unset;
}
.main-password .password-body .password-content {
  text-align: center;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}
@media only screen and (max-width: 959px) {
  .main-password .password-body .password-content {
    margin-bottom: 18px;
    margin-top: 10px;
  }
}
.main-password .password-body .password {
  display: flex;
  flex-direction: row;
  position: relative;
  flex-grow: 1;
}
@media only screen and (max-width: 959px) {
  .main-password .password-body .password {
    flex-direction: column;
  }
}
.main-password .password-body .password__media {
  height: 100%;
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  overflow: hidden;
}
.main-password .password-body .password__media > img {
  object-fit: cover;
  object-position: center center;
  transition: opacity 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}
.main-password .password-body .password:after,
.main-password .password-body .password__media:after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  background: #000000;
  opacity: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
}
@media screen and (max-width: 959px) {
  .main-password .password-body .password__media {
    position: relative;
  }
  .main-password
    .password-body
    .password--mobile-bottom.password--adapt
    .password__media {
    height: 390px;
  }
  .main-password
    .password-body
    .password--small:not(.password--adapt)
    .password__media {
    height: 280px;
  }
  .main-password
    .password-body
    .password--medium:not(.password--adapt)
    .password__media {
    height: 340px;
  }
  .main-password
    .password-body
    .password--large:not(.password--adapt)
    .password__media {
    height: 390px;
  }
  .main-password
    .password-body
    .password--small:not(.password--mobile-bottom):not(.password--adapt)
    .password__content {
    height: 280px;
  }
  .main-password
    .password-body
    .password--medium:not(.password--mobile-bottom):not(.password--adapt)
    .password__content {
    height: 340px;
  }
  .main-password
    .password-body
    .password--large:not(.password--mobile-bottom):not(.password--adapt)
    .password__content {
    height: 390px;
  }
}
@media screen and (min-width: 960px) {
  .main-password .password-body .password {
    flex-direction: row;
  }

  .main-password .password-body .password--small:not(.password--adapt) {
    min-height: 420px;
  }

  .main-password .password-body .password--medium:not(.password--adapt) {
    min-height: 560px;
  }

  .main-password .password-body .password--large:not(.password--adapt) {
    min-height: 720px;
  }

  .main-password .password-body .password__content.password__content--top-left {
    align-items: flex-start;
    justify-content: flex-start;
  }

  .main-password
    .password-body
    .password__content.password__content--top-center {
    align-items: flex-start;
    justify-content: center;
  }

  .main-password
    .password-body
    .password__content.password__content--top-right {
    align-items: flex-start;
    justify-content: flex-end;
  }

  .main-password
    .password-body
    .password__content.password__content--middle-left {
    align-items: center;
    justify-content: flex-start;
  }

  .main-password
    .password-body
    .password__content.password__content--middle-center {
    align-items: center;
    justify-content: center;
  }
  .main-password
    .password-body
    .password__content.password__content--middle-right {
    align-items: center;
    justify-content: flex-end;
  }

  .main-password
    .password-body
    .password__content.password__content--bottom-left {
    align-items: flex-end;
    justify-content: flex-start;
  }

  .main-password
    .password-body
    .password__content.password__content--bottom-center {
    align-items: flex-end;
    justify-content: center;
  }

  .main-password
    .password-body
    .password__content.password__content--bottom-right {
    align-items: flex-end;
    justify-content: flex-end;
  }
}
.main-password .password-body .password__content {
  padding: 0;
  display: flex;
  position: relative;
  width: 100%;
  align-items: center;
  justify-content: center;
  z-index: 2;
  padding: 50px;
}
@media screen and (max-width: 959px) {
  .main-password .password-body .password__content {
    padding: 0;
  }
}
.main-password .password-body .password__wrapper {
  max-width: 710px;
  min-width: 450px;
  padding: 40px 35px;
  position: relative;
  height: fit-content;
  align-items: center;
  width: 100%;
  word-wrap: break-word;
  z-index: 1;
  background: rgba(var(--color-page-background));
}
@media screen and (max-width: 959px) {
  .main-password .password-body .password__wrapper {
    min-width: 100%;
    padding: 40px 20px;
  }
}
/*transfrom*/
@media screen and (min-width: 960px) {
  .main-password
    .password-body
    .password--desktop-transparent:not(.password__box--no-image)
    .password__wrapper {
    background: transparent;
    color: #fff;
    --color-text: 255, 255, 255;
    --color-button: 255, 255, 255;
    --color-button-text: 0, 0, 0;
    max-width: 890px;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }
  .main-password .password-body .password--desktop-transparent .field {
    background: transparent;
  }
  .main-password .password-body .password--desktop-transparent .field__label {
    color: rgba(var(--color-text));
  }

  .main-password
    .password-body
    .password--desktop-transparent
    .button--secondary {
    --color-button: 255, 255, 255;
    --color-button-text: 255, 255, 255;
    --alpha-button-background: 0;
  }

  .main-password
    .password-body
    .password--desktop-transparent
    .content-container:after {
    display: none;
  }
}
/*align*/
@media screen and (min-width: 960px) {
  .main-password .password-body .password--content-align-left {
    text-align: left;
  }
  .main-password .password-body .password--content-align-center {
    text-align: center;
  }
  .main-password .password-body .password--content-align-right {
    text-align: right;
  }
}
@media screen and (max-width: 959px) {
  .main-password .password-body .password--content-align-mobile-left {
    text-align: left;
  }
  .main-password .password-body .password--content-align-mobile-center {
    text-align: center;
  }
  .main-password .password-body .password--content-align-mobile-right {
    text-align: right;
  }
}
/*password--mobile-bottom */
@media screen and (max-width: 959px) {
  .main-password
    .password-body
    .password:not(.password--mobile-bottom)
    .password__media {
    position: absolute;
  }
  .main-password
    .password-body
    .password:not(.password--mobile-bottom):not(.password__box--no-image)
    .password__wrapper {
    color: #fff;
    background-color: transparent;
    --color-text: 255, 255, 255;
    --color-button: 255, 255, 255;
    --color-button-text: 0, 0, 0;
    border: none;
    border-radius: 0;
    box-shadow: none;
  }
  .main-password .password-body .password:not(.password--mobile-bottom) .field {
    background: transparent;
  }
  .main-password
    .password-body
    .password:not(.password--mobile-bottom)
    .field__label {
    color: rgba(var(--color-text));
  }

  .main-password
    .password-body
    .password:not(.password--mobile-bottom)
    .button--secondary {
    --color-button: 255, 255, 255;
    --color-button-text: 255, 255, 255;
    --alpha-button-background: 0;
  }

  .main-password
    .password-body
    .password:not(.password--mobile-bottom)
    .content-container:after {
    display: none;
  }
}
.main-password .password-body .password__wrapper > * {
  margin-bottom: 20px;
}
.main-password .password-body .password__wrapper > *:last-child {
  margin-bottom: 0;
}
.main-password .password-body .newsletter-form {
  max-width: 360px;
  display: inline-block;
  width: 100%;
}
.main-password .password-body .newsletter-form .field__label {
  text-align: left;
}
.main-password .password-body .newsletter-form .field__input {
  height: 50px;
}
/* The ipad end responds to the mobile end in vertical screen */
/* @custom-media --tablet (max-width: 959px); */
/* @custom-media --gt-mobile (min-width: 751px); */
/* detectingScreen need to consider the configuration of the tablet */
