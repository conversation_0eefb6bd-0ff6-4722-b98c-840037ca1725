.pagination-wrapper {
  margin-top: 40px;
}

.pagination {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.simple-pagination__list {
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 4px;
  border: 1px solid rgb(var(--color-entry-line));
  padding: 8px 10px;
}

.simple-pagination__list .pagination__item.pagination__item-arrow {
  color: rgb(var(--color-button-text));
  width: 24px;
  height: 24px;
}

.simple-pagination__list .pagination__item.pagination__item-arrow svg {
  color: rgb(var(--color-button-background));
}

.simple-pagination__list .pagination__item--disible {
  pointer-events: none;
}

.simple-pagination__list .pagination__item--disible:hover {
  background-color: unset;
}

.simple-pagination__list .pagination__item--disible .icon {
  opacity: 0.5;
  cursor: not-allowed;
}

.simple-pagination__list .pagination__item--disible .icon:hover {
  opacity: 0.5;
}

.simple-pagination__list .pagination__num--container {
  color: rgba(var(--color-text), #000);
  text-align: center;
  padding: 0 20px;
}

.simple-pagination-info {
  color: var(--color-entry-line, #000);
  text-align: center;
  margin-top: 12px;
}

.normal-pagination__list {
  display: flex;
  align-items: center;
  column-gap: 10px;
}

.normal-pagination__list .pagination__item {
  padding: 8px;
  position: relative;
}

.normal-pagination__list
  .pagination__item:not(.pagination__item-arrow, .pagination__item--current) {
  color: rgba(var(--color-light-text));
}

.normal-pagination__list .pagination__item.pagination__item-arrow {
  background-color: rgb(var(--color-button-background));
  color: rgb(var(--color-button-text));
  border-radius: 50%;
  width: 40px;
  height: 40px;
}

.normal-pagination__list .pagination__item.pagination__item-arrow:hover {
  color: rgb(var(--color-button-text));
}

.normal-pagination__list .pagination__item.pagination__item--current::after {
  content: "";
  position: absolute;
  left: 0;
  bottom: 0;
  width: 100%;
  height: 2px;
  background-color: rgba(var(--color-text));
}

.normal-pagination__list .pagination__item.pagination__item--prev {
  margin-left: 10px;
}

.normal-pagination__list .pagination__item.pagination__item--prev:hover .icon {
  transform: rotate(-90deg) scale(1.07);
}

.normal-pagination__list .pagination__item.pagination__item--next {
  margin-right: 10px;
}

.normal-pagination__list .pagination__item.pagination__item--next:hover .icon {
  transform: rotate(90deg) scale(1.07);
}

.pagination__item {
  color: rgb(var(--color-text));
  display: inline-flex;
  justify-content: center;
  align-items: center;
  position: relative;
  padding: 0;
  text-decoration: none;
}

.pagination__item:hover {
  background: rgba(0, 0, 0, 0.05);
}

a.pagination__item--current,
a.pagination__item:hover {
  opacity: 1;
}

.pagination__item--next .icon {
  transform: rotate(90deg);
}

.pagination__item--prev .icon {
  transform: rotate(-90deg);
}

@media screen and (max-width: 959px) {
  .pagination-wrapper {
    margin-top: 20px;
  }
  .simple-pagination__list {
    padding: 4px 5px;
  }
  .simple-pagination__list .pagination__num--container {
    padding: 0 16px;
  }
  .simple-pagination-info {
    margin-top: 6px;
  }
  .normal-pagination__list .pagination__item.pagination__item--prev {
    margin-left: 6px;
  }
  .normal-pagination__list .pagination__item.pagination__item--next {
    margin-right: 6px;
  }
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
