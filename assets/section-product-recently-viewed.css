product-recently-viewed {
  display: block;
}

.product-recently-viewed-title.large {
  font-size: calc(var(--title4-font-size) * 1.2);
}

.product-recently-viewed-title.small {
  font-size: calc(var(--title4-font-size) * 0.8);
}

.product-recently-viewed-title.medium {
  font-size: calc(var(--title4-font-size) * 1);
}

@media (min-width: 960px) {
  .slider-product-recently-viewed.no-slider-pc > .slider {
    flex-wrap: wrap;
  }
  .slider-product-recently-viewed.no-slider-pc
    .slider-product-recently-viewed__button {
    display: none;
  }
}

.slider-product-recently-viewed.no-slider-mobile > .slider {
  flex-wrap: wrap;
}

.slider-product-recently-viewed.no-slider-mobile
  .slider-product-recently-viewed__button {
  display: none;
}

@media (max-width: 959px) {
  .slider-product-recently-viewed > .slider.slider--mobile {
    margin-left: calc(var(--page-padding) * -1) !important;
    margin-right: calc(var(--page-padding) * -1) !important;
    padding-left: var(--page-padding);
    padding-right: var(--page-padding);
    scroll-padding-left: var(--page-padding);
  }

  .slider-product-recently-viewed > .slider.slider--mobile > .slider__slide {
    width: calc((200vw - var(--page-padding)) / (2 * var(--mobile-cols) + 1));
  }
}

.slider-product-recently-viewed__button {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background-color: rgba(var(--color-page-background));
  border: 1px solid rgba(var(--color-entry-line));
  cursor: pointer;
  color: rgba(var(--color-text));
}

.slider-product-recently-viewed__button.previous {
  left: 0;
  transform: translate(-50%, -50%) rotate(90deg);
}

.slider-product-recently-viewed__button.next {
  right: 0;
  transform: translate(50%, -50%) rotate(-90deg);
}

.slider-product-recently-viewed__button:disabled {
  color: rgba(var(--color-text), 0.3);
}

.slider-product-recently-viewed__button:hover:not(:disabled) {
  border-color: rgba(var(--color-text));
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
