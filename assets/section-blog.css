.simple-blog__tag {
  font-size: 14px;
  opacity: 0.6;
}

.blogs__list-wrapper {
  width: 100%;
}

.blogs__list {
  list-style: none;
  padding-inline-start: 0;
  margin-block-end: 0;
}

.title__align-left {
  margin-right: auto;
}

.title__align-right {
  margin-left: auto;
}

@media (min-width: 960px) {
  .blogs__list {
    flex-wrap: wrap;
  }
  .blogs__list--list .simple-blog {
    align-items: flex-start;
    display: flex;
    text-align: left;
  }
  .blogs__list--list .simple-blog__image-wrapper {
    align-self: stretch;
    margin-bottom: 0;
    margin-right: 40px;
    max-width: 458px;
    width: 35%;
  }
  .blogs__list--list .simple-blog__image-wrapper .global-image {
    height: 100%;
    width: 100%;
  }
  .blogs__list--list .simple-blog__main {
    flex: 1 0 0;
    overflow: hidden;
  }
  .blogs__list--list .simple-blog__extra-info {
    justify-content: flex-start;
  }
  .blogs__list--list .simple-blog__extra-info .text:last-child {
    flex: 1 0 0;
    max-width: none;
  }
  .blogs__list--list .simple-blog__description {
    padding: 0;
  }
}

.stage-blogs-btn {
  margin-top: 30px;
  text-align: center;
}

.blogs__head {
  align-items: center;
  display: flex;
  justify-content: center;
  margin-bottom: 40px;
}

.blogs__head--no-title {
  flex-direction: row-reverse;
}

@media (max-width: 959px) {
  .blogs__head--no-title {
    margin-bottom: 0;
  }
}

.blogs__btn-wrapper {
  margin-top: 48px;
}

.simple-blog:not(.blog-card-style-card) .simple-blog__main {
  margin-bottom: 20px;
}

@media (max-width: 959px) {
  .blogs__head {
    margin-bottom: 20px;
  }
  .simple-blog:not(.blog-card-style-card) .simple-blog__main {
    margin-bottom: 0px;
  }
  .blogs__list:not(.grid-cols-2-tablet) .blogs__item {
    width: 100%;
  }
  .slider:not(.grid-cols-2-tablet) .blogs__item {
    width: 85%;
  }
  .slider.grid-cols-2-tablet .blogs__item {
    width: calc((200vw - var(--page-padding)) / (2 * var(--mobile-cols) + 1));
  }
  .blogs__list-wrapper.blogs__list-simple .slider {
    margin-bottom: 0;
  }
  .blogs__list-wrapper .slider-buttons {
    padding: 10px 0;
  }
  /* bars pagination */
  .blogs__list-wrapper .blog__control--bars {
    margin-top: 30px;
    gap: 0;
  }

  .blogs__list-wrapper .blog__control--bars .blog__control--bar {
    transition: background 0.3s;
    background: rgba(var(--color-mask), 0.1);
    flex: 1;
    border: none;
    height: 6px;
  }
  .blogs__list-wrapper .blog__control--bars .blog__control--bar:first-child {
    border-radius: 2px 0 0 2px;
  }
  .blogs__list-wrapper .blog__control--bars .blog__control--bar:last-child {
    border-radius: 0 2px 2px 0;
  }
  .blogs__list-wrapper .blog__control--bars .blog__control--bar.is-active {
    background: rgba(var(--color-mask));
    border-radius: 2px;
  }
  /* dots pagination */
  .blogs__list-wrapper .blog__control--dots {
    column-gap: 6px;
  }
  .blogs__list-wrapper .blog__control--dots .blog__control--dot {
    display: block;
    padding: 0;
    width: 6px;
    height: 6px;
    border: none;
    border-radius: 50%;
    background: rgba(var(--color-mask), 0.1);
    transition: background 0.3s;
  }
  .blogs__list-wrapper .blog__control--dots .blog__control--dot.is-active {
    width: 10px;
    height: 10px;
    background: rgba(var(--color-mask));
  }
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
