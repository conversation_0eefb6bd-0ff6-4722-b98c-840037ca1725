.video-section__media {
  position: relative;
  padding-bottom: 56.25%;
}

.video-section__media .placeholder {
  width: 100%;
  height: 100%;
  background-color: rgb(var(--color-image-background));
  position: absolute;
  top: 0;
  left: 0;
}

.video-section__media .video-section__cover {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
}

.video-section__media.deferred-media:after {
  content: none;
}

.video-section__media iframe {
  border: 0;
}

.video-section__poster,
.video-section__media iframe,
.video-section__media video {
  position: absolute;
  width: 100%;
  height: 100%;
}

.video-section__title-wrapper {
  margin-bottom: 30px;
}

@media (max-width: 959px) {
  .video-section__title-wrapper {
    margin-bottom: 20px;
  }
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
