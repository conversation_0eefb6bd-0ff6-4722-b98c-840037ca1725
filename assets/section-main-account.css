.account-section {
  display: flex;
}
.account-section > * {
  width: 50%;
}
.account-section > div:first-child {
  margin-right: 20px;
}
.account-section .card-container + .card-container {
  margin-top: 20px;
}
.account-section .customer__delete-container .modal__toggle {
  width: max-content;
}
.account-section .customer__delete-account {
  position: relative;
  background: none;
  border: none;
  display: flex;
  align-items: center;
  padding: 0;
  margin: 0;
  color: rgb(var(--color-discount));
  cursor: pointer;
}
.account-section .customer__delete-account .icon-delete {
  margin-right: 5px;
}
.account-section .customer__delete-close {
  position: absolute;
  top: 20px;
  right: 20px;
  background: none;
  border: 1px solid rgba(var(--color-text), 0.05);
  padding: 15px;
  border-radius: 50%;
  line-height: 0;
  cursor: pointer;
}
.account-section .customer__delete-modal {
  width: 510px;
  flex-direction: column;
  justify-content: flex-start;
  max-height: max-content;
  padding: 60px 40px 40px;
}
@media (max-width: 959px) {
  .account-section .customer__delete-modal {
    width: 90%;
    padding: 50px 20px 20px;
  }
}
.account-section .customer__delete-modal h5 {
  margin-bottom: 20px;
}
.account-section .customer__delete-modal p {
  margin: 0;
  margin-bottom: 20px;
}
.account-section .customer__delete-modal ul {
  width: 100%;
  list-style-position: inside;
  padding: 0;
  margin: 0 0 20px 0;
}
.account-section .customer__delete-modal ul li + li {
  margin-top: 12px;
}
.account-section #delete-customer-form {
  width: 100%;
}
@media (max-width: 959px) {
  .account-section {
    flex-wrap: wrap;
  }
  .account-section > * {
    width: 100%;
  }
  .account-section > div:first-child {
    margin-right: 0px;
  }
  .account-section > div + div {
    margin-top: 20px;
  }
}

#account .account-edit__name--wrapper {
  display: none;
  justify-content: space-between;
  column-gap: 16px;
}

#account .account-edit__name--wrapper > div {
  flex: 1;
  color: rgb(var(--color-light-text));
}

#account .account-edit__name--wrapper > div .account-edit__name--title {
  margin-bottom: 4px;
}

#account .account-edit__name--wrapper > div input {
  -webkit-appearance: none;
  background: none;
  width: 100%;
  border: 1px solid rgb(var(--color-entry-line));
  border-radius: var(--btn-border-radius);
  padding: 5px 12px;
}

#account .account-edit__name--wrapper > div input:focus {
  border-color: rgb(var(--color-text));
}

#account.editing .username .info-label,
#account.editing .username .info-value {
  display: none;
}

@media (max-width: 959px) {
  #account.editing .phone .info-value,
  #account.editing .email .info-value {
    justify-content: space-between;
  }
}

#account.editing .account-edit__name--wrapper {
  display: flex;
}

@media (max-width: 959px) {
  #account.editing .account-edit__name--wrapper {
    display: block;
  }
  #account.editing .account-edit__name--wrapper > div {
    width: 100%;
    margin-top: 16px;
  }
  #account.editing .account-edit__name--wrapper > div:first-of-type {
    margin-top: 0;
  }
}

#account.editing .info-value__edit {
  display: flex;
}

#personal .birthday-edit {
  width: 160px;
  border: 1px solid rgb(var(--color-entry-line));
  padding: 8px 12px;
  display: none;
  align-items: center;
}

#personal .birthday-edit input {
  background: none;
  border: none;
  outline: none;
  width: 100%;
}

#personal .birthday-edit svg {
  flex-shrink: 0;
  margin-left: 8px;
}

#personal .gender-edit {
  display: none;
  line-height: 0;
}

#personal .gender-edit .gender-radio__wrapper {
  cursor: pointer;
  align-items: center;
  display: inline-flex;
}

#personal .gender-edit .gender-radio__wrapper .gender-radio {
  cursor: pointer;
  position: relative;
}

#personal .gender-edit .gender-radio__wrapper .gender-radio input {
  margin: 0;
  padding: 0;
  height: 100%;
  inset: 0;
  opacity: 0;
  position: absolute;
  width: 100%;
  z-index: 1;
  cursor: pointer;
}

#personal
  .gender-edit
  .gender-radio__wrapper
  .gender-radio
  input:checked
  + .gender-radio__inner {
  border-color: rgba(var(--color-button-background));
}

#personal
  .gender-edit
  .gender-radio__wrapper
  .gender-radio
  input:checked
  + .gender-radio__inner:after {
  opacity: 1;
  transform: scale(0.5);
}

#personal
  .gender-edit
  .gender-radio__wrapper
  .gender-radio
  .gender-radio__inner {
  display: block;
  height: 16px;
  width: 16px;
  background-color: rgba(var(--color-page-background));
  border: 1px solid rgba(var(--color-entry-line));
  border-collapse: separate;
  border-radius: 50%;
  left: 0;
  position: relative;
  top: 0;
  transition: all 0.3s;
}

#personal
  .gender-edit
  .gender-radio__wrapper
  .gender-radio
  .gender-radio__inner::after {
  height: 16px;
  width: 16px;
  background-color: rgba(var(--color-button-background));
  border-radius: 16px;
  content: "";
  left: 50%;
  margin-left: -8px;
  margin-top: -8px;
  opacity: 0;
  position: absolute;
  top: 50%;
  transform: scale(0);
  transition: all 0.3s cubic-bezier(0.78, 0.14, 0.15, 0.86);
}

#personal .gender-edit .gender-radio__wrapper .gender-radio + span {
  padding: 0 15px 0 8px;
}

#personal.editing .birthday .info-value,
#personal.editing .gender .info-value {
  display: none;
}

#personal.editing .birthday-edit {
  display: flex;
}

#personal.editing .gender-edit {
  display: block;
}

#account.editing .edit__action-buttons,
#personal.editing .edit__action-buttons {
  display: flex;
}

@media (max-width: 959px) {
  #account.editing .edit__action-buttons,
  #personal.editing .edit__action-buttons {
    flex-direction: column-reverse;
  }
  #account.editing .edit__action-buttons .button,
  #personal.editing .edit__action-buttons .button {
    display: block;
    margin-top: 12px;
  }
  #account.editing .edit__action-buttons .button:last-child,
  #personal.editing .edit__action-buttons .button:last-child {
    margin-top: 0;
  }
}

.edit__action-buttons {
  margin-top: 24px;
  padding-top: 20px;
  display: none;
  justify-content: flex-end;
  column-gap: 12px;
  border-top: 1px solid rgb(var(--color-entry-line));
}

.modify-info__button {
  text-transform: capitalize;
  padding: 8px 12px;
}

.modify-info__modal {
  width: 432px;
  max-height: max-content;
  box-sizing: content-box;
  padding: 24px;
  border-radius: 10px;
  display: block;
  background-color: rgb(var(--color-page-background));
}

@media (max-width: 959px) {
  .modify-info__modal {
    padding: 24px 20px;
    width: calc(100% - 80px);
  }
}

.modify-info__modal .modify-info__close {
  position: absolute;
  border: 1px solid rgba(var(--color-text), 0.05);
  padding: 9px;
  line-height: 0;
  border-radius: 50%;
  top: 10px;
  right: 10px;
  cursor: pointer;
  display: none;
  color: inherit;
  user-select: none;
  background-color: rgb(var(--color-page-background));
}

@media (max-width: 959px) {
  .modify-info__modal .modify-info__close {
    display: block;
  }
}

.modify-info__modal .modify-info__title {
  color: rgb(var(--color-text));
}

.modify-info__modal .modify-info__desc {
  color: rgb(var(--color-text));
  margin-top: 4px;
}

.modify-info__modal .modify-info__desc em {
  font-style: normal;
  text-decoration: underline;
}

.modify-info__modal .modify-info__content {
  margin-top: 20px;
}

.modify-info__modal .modify-info__content .modify-info__input {
  padding: 10px 16px;
  border-radius: var(--btn-border-radius);
  border: 1px solid rgb(var(--color-entry-line));
  background-color: rgb(var(--color-page-background));
  display: flex;
  align-items: center;
  margin-top: 10px;
}

.modify-info__modal .modify-info__content .modify-info__input.field-error {
  border-color: rgb(var(--color-discount));
}

.modify-info__modal .modify-info__content .modify-info__input:focus-within {
  border-color: rgb(var(--color-text));
}

.modify-info__modal .modify-info__content .modify-info__input:first-of-type {
  margin-top: 0;
}

.modify-info__modal .modify-info__content .modify-info__input.disabled {
  background-color: rgba(var(--color-text), 0.05);
}

.modify-info__modal .modify-info__content .modify-info__input input {
  width: 100%;
  background: none;
  outline: 0;
  border: none;
  flex-grow: 1;
  margin-right: 20px;
}

.modify-info__modal .modify-info__content .modify-info__input input:disabled {
  color: inherit;
}

.modify-info__modal
  .modify-info__content
  .modify-info__input
  input::placeholder {
  color: rgb(var(--color-light-text));
}

.modify-info__modal
  .modify-info__content
  .modify-info__input
  input:-webkit-autofill {
  -webkit-text-fill-color: rgba(var(--color-text));
  -webkit-box-shadow: 0 0 0 1000px rgba(var(--color-page-background)) inset !important;
}

.modify-info__modal
  .modify-info__content
  .modify-info__input
  .modify-code-select__container {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  position: relative;
}

.modify-info__modal
  .modify-info__content
  .modify-info__input
  .modify-code-select__container::before {
  position: absolute;
  left: 0;
  top: 0;
  width: 1px;
  height: 100%;
  background-color: rgba(var(--color-entry-line));
  content: "";
}

.modify-info__modal
  .modify-info__content
  .modify-info__input
  .modify-code-select__container
  .modify-code-select__wrapper {
  padding-left: 16px;
}

.modify-info__modal
  .modify-info__content
  .modify-info__input
  .modify-code-select__container
  .modify-code-select__wrapper
  span {
  padding-right: 4px;
  color: rgb(var(--color-text));
}

.modify-info__modal
  .modify-info__content
  .modify-info__input
  .modify-code-select__container
  .modify-code-select__wrapper
  svg {
  color: rgb(var(--color-light-text));
}

.modify-info__modal
  .modify-info__content
  .modify-info__input
  .modify-code-select__container
  select {
  appearance: none;
  border: none;
  bottom: 0;
  left: 0;
  opacity: 0;
  position: absolute;
  right: 0;
  top: 0;
  cursor: pointer;
}

.modify-info__modal
  .modify-info__content
  .modify-info__input
  .modify-info__send-code {
  flex-shrink: 0;
}

.modify-info__modal
  .modify-info__content
  .modify-info__input
  .modify-info__send-code
  span {
  cursor: pointer;
}

.modify-info__modal
  .modify-info__content
  .modify-info__input
  .modify-info__send-code
  em {
  font-style: normal;
  cursor: not-allowed;
  display: none;
  color: rgb(var(--color-light-text));
}

.modify-info__modal .field-error-message,
.modify-info__modal .common-error-message {
  color: rgb(var(--color-discount));
  margin-top: 6px;
}

.modify-info__modal .modify-info__action-buttons {
  display: flex;
  justify-content: flex-end;
  column-gap: 12px;
  margin-top: 30px;
}

@media (max-width: 959px) {
  .modify-info__modal .modify-info__action-buttons {
    display: block;
  }
  .modify-info__modal .modify-info__action-buttons .button {
    width: 100%;
    margin-bottom: 12px;
  }
  .modify-info__modal .modify-info__action-buttons .button:last-of-type {
    margin-bottom: 0;
  }
  .modify-info__modal .modify-info__action-buttons .button--secondary {
    display: none;
  }
}

.address--empty {
  margin-top: 58px;
  text-align: center;
}

.address--empty .icon-location {
  width: 46px;
  height: 56px;
  opacity: 0.3;
}

.address--empty .icon-plus {
  margin-right: 6px;
  margin-bottom: 1px;
}

.address--empty p {
  margin: 28px 0 20px;
  text-align: center;
}

.address--empty .button {
  margin-bottom: 40px;
}

@media (max-width: 959px) {
  .address--empty {
    margin-top: 46px;
  }
  .address--empty p {
    margin: 20px 0 10px;
  }
  .address--empty .button {
    margin-bottom: 20px;
  }
}

.address--default {
  padding: 2px 5px;
  font-weight: 700;
  font-size: 12px;
  line-height: 1.4;
  background-color: rgba(var(--color-entry-line), 0.5);
  flex-shrink: 0;
}

.address .address__item {
  padding: 24px 0;
}

.address .address__item .address__detail {
  margin-top: 20px;
}

@media (max-width: 959px) {
  .address .address__item {
    padding: 20px 0;
  }
}

.address .address__item + .address__item {
  border-top: 1px solid rgb(var(--color-entry-line));
}

.address__header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.address__header .address__btns {
  margin-top: 0;
}

.address__btns {
  display: flex;
  align-items: center;
}

.address__btns .icon {
  color: rgb(var(--color-text));
  opacity: 0.6;
}

.address__btns .address__btn:first-child {
  margin-right: 12px;
}

.address__btns .address__btn--remove {
  background: transparent;
  padding: 0;
  border: none;
  cursor: pointer;
}

.unsubscribe-info:hover .unsubscribe-button__status:first-child {
  display: none;
}

.unsubscribe-info:hover .unsubscribe-button__status:last-child {
  display: flex;
}

@media (max-width: 959px) {
  .unsubscribe-info .unsubscribe-button__status:first-child {
    display: none;
  }
  .unsubscribe-info .unsubscribe-button__status:last-child {
    display: flex;
  }
}

.unsubscribe-button {
  display: flex;
  align-items: center;
  border: none;
  background: none;
  cursor: pointer;
  color: inherit;
}

.unsubscribe-button__status {
  display: flex;
  align-items: center;
  gap: 3px;
  transition: opacity 0.2s;
}

.unsubscribe-button__status:last-child {
  display: none;
}

.unsubscribe-reasons {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.unsubscribe-reason {
  display: grid;
  grid-template-columns: auto 1fr;
  gap: 10px;
  align-items: center;
  cursor: pointer;
}

.unsubscribe-reason[checked] .unsubscribe-reason__input {
  display: block;
}

.unsubscribe-reason__input {
  display: none;
  grid-column: span 2;
  -webkit-appearance: none;
  background: none;
  width: 100%;
  border: 1px solid rgb(var(--color-entry-line));
  border-radius: 4px;
  padding: 5px 12px;
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
