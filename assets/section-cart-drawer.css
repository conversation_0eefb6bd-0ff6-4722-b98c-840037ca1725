.cart-drawer {
  position: fixed;
  z-index: 999;
  left: 0;
  top: 0;
  width: 100vw;
  height: 100%;
  display: flex;
  justify-content: flex-end;
  background-color: rgba(var(--color-mask), 0.5);
  transition: visibility 200ms ease;
}

.cart-drawer {
  visibility: hidden;
}

.cart-drawer.active {
  visibility: visible;
}

.cart-drawer__inner {
  height: 100%;
  overflow: hidden;
  transform: translateX(100%);
  transition: transform 200ms ease;
  display: flex;
  flex-direction: column;
  padding: 20px;
  background-color: rgb(var(--color-page-background));
}

.cart-drawer__fixed-checkout {
  transform: translateX(100%);
  transition: transform 200ms ease;
}

.cart-drawer__inner .cart-drawer__inner-wrapper {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.cart-drawer__warnings {
  display: flex;
  flex-direction: column;
  flex: 1;
  justify-content: center;
}

.cart-drawer.active .cart-drawer__inner,
.cart-drawer.active .cart-drawer__fixed-checkout {
  transform: translateX(0);
}

.cart-drawer__header {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20px;
}

.cart-drawer__header::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: -20px;
  width: calc(100% + 40px);
  height: 1px;
  background-color: rgb(var(--color-entry-line));
}

.cart-drawer__close {
  color: rgb(var(--color-text));
  cursor: pointer;
  width: 24px;
  height: 24px;
  line-height: 0;
  top: 2px;
  right: -4px;
  padding: 6px;
  position: absolute;
}

cart-drawer {
  display: block;
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100%;
}

.cart-drawer-container {
  width: 440px;
}

.cart-drawer__overlay {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
}

.cart-drawer__overlay:empty {
  display: block;
}

/* cart-drawer-items {
  overflow: auto;
  flex: 1;
} */

.cart-drawer__inner {
  overflow-y: scroll;
}

@media screen and (max-height: 650px) {
  cart-drawer-items {
    overflow: visible;
  }

  .cart-drawer__inner {
    overflow-y: scroll;
  }
}

@media (max-width: 959px) {
  .cart-drawer-container {
    width: 90%;
  }
}

cart-drawer-items::-webkit-scrollbar {
  width: 3px;
}

cart-drawer-items::-webkit-scrollbar-thumb {
  background-color: rgba(18, 18, 18, 0.7);
  border-radius: 100px;
}

cart-drawer-items::-webkit-scrollbar-track-piece {
  margin-top: 20px;
}

/* cart-drawer-footer */

.cart-drawer__footer__container {
  display: flex;
  justify-content: flex-end;
  padding-top: 40px;
  padding-bottom: 40px;
}

.cart-drawer__checkout-container {
  width: 100%;
  position: relative;
  padding: 40px 0;
}

.cart-drawer__checkout-container::after {
  content: "";
  position: absolute;
  top: 0;
  left: -20px;
  width: calc(100% + 40px);
  height: 1px;
  background-color: rgb(var(--color-entry-line));
}

.cart-drawer__amount-wrapper {
  margin: 0;
  padding: 0;
}

.cart-drawer__amount-wrapper li {
  list-style: none;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: rgb(var(--color-text));
  margin-bottom: 8px;
}

.cart-drawer__amount-wrapper li em {
  font-style: normal;
  margin-right: 12px;
}

.cart-drawer__amount-wrapper .cart__discount span {
  color: rgb(var(--color-discount));
}

.cart-drawer__discount span {
  color: rgb(var(--color-discount));
}

#checkout {
  width: 100%;
}

.cart-drawer__checkout {
  margin-top: 15px;
}

.cart-drawer__taxes__desc {
  text-align: center;
}

.cart-drawer__body {
  position: relative;
  display: flex;
  height: 100%;
  flex-direction: column;
}

.cart-drawer__body .cart-drawer__inner {
  flex: 1;
}

.cart-drawer__body .cart-fixed-checkout {
  position: static;
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
