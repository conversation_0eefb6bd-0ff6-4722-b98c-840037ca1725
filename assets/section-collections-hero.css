.collection-hero {
  position: relative;
  flex-direction: column;
}
.collection-hero .collection-hero__image-wrapper {
  position: relative;
  display: flex;
  height: var(--pc-collection-img-height);
  overflow: hidden;
}
@media (max-width: 959px) {
  .collection-hero .collection-hero__image-wrapper {
    height: var(--md-collection-img-height);
  }
}
.collection-hero .collection-hero__inner {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1;
}
.collection-hero .collection-hero__text-wrapper {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-direction: column;
  --color-text: 255, 255, 255;
  color: rgb(var(--color-text));
}
.collection-hero .collection-hero__image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: var(--collection-hero-cover-area);
}
.collection-hero .collection-hero__image-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: var(--collection-hero-mask-color);
  opacity: var(--collection-hero-mask-color-opacity);
}
.collection-hero .collection-hero__description {
  margin-top: 20px;
}
@media (max-width: 959px) {
  .collection-hero .collection-hero__description {
    margin-top: 10px;
  }
}
.collection-hero .breadcrumb {
  margin-top: 0;
  margin-bottom: 10px;
}

.collection-hero:not(.has-collection-image) .collection-hero__description {
  margin-top: 12px;
}

.collection-hero__no-cover {
  padding-top: 40px;
}

.collection-hero__no-cover.collection-hero__no-title {
  padding-top: 0;
}

@media (max-width: 959px) {
  .collection-hero__no-cover.collection-hero__no-title .display-block-tablet {
    margin-top: 20px;
    margin-bottom: 0;
  }
}

@media (min-width: 960px) {
  .collection-hero__no-cover.collection-hero__no-title .display-block-desktop {
    margin-top: 40px;
    margin-bottom: 0;
  }
}

@media (max-width: 959px) {
  .collection-hero__no-cover {
    padding-top: 20px;
  }
}

/* The ipad end responds to the mobile end in vertical screen */

/* @custom-media --tablet (max-width: 959px); */

/* @custom-media --gt-mobile (min-width: 751px); */

/* detectingScreen need to consider the configuration of the tablet */
