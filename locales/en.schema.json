{"sections": {"announcement-bar": {"name": "Announcement bar", "settings": {"enable_sticky": {"label": "Announcement bar pinned at the top"}, "sticky_mode": {"label": "Display position", "options__0": {"label": "Pin all to top"}, "options__1": {"label": "Only pin to top on desktop"}, "options__2": {"label": "Only pin to top on mobile"}}, "enable_autoplay": {"label": "Turn on auto-switch"}, "autoplay_speed": {"label": "Switching interval"}, "show_social_media": {"label": "Display social media icons on desktop", "info": "You can add your social media links in 'Theme settings' > 'Social media'. Social media links won't be displayed if the announcement bar is set to 'Compact layout', 'Tiled display', or 'Automatically scroll left and right'."}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"item": {"name": "Announcement", "settings": {"notice_link_text": {"label": "Body"}, "notice_link_mb_text": {"label": "Main body on mobile device", "info": "If not entered, system will display texts of PC version on mobile device"}, "notice_link": {"label": "URL"}, "announcement_division_bottom": {"label": "Display the divider"}, "notice_text_color": {"label": "Font color"}, "notice_bg_color": {"label": "Background color"}}}}}, "apps": {"name": "Apps", "settings": {"include_margins": {"label": "Make section margins the same as theme"}}}, "blog": {"name": "Blogs", "settings": {"title": {"label": "Title"}, "title_align": {"label": "Title alignment style"}, "blog_collection": {"label": "Blogs"}, "limit": {"label": "Blog count", "unit": "<PERSON><PERSON>"}, "pc_cols": {"label": "No. of rows on desktop"}, "mobile_cols": {"label": "Number of columns on mobile", "options__0": {"label": "1 column"}, "options__1": {"label": "2 columns"}}, "enable_mobile_slide": {"label": "Turn on left-right scroll on mobile"}, "mobile_pagination_style": {"label": "Left-right scroll pagination style on mobile", "options__0": {"label": "Progress bar"}, "options__1": {"label": "Dots"}, "options__2": {"label": "Simple"}}, "is_show_date": {"label": "Display date"}, "is_show_author": {"label": "Display author"}, "is_show_cover": {"label": "Display cover"}, "image_cover_ratio": {"label": "Cover image ratio", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "1:1"}, "options__2": {"label": "4:3"}, "options__3": {"label": "3:2"}, "options__4": {"label": "16:9"}}, "is_show_desc": {"label": "Display summary"}, "btn_text": {"label": "Button text"}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "presets": {"presets__0": {"category": "Blogs", "name": "Blogs"}}}, "collapsible-content": {"name": "FAQS", "settings": {"heading": {"label": "Title"}, "heading_size": {"label": "Heading size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}, "heading_alignment": {"label": "Alignment style", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "layout_style": {"label": "Layout style", "options__0": {"label": "No container"}, "options__1": {"label": "Container per row"}, "options__2": {"label": "Section container"}}, "background_color": {"label": "Container background color"}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "open_first_collapsible_row": {"label": "Expand first tab by default"}, "group_header__0": {"label": "Image"}, "image": {"label": "Image"}, "image_ratio": {"label": "Image height", "options__0": {"label": "Auto"}, "options__1": {"label": "High"}, "options__2": {"label": "Low"}}, "desktop_layout": {"label": "Image position on desktop", "info": "Text below image by default on mobile", "options__0": {"label": "Image to left"}, "options__1": {"label": "Image to right"}}, "group_header__1": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"collapsible_row": {"name": "collapsible row", "settings": {"heading": {"label": "Title"}, "icon": {"label": "Logo", "options__0": {"label": "None"}, "options__1": {"label": "apple"}, "options__2": {"label": "banana"}, "options__3": {"label": "bottle"}, "options__4": {"label": "box"}, "options__5": {"label": "carrot"}, "options__6": {"label": "Chat bubble"}, "options__7": {"label": "Check mark"}, "options__8": {"label": "clipboard"}, "options__9": {"label": "Dairy products"}, "options__10": {"label": "Dairy free"}, "options__11": {"label": "Tumble dry"}, "options__12": {"label": "eye"}, "options__13": {"label": "fire"}, "options__14": {"label": "gluten free"}, "options__15": {"label": "Heart-shaped"}, "options__16": {"label": "iron"}, "options__17": {"label": "Leaves"}, "options__18": {"label": "leather"}, "options__19": {"label": "Lightning bolt"}, "options__20": {"label": "lipstick"}, "options__21": {"label": "Lock"}, "options__22": {"label": "Thumbtack"}, "options__23": {"label": "Contains no nuts"}, "options__24": {"label": "<PERSON>ts"}, "options__25": {"label": "Paw prints"}, "options__26": {"label": "pepper"}, "options__27": {"label": "perfume"}, "options__28": {"label": "airplane"}, "options__29": {"label": "Green plants"}, "options__30": {"label": "price tag"}, "options__31": {"label": "Question mark"}, "options__32": {"label": "Recycle and reuse"}, "options__33": {"label": "Refund"}, "options__34": {"label": "ruler"}, "options__35": {"label": "Dinner plate"}, "options__36": {"label": "shirt"}, "options__37": {"label": "shoe"}, "options__38": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "options__39": {"label": "snowflake"}, "options__40": {"label": "star"}, "options__41": {"label": "stopwatch"}, "options__42": {"label": "truck"}, "options__43": {"label": "Machine wash"}}, "upload_icon": {"label": "Upload icon"}, "icon_width": {"label": "Icon width on desktop"}, "row_content": {"label": "Description"}, "page": {"label": "Custom content"}}}}, "presets": {"presets__0": {"category": "Trust component"}}}, "collection-list": {"name": "Collection list", "settings": {"title": {"label": "Title"}, "collection_image_ratio": {"label": "Collection image ratio", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "1:1"}, "options__2": {"label": "4:3"}, "options__3": {"label": "2:3"}}, "collection_image_shape": {"label": "Collection image shape", "options__0": {"label": "Square"}, "options__1": {"label": "Round"}}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "desktop_grid_cols": {"label": "No. of rows on desktop"}, "m_cols": {"label": "Number of columns on mobile", "options__0": {"label": "1 column"}, "options__1": {"label": "2 columns"}, "options__2": {"label": "3 columns"}, "options__3": {"label": "4 columns"}}, "m_rows": {"label": "Number of rows on mobile", "info": "This takes effect when left-right scroll is turned on", "options__0": {"label": "1 line"}, "options__1": {"label": "2 line"}, "options__2": {"label": "3 line"}}, "slice_in_mobile": {"label": "Turn on left-right scroll to View all"}, "slice_in_pc": {"label": "<PERSON>roll left and right to view on desktop"}, "max_in_mobile": {"label": "The maximum number of categories displayed on the mobile terminal"}, "button_text": {"label": "Button text"}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"collection": {"name": "Collection list", "settings": {"category": {"label": "Collection list"}, "title": {"label": "Title"}, "image_display_area": {"label": "Image display area", "info": "Adjust the product map display area", "options__0": {"label": "Upper left"}, "options__1": {"label": "Above"}, "options__2": {"label": "Upper right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Center"}, "options__5": {"label": "Right"}, "options__6": {"label": "Bottom left"}, "options__7": {"label": "Below"}, "options__8": {"label": "Bottom right"}}}}}, "presets": {"presets__0": {"category": "Product display", "name": "Collection list"}}}, "combine-shoppable-image": {"name": "Combined shopping image", "settings": {"text_title": {"label": "Title"}, "description": {"label": "Description"}, "button_text": {"label": "Button text"}, "jump_link": {"label": "URL"}, "show_columns": {"label": "Display column", "options__0": {"label": "1 column"}, "options__1": {"label": "2 columns"}, "options__2": {"label": "3 columns"}}, "image_ratio": {"label": "Image ratio", "options__0": {"label": "2:3"}, "options__1": {"label": "1:1"}, "options__3": {"label": "2:1"}}, "group_header__0": {"label": "Anchor point"}, "anchor_quick_view": {"label": "Quick add to cart anchor"}, "anchor_show_type": {"label": "Anchor point display mode", "options__0": {"label": "Show title and price after clicking anchor"}, "options__1": {"label": "Fixed display of title and price"}}, "group_header__1": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image"}, "product_butotn_text": {"label": "Product button text"}, "product1": {"label": "Product 1"}, "horizontal_axis_position1": {"label": "Location of horizontal axis"}, "vertical_axis_position1": {"label": "Location of vertical axis"}, "product2": {"label": "Product 2"}, "horizontal_axis_position2": {"label": "Location of horizontal axis"}, "vertical_axis_position2": {"label": "Location of vertical axis"}, "product3": {"label": "Product 3"}, "horizontal_axis_position3": {"label": "Location of horizontal axis"}, "vertical_axis_position3": {"label": "Location of vertical axis"}, "product4": {"label": "Product 4"}, "horizontal_axis_position4": {"label": "Location of horizontal axis"}, "vertical_axis_position4": {"label": "Location of vertical axis"}, "product5": {"label": "Product 5"}, "horizontal_axis_position5": {"label": "Location of horizontal axis"}, "vertical_axis_position5": {"label": "Location of vertical axis"}}}}, "presets": {"presets__0": {"category": "Product display", "name": "Combined shopping image"}}}, "contact-form": {"name": "Contact form", "settings": {"heading": {"label": "Title"}, "heading_size": {"label": "Heading size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "presets": {"presets__0": {"category": "Customer operation", "name": "Contact form"}}}, "custom-html": {"name": "Custom HTML", "settings": {"html": {"label": "HTML"}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "presets": {"presets__0": {"category": "Custom", "name": "Custom HTML"}}}, "custom-page": {"name": "Custom page", "settings": {"page": {"label": "Custom page"}, "heading_size": {"label": "Heading size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "presets": {"presets__0": {"category": "Custom", "name": "Custom page"}}}, "featured-collection-with-banner": {"name": "Featured Products with cover", "settings": {"title": {"label": "Title"}, "heading_size": {"label": "Heading size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}, "product_categories": {"label": "Collection list"}, "show_collections_desc": {"label": "show category description"}, "group_header__0": {"label": "Cover image"}, "show_collection_image": {"label": "Banner display category image"}, "image": {"label": "Image"}, "image_opacticy": {"label": "Image mask layer opacity"}, "text_align": {"label": "Text alignment style", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "collection_title": {"label": "Title"}, "collection_description": {"label": "Body"}, "collection_text_color": {"label": "Font color"}, "collection_button_text": {"label": "Collection image button text"}, "group_header__1": {"label": "List"}, "products_num": {"label": "Maximum product quantity"}, "pc_cols": {"label": "No. of rows on desktop"}, "mobile_cols": {"label": "Number of columns on mobile", "options__0": {"label": "1 column"}, "options__1": {"label": "2 columns"}}, "slice_in_pc": {"label": "<PERSON>roll left and right to view on desktop"}, "slice_in_mobile": {"label": "Turn on left-right scroll to View all"}, "button_text": {"label": "Button text", "info": "Display this button only if 'Maximum products displayed' is less than the product quantity of this collection"}, "group_header__2": {"label": "Product image"}, "product_image_ratio": {"label": "Product image ratio", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "image_fill_type": {"label": "Image fill method", "options__0": {"label": "Fit"}, "options__1": {"label": "Fill"}}, "show_secondary_image": {"label": "Display next product image on hover"}, "group_header__3": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"image": {"name": "Image"}, "title": {"name": "Title"}, "price": {"name": "Price"}, "highlight": {"name": "Feature description", "settings": {"group_header__0": {"label": "To use this feature, create a namespace called \"highlights\" and a key with the name \"list\" for the product metafield, selecting the data type as \"multi-line text\". Once created, you can add values for the metafield field in specific products, and they will be displayed on the page."}}}, "text": {"name": "Text", "settings": {"text": {"label": "Text"}}}, "divider": {"name": "Divider"}, "brand": {"name": "Brand"}, "sku": {"name": "sku"}, "quick_add_button": {"name": "Quick add to cart button"}}, "presets": {"presets__0": {"category": "Product display", "name": "Featured Products with cover"}}}, "featured-collection": {"name": "Featured collection", "settings": {"title": {"label": "Title"}, "heading_size": {"label": "Heading size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}, "collection_1": {"label": "Product category 1"}, "label_1": {"label": "Tag title 1"}, "collection_2": {"label": "Product category 2"}, "label_2": {"label": "Tag title 2"}, "collection_3": {"label": "Product category 3"}, "label_3": {"label": "Tag title 3"}, "collection_4": {"label": "Product category 4"}, "label_4": {"label": "Tag title 4"}, "collection_5": {"label": "Product category 5"}, "label_5": {"label": "Tag title 5"}, "collection_6": {"label": "Product category 6"}, "label_6": {"label": "Tag title 6"}, "group_header__0": {"label": "List"}, "products_to_show": {"label": "Maximum product quantity"}, "columns_desktop": {"label": "No. of rows on desktop"}, "full_width": {"label": "Full screen width on desktop"}, "columns_mobile": {"label": "Number of columns on mobile", "options__0": {"label": "1 column"}, "options__1": {"label": "2 columns"}}, "enable_desktop_slider": {"label": "<PERSON>roll left and right to view on desktop"}, "enable_mobile_slider": {"label": "Turn on left-right scroll to View all"}, "button_text": {"label": "Button text", "info": "Display this button only if 'Maximum products displayed' is less than the product quantity of this collection"}, "full_in_mobile": {"label": "Full screen display on mobile"}, "group_header__1": {"label": "Product image"}, "product_image_ratio": {"label": "Product image ratio", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "image_fill_type": {"label": "Image fill method", "options__0": {"label": "Fit"}, "options__1": {"label": "Fill"}}, "image_grid_shape": {"label": "Collection image shape", "options__0": {"label": "Round"}, "options__1": {"label": "Square"}}, "show_secondary_image": {"label": "Display next product image on hover"}, "group_header__2": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"image": {"name": "Image"}, "title": {"name": "Title"}, "price": {"name": "Price"}, "highlight": {"name": "Feature description", "settings": {"group_header__0": {"label": "To use this feature, create a namespace called \"highlights\" and a key with the name \"list\" for the product metafield, selecting the data type as \"multi-line text\". Once created, you can add values for the metafield field in specific products, and they will be displayed on the page."}}}, "text": {"name": "Text", "settings": {"text": {"label": "Text"}}}, "divider": {"name": "Divider"}, "brand": {"name": "Brand"}, "sku": {"name": "sku"}, "quick_add_button": {"name": "Quick add to cart button"}}, "presets": {"presets__0": {"category": "Product display", "name": "Featured collection"}}}, "featured-product": {"name": "Featured product", "settings": {"product": {"label": "Products"}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "secondary_background": {"label": "Display background color block"}, "group_header__0": {"label": "Media"}, "product_image_pc_position": {"label": "Content location on desktop", "options__0": {"label": "Left"}, "options__1": {"label": "Right"}}, "magnifier_interactive_type": {"label": "Main image magnifier mode", "options__0": {"label": "Mode 1"}, "options__1": {"label": "Mode 2"}}, "video_loop": {"label": "Turn on video loop"}, "group_header__1": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"title": {"name": "Title", "settings": {"heading_size": {"label": "Heading size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}}}, "price": {"name": "Price"}, "variant_picker": {"name": "Variant picker", "settings": {"picker_type": {"label": "Product SKU style", "options__0": {"label": "Tiled"}, "options__1": {"label": "Dropdown"}}}}, "quantity_selector": {"name": "Quantity selector"}, "buy_buttons": {"name": "Purchase button"}, "share": {"name": "Share"}, "html": {"name": "Custom HTML", "settings": {"html": {"label": "HTML"}}}, "text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Text style", "options__0": {"label": "Body"}, "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}}}, "highlight": {"name": "Feature description"}}, "presets": {"presets__0": {"category": "Product display", "name": "Featured product"}}}, "featured-slideshow": {"name": "Split slideshow", "settings": {"section_height": {"label": "Height on desktop", "options__0": {"label": "Fit to Image 1"}, "options__1": {"label": "450px"}, "options__2": {"label": "550px"}, "options__3": {"label": "650px"}, "options__4": {"label": "750px"}}, "autoplay": {"label": "Turn on auto-switch"}, "autoplay_speed": {"label": "Change slides every", "unit": "s"}, "group_header__0": {"label": "Static image 1"}, "pc_static_image1": {"label": "Image on desktop"}, "mb_static_image1": {"label": "Image on mobile"}, "overlay_opacity1": {"label": "Layer mask opacity"}, "pc_static_text_position1": {"label": "Content position on desktop", "options__0": {"label": "Left"}, "options__1": {"label": "Middle"}, "options__2": {"label": "Right"}}, "static_text_align1": {"label": "Content alignment on desktop", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "mb_static_text_align1": {"label": "Content alignment on mobile", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "title1": {"label": "Main title"}, "title_size1": {"label": "Main title font size"}, "subheading1": {"label": "Body"}, "jump_link1": {"label": "URL"}, "text_color1": {"label": "Font color"}, "group_header__1": {"label": "Static image 2"}, "pc_static_image2": {"label": "Image on desktop"}, "mb_static_image2": {"label": "Image on mobile"}, "overlay_opacity2": {"label": "Layer mask opacity"}, "pc_static_text_position2": {"label": "Content position on desktop", "options__0": {"label": "Left"}, "options__1": {"label": "Middle"}, "options__2": {"label": "Right"}}, "static_text_align2": {"label": "Content alignment on desktop", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "mb_static_text_align2": {"label": "Content alignment on mobile", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "title2": {"label": "Main title"}, "title_size2": {"label": "Main title font size"}, "subheading2": {"label": "Body"}, "jump_link2": {"label": "URL"}, "text_color2": {"label": "Font color"}}, "blocks": {"image": {"name": "Slideshow", "settings": {"image": {"label": "Image on desktop"}, "image_mobile": {"label": "Image on mobile"}, "overlay_opacity": {"label": "Layer mask opacity"}, "text_mask": {"label": "Text overlay"}, "text_mask_color": {"label": "Text overlay color", "options__0": {"label": "Dark"}, "options__1": {"label": "Light"}}, "pc_text_position": {"label": "Content position on desktop", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}, "options__3": {"label": "Upper left"}, "options__4": {"label": "Above"}, "options__5": {"label": "Upper right"}, "options__6": {"label": "Bottom left"}, "options__7": {"label": "Below"}, "options__8": {"label": "Bottom right"}}, "pc_text_align": {"label": "Content alignment on desktop", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "mobile_text_align": {"label": "Content alignment on mobile", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "group_header__0": {"label": "Content"}, "sub_title": {"label": "Subtitle"}, "title": {"label": "Main title"}, "title_size": {"label": "Main title font size"}, "subheading": {"label": "Body"}, "group_header__1": {"label": "Button content"}, "link_text": {"label": "Button text"}, "link": {"label": "URL"}, "is_profile_link": {"label": "Use outline button style"}, "link_text_2": {"label": "Button text 2"}, "link_2": {"label": "URL 2"}, "is_profile_link2": {"label": "Use outline button style2"}, "text_color": {"label": "Font color"}}}}, "presets": {"presets__0": {"category": "Graphic display", "name": "Slideshow"}}}, "footer": {"name": "Footer", "settings": {"color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "show_pay_channel": {"label": "Show payment icon"}, "show_country_selector": {"label": "Display country/region selection", "info": "Add at least two markets for this to take effect. To add a country/region, go to [Market settings](/admin/settings/markets)"}, "show_locale_selector": {"label": "Display Language selector", "info": "You need to go to 'Market' > 'Language management' and add at least two languages for the selector to take effect. To add a language, go to ['Language settings'](/admin/settings/lang), and after adding is complete, go to ['Market'](/admin/settings/markets) > 'Language management' and add the language of the current market."}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"menu": {"name": "Quick navigation", "settings": {"title": {"label": "Title"}, "menu": {"label": "Navigation menu"}, "span": {"label": "Column width"}, "default_fold": {"label": "Expand by default"}}}, "custom": {"name": "Custom content", "settings": {"title": {"label": "Title"}, "content": {"label": "Body"}, "span": {"label": "Column width"}}}, "image": {"name": "Image", "settings": {"image": {"label": "Image"}, "image_width": {"label": "Image width"}, "span": {"label": "Column width"}}}, "newsletter": {"name": "Email subscription", "settings": {"title": {"label": "Title"}, "desc": {"label": "Description"}, "subscribe_letter_placeholder": {"label": "Input box prompt text"}, "span": {"label": "Column width"}}}, "social_media": {"name": "Social media icon", "settings": {"span": {"label": "Column width"}}}}}, "header": {"name": "Header", "settings": {"full_width": {"label": "Full screen width"}, "header_division_bottom": {"label": "Display divider below the header"}, "group_header__0": {"label": "Search"}, "show_search_mobile": {"label": "Display the search bar on mobile devices"}, "search_menu": {"label": "Suggested searches", "info": "Once selected, this menu will appear in search bar as a suggestion when customers search for the first time"}, "group_header__1": {"label": "Logo list"}, "show_icon": {"label": "Display statement icon"}, "icon": {"label": "Logo", "options__0": {"label": "Don't display"}, "options__1": {"label": "Secure payment"}, "options__2": {"label": "Package"}, "options__3": {"label": "Email"}, "options__4": {"label": "Locate"}, "options__5": {"label": "Customer"}, "options__6": {"label": "Cha<PERSON>"}, "options__7": {"label": "Gifts"}, "options__8": {"label": "Mobile phone No."}, "options__9": {"label": "Question mark"}, "options__10": {"label": "Logistics"}, "options__11": {"label": "Discount tag"}, "options__12": {"label": "medal"}, "options__13": {"label": "green"}}, "icon_image": {"label": "Image", "info": "Recommended size is 30 x 30px"}, "icon_title": {"label": "Title"}, "icon_sub_title": {"label": "Subtitle"}, "icon_link": {"label": "URL"}, "group_header__2": {"label": "Tool bar", "info": "Toolbar will be fixed at the above of header"}, "show_tool": {"label": "Display the toolbar"}, "show_tool_full": {"label": "Enable the widescreen display"}, "show_locale_selector": {"label": "Display Language selector", "info": "You need to go to 'Market' > 'Language management' and add at least two languages for the selector to take effect. To add a language, go to ['Language settings'](/admin/settings/lang), and after adding is complete, go to ['Market'](/admin/settings/markets) > 'Language management' and add the language of the current market."}, "show_country_selector": {"label": "Display country selector", "info": "Add at least two markets for this to take effect. To add a country/region, go to [Market settings](/admin/settings/markets)"}, "toolbar_social": {"label": "Display the social media icon", "info": "Add relevant social media links in ['Theme settings' > 'Social media'](/editor?locator=settings&category=media_social)"}, "toolbar_bacground_color": {"label": "Toolbar background color"}, "toolbar_link_color": {"label": "Toolbar link color"}, "toolbar_link_hover_color": {"label": "Toolbar link floating color"}, "toolbar_menu": {"label": "Navigation menu(toolbar)", "info": "Toolbar doesn't display drop-down menu, it only shows the first-level menu"}, "toolbar_menu_mobile": {"label": "Display the menu on mobile devices"}, "toolbar_menu_position": {"label": "<PERSON><PERSON>'s position on mobile devices", "options__0": {"label": "Above the main menu"}, "options__1": {"label": "Below the main menu"}}, "group_header__3": {"label": "Menu bar"}, "main_menu_link_list": {"label": "Main menu navigation"}, "second_menu_link_list": {"label": "Sub-menu navigation", "info": "Submenu navigation doesn't display drop-down menu, it only shows the first-level menu"}, "body_pc_second_font_size": {"label": "Secondary navigation font size on desktop", "options__0": {"label": "Large"}, "options__1": {"label": "Center"}, "options__2": {"label": "Small"}}, "body_pc_second_font_bold": {"label": "Bold display of secondary navigation on computer"}, "body_pc_thirdly_font_size": {"label": "Font size of tertiary navigation on desktop", "options__0": {"label": "Large"}, "options__1": {"label": "Center"}, "options__2": {"label": "Small"}}, "mobile_top_menu": {"label": "Pin-to-top menu on mobile", "info": "This will pin menu to bottom of header on mobile device and can be switched with left-right scroll"}, "mobile_top_menu_show_home": {"label": "<PERSON>u pinned to top will only be displayed on home page", "info": "This will only display menu pinned to top on the home page"}, "group_header__4": {"label": "Highlights", "info": "You can highlight specific menu items"}, "enable_highlight": {"label": "Turn on Highlights"}, "highlight_menus": {"label": "Highlights menu", "info": "Enter the main menu names you want to highlight. Separate multiple names with a comma (,)."}, "highlight_text_color": {"label": "Highlights text color"}, "highlight_bg_color": {"label": "Highlights background color"}, "group_header__5": {"label": "Color"}, "header_background_color": {"label": "Header background color"}, "header_text_color": {"label": "Header text color"}, "menu_background_color": {"label": "Menu background color"}, "menu_text_color": {"label": "Menu text color"}, "search_color": {"label": "Search box text color"}, "search_bacground_color": {"label": "Search box background color"}, "user_mobile_layout": {"label": "Account position on mobile", "options__0": {"label": "Display on home page"}, "options__1": {"label": "Display in side menu"}}, "sticky_header_type": {"label": "Sticky header", "options__0": {"label": "None"}, "options__1": {"label": "Always"}, "options__2": {"label": "On scroll up"}}, "show_user_entry": {"label": "Display Account icon"}, "show_cart_entry": {"label": "Display shopping cart"}, "cart_icon": {"label": "Shopping cart icon", "options__0": {"label": "<PERSON><PERSON>"}, "options__1": {"label": "Shopping bag"}}}, "blocks": {"menuImage": {"name": "Menu Image", "settings": {"menu_title": {"label": "Trigger menu", "info": "Enter your first-level menu name, which will take effect when you have a dropdown"}, "group_header__0": {"label": "Image 1"}, "image_1": {"label": "Image"}, "image_1_title": {"label": "Title"}, "image_1_link_text": {"label": "Button text"}, "image_1_link": {"label": "URL"}, "image_1_position": {"label": "Image position", "options__0": {"label": "Front"}, "options__1": {"label": "Back"}}, "group_header__1": {"label": "Image 2"}, "image_2": {"label": "Image"}, "image_2_title": {"label": "Title"}, "image_2_link_text": {"label": "Button text"}, "image_2_link": {"label": "URL"}, "image_2_position": {"label": "Image position", "options__0": {"label": "Front"}, "options__1": {"label": "Back"}}, "group_header__2": {"label": "Image 3"}, "image_3": {"label": "Image"}, "image_3_title": {"label": "Title"}, "image_3_link_text": {"label": "Button text"}, "image_3_link": {"label": "URL"}, "image_3_position": {"label": "Image position", "options__0": {"label": "Front"}, "options__1": {"label": "Back"}}}}}}, "icon-list": {"name": "Logo list", "settings": {"title": {"label": "Title"}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"icon": {"name": "Logo", "settings": {"image": {"label": "Image", "info": "Suggested ratio: 2:1 / Size: 220-220px Suggested ratio: 1:2 / Size: 157-157px"}, "link": {"label": "URL"}}}}, "presets": {"presets__0": {"category": "Trust component", "name": "Logo list"}}}, "image-banner": {"name": "Image banner", "settings": {"banner1": {"label": "Image 1"}, "banner2": {"label": "Image 2"}, "banner_height_size": {"label": "Image height", "info": "Recommended image ratio of 2:3", "options__0": {"label": "Low"}, "options__1": {"label": "Center"}, "options__2": {"label": "High"}}, "override_banner_height": {"label": "Fit to Image 1", "info": "Overlay image height"}, "pc_content_position": {"label": "Content position on desktop", "options__0": {"label": "Upper left"}, "options__1": {"label": "Above"}, "options__2": {"label": "Upper right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Center"}, "options__5": {"label": "Right"}, "options__6": {"label": "Bottom left"}, "options__7": {"label": "Below"}, "options__8": {"label": "Bottom right"}}, "pc_text_position": {"label": "Content alignment on desktop", "options__0": {"label": "Left align"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right align"}}, "pc_show_textarea": {"label": "Display text box on desktop"}, "alpha_range": {"label": "Overlay opacity"}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__0": {"label": "Mobile device layout"}, "mobile_text_position": {"label": "Content alignment on mobile", "options__0": {"label": "Left align"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right align"}}, "mobile_banner_flatten": {"label": "Tiled display on mobile"}, "m_show_textarea": {"label": "Display text below the image on mobile"}}, "blocks": {"title": {"name": "Title", "settings": {"title": {"label": "Main title"}, "title_size": {"label": "Main title font size", "options__0": {"label": "Large"}, "options__1": {"label": "Center"}, "options__2": {"label": "Small"}}}}, "desc": {"name": "Description", "settings": {"description": {"label": "Description"}}}, "button": {"name": "Buttons", "settings": {"button_text": {"label": "Button text"}, "link": {"label": "URL"}, "outline_button": {"label": "Apply button outline style"}, "link_text_2": {"label": "Button text 2"}, "link_2": {"label": "URL 2"}, "outline_button_2": {"label": "Apply button outline style"}}}}, "presets": {"presets__0": {"category": "Graphic display", "name": "Image banner"}}}, "image-with-text": {"name": "Image with text", "settings": {"image": {"label": "Image"}, "image_height": {"label": "Image height", "options__0": {"label": "Auto"}, "options__1": {"label": "High"}, "options__2": {"label": "Low"}}, "pc_image_width": {"label": "Image width on desktop", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}, "pc_image_position": {"label": "Image position on desktop", "info": "Text below image by default on mobile", "options__0": {"label": "Image to left"}, "options__1": {"label": "Image to right"}}, "pc_box_align": {"label": "Content position on desktop", "options__0": {"label": "Top"}, "options__1": {"label": "Center"}, "options__2": {"label": "Bottom"}}, "pc_text_align": {"label": "Content alignment on desktop", "options__0": {"label": "Left align"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right align"}}, "image_overlap_display": {"label": "Text overlap on image"}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "mobile_text_align": {"label": "Content alignment on mobile", "options__0": {"label": "Left align"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right align"}}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"sub_title": {"name": "Subtitle", "settings": {"text": {"label": "Text"}, "text_size": {"label": "Text size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}}}, "title": {"name": "Title", "settings": {"title": {"label": "Title"}, "title_size": {"label": "Title text size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}}}, "content": {"name": "Text", "settings": {"content": {"label": "Content"}}}, "button": {"name": "Buttons", "settings": {"button_text": {"label": "Button text"}, "link": {"label": "URL"}}}}, "presets": {"presets__0": {"category": "Graphic display", "name": "Image with text"}}}, "logo-list": {"name": "Logo list", "settings": {"width": {"label": "Component width", "options__0": {"label": "Full screen"}, "options__1": {"label": "Standard"}}, "layout": {"label": "Layout", "options__0": {"label": "Vertical layout"}, "options__1": {"label": "Horizontal layout"}}, "font_color": {"label": "Font color"}, "icon_color": {"label": "Icon color"}, "background_color": {"label": "Background color"}, "mobile_display": {"label": "Styles on mobile device", "options__0": {"label": "<PERSON><PERSON> left and right"}, "options__1": {"label": "Grid"}}, "style_card": {"label": "Card style"}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"icon": {"name": "Statement", "settings": {"icon": {"label": "Logo", "options__0": {"label": "Don't display"}, "options__1": {"label": "Secure payment"}, "options__2": {"label": "Package"}, "options__3": {"label": "Email"}, "options__4": {"label": "Locate"}, "options__5": {"label": "Customer"}, "options__6": {"label": "Cha<PERSON>"}, "options__7": {"label": "Gifts"}, "options__8": {"label": "Mobile phone No."}, "options__9": {"label": "Question mark"}, "options__10": {"label": "Logistics"}, "options__11": {"label": "Discount tag"}}, "image": {"label": "Image", "info": "Suggested ratio: 1:1 / Size: 48-48px"}, "title": {"label": "Title"}, "subtitle": {"label": "Subtitle"}, "link": {"label": "URL"}}}}, "presets": {"presets__0": {"category": "Trust component", "name": "Logo list"}}}, "main-404": {"name": "404 error page"}, "main-account": {"name": "Account", "settings": {"group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "main-activate-account": {"name": "Product", "settings": {"group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "main-addresses": {"name": "Address", "settings": {"group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "product-recently-viewed": {"name": "Recently viewed products", "settings": {"title": {"label": "Title"}, "title_size": {"label": "Heading size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}, "show_product_number": {"label": "Display product quantity"}, "pc_cols": {"label": "No. of rows on desktop"}, "mobile_cols": {"label": "Number of columns on mobile", "options__0": {"label": "1 column"}, "options__1": {"label": "2 columns"}}, "enable_horizontal_slider": {"label": "Turn on left-right scroll", "info": "Apply when more products are displayed than columns available"}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__0": {"label": "Product image"}, "product_image_ratio": {"label": "Product image ratio", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "product_fill_type": {"label": "Image fill method", "options__0": {"label": "Fit"}, "options__1": {"label": "Fill"}}, "show_secondary_image": {"label": "Display next product image on hover"}, "group_header__1": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "presets": {"presets__0": {"category": "Product display", "name": "Recently viewed products"}}}, "main-article": {"name": "Article", "blocks": {"select": {"name": "Image", "settings": {"image_height": {"label": "Cover height", "options__0": {"label": "Auto"}, "options__1": {"label": "Small"}, "options__2": {"label": "Center"}, "options__3": {"label": "Large"}}}}, "title": {"name": "Title", "settings": {"blog_show_date": {"label": "Display date"}, "blog_show_author": {"label": "Display author"}}}, "share": {"name": "Share"}, "content": {"name": "Content"}}}, "main-blog-list": {"name": "Blogs", "settings": {"layout": {"label": "Layout", "options__0": {"label": "Grid"}, "options__1": {"label": "List"}}, "page_number": {"label": "Number of blogs per page"}, "columns": {"label": "No. of rows on desktop", "info": "Applies only in grid layout", "options__0": {"label": "2 columns"}, "options__1": {"label": "3 columns"}}, "is_show_cover": {"label": "Display cover"}, "cover_img_ratio": {"label": "Cover height", "options__0": {"label": "Auto"}, "options__1": {"label": "Small"}, "options__2": {"label": "Center"}, "options__3": {"label": "Large"}}, "is_show_date": {"label": "Display date"}, "is_show_author": {"label": "Display author"}, "is_show_desc": {"label": "Display summary"}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "main-cart-footer": {"name": "Subtotal", "blocks": {"buttons": {"name": "Checkout button"}, "subtotal": {"name": "Subtotal price"}}}, "main-cart-items": {"name": "Products", "settings": {"group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "main-collection-banner": {"name": "Collection header", "settings": {"show_collection_description": {"label": "Display collection description"}, "show_collection_name": {"label": "Display collection name"}, "show_collection_cover": {"label": "Display collection image", "info": "To add collection images, go to the product collection management page. [Add now](/admin/categories)"}, "banner_image_mobile": {"label": "Mobile banner"}, "pc_collection_img_height": {"label": "Collection height on desktop"}, "md_collection_img_height": {"label": "Collection height on mobile"}, "collection_cover_area": {"label": "Image display area", "options__0": {"label": "Top"}, "options__1": {"label": "Middle"}, "options__2": {"label": "Bottom"}}, "mask_color": {"label": "Overlay color"}, "mask_color_opacity": {"label": "Overlay opacity"}, "parallax_scroll": {"label": "Parallax scroll"}}}, "main-collection-product-list": {"name": "Product list", "settings": {"products_per_page": {"label": "Products per page"}, "columns_desktop": {"label": "No. of rows on desktop"}, "columns_mobile": {"label": "Number of columns on mobile", "options__0": {"label": "1 column"}, "options__1": {"label": "2 columns"}}, "pagination_style": {"label": "Paginator", "options__0": {"label": "Flip back and forth"}, "options__1": {"label": "Page redirection"}}, "enable_infinite_scroll": {"label": "Infinite pagination"}, "enable_infinite_scroll_button": {"label": "Display 'View more' in infinite pagination"}, "group_header__0": {"label": "Product information", "info": "To configure promotion tags and view/add to your cart, go to ['Theme settings' > 'Products'](/editor?locator=settings&category=product)"}, "price_show_type": {"label": "Display style of price", "info": "If the product has only one variant, then display with the style of single price", "options__0": {"label": "Minimum price"}, "options__1": {"label": "Price range"}, "options__2": {"label": "From minimum price"}}, "show_origin_price": {"label": "Whether display original price"}, "group_header__1": {"label": "Product image"}, "product_image_ratio": {"label": "Product image ratio", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "image_fill_type": {"label": "Image fill method", "options__0": {"label": "Fit"}, "options__1": {"label": "Fill"}}, "image_display_area": {"label": "Image display area", "info": "Adjust the product map display area", "options__0": {"label": "Upper left"}, "options__1": {"label": "Above"}, "options__2": {"label": "Upper right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Center"}, "options__5": {"label": "Right"}, "options__6": {"label": "Bottom left"}, "options__7": {"label": "Below"}, "options__8": {"label": "Bottom right"}}, "show_secondary_image": {"label": "Display next product image on hover"}, "mobile_show_secondary_image": {"label": "Display the next product image when hovering on a mobile device"}, "sticky_filtering": {"label": "Fixed toolbar when scrolling"}, "group_header__2": {"label": "sorting and filtering"}, "enable_filtering": {"label": "Turn on filtering"}, "filter_type": {"label": "Filter layout", "options__0": {"label": "Horizontal"}, "options__1": {"label": "Vertical"}, "options__2": {"label": "Drawers"}}, "enable_sorting": {"label": "Turn on sorting"}, "group_header__3": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"image": {"name": "Image"}, "title": {"name": "Title"}, "price": {"name": "Price"}, "highlight": {"name": "Feature description", "settings": {"group_header__0": {"label": "To use this feature, create a namespace called \"highlights\" and a key with the name \"list\" for the product metafield, selecting the data type as \"multi-line text\". Once created, you can add values for the metafield field in specific products, and they will be displayed on the page."}}}, "text": {"name": "Text", "settings": {"text": {"label": "Text"}}}, "divider": {"name": "Divider"}, "brand": {"name": "Brand"}, "sku": {"name": "sku"}, "quick_add_button": {"name": "Quick add to cart button"}}}, "main-company": {"name": "Company account registration", "settings": {"padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "main-forgot-password": {"name": "Forgot password", "settings": {"padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "main-list-collections": {"name": "Collection list", "settings": {"title": {"label": "Title"}, "sort": {"label": "Sort by", "options__0": {"label": "Name: A-<PERSON>"}, "options__1": {"label": "Name: Z-<PERSON>"}, "options__2": {"label": "Date, from latest to earliest"}, "options__3": {"label": "Date, from earliest to latest"}, "options__4": {"label": "Products: most - least"}, "options__5": {"label": "Products: least - most"}}, "collection_image_ratio": {"label": "Collection image ratio", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "collection_fill_type": {"label": "Image fill method", "options__0": {"label": "Fit"}, "options__1": {"label": "Fill"}}, "pc_cols": {"label": "No. of rows on desktop"}, "m_cols": {"label": "Number of columns on mobile", "options__0": {"label": "1 column"}, "options__1": {"label": "2 columns"}}}}, "main-login": {"name": "Customer <PERSON><PERSON>", "settings": {"padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "main-order-detail": {"name": "Order details", "settings": {"group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "main-order-list": {"name": "Order list", "settings": {"group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "main-order-tracking": {"name": "Order Tracking", "settings": {"title": {"label": "Title"}, "btn_text": {"label": "Button text"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "main-page": {"name": "Custom page", "settings": {"group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "main-password-footer": {"name": "Password page footer"}, "main-password-header": {"name": "Password page header"}, "main-password": {"name": "Password", "settings": {"group_header__0": {"label": "Title"}, "image": {"label": "Background image"}, "image_overlay_opacity": {"label": "Image overlay opacity"}, "show_background_image": {"label": "Display background image"}, "image_height": {"label": "Banner height", "info": "To achieve optimal results, please use an image with 16:9 aspect ratio", "options__0": {"label": "Auto"}, "options__1": {"label": "Small"}, "options__2": {"label": "Center"}, "options__3": {"label": "Large"}}, "desktop_content_position": {"label": "Content position on desktop", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}, "options__3": {"label": "Upper left"}, "options__4": {"label": "Bottom left"}, "options__5": {"label": "Below"}, "options__6": {"label": "Upper right"}, "options__7": {"label": "Bottom right"}}, "show_text_box": {"label": "Display text background module on PC"}, "desktop_content_alignment": {"label": "Content alignment on desktop", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__1": {"label": "Mobile device layout"}, "mobile_content_alignment": {"label": "Content alignment on mobile", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "show_text_below": {"label": "Display contents below image on mobile device"}}, "blocks": {"heading": {"name": "Title", "settings": {"heading": {"label": "Title"}, "heading_size": {"label": "Heading size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}}}, "paragraph": {"name": "Description", "settings": {"text": {"label": "Description"}}}, "email_form": {"name": "Email subscription"}}}, "main-product": {"name": "Product", "settings": {"product_info_sticky": {"label": "Fixed display of product info on desktop"}, "product_image_pc_show_style": {"label": "Layout on desktop", "options__0": {"label": "Tiled"}, "options__1": {"label": "2 columns"}, "options__2": {"label": "Tiled thumbnails"}, "options__3": {"label": "Thumbnail carousel"}}, "product_image_size": {"label": "Content size on desktop", "info": "Media files will automatically optimize on mobile", "options__0": {"label": "Large"}, "options__1": {"label": "Center"}, "options__2": {"label": "Small"}}, "product_image_fill_type": {"label": "Image fill mode on desktop", "options__0": {"label": "Fit"}, "options__1": {"label": "Fill"}}, "product_image_ratio": {"label": "Image ratio on desktop", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "Follow first image"}, "options__2": {"label": "1:1"}, "options__4": {"label": "4:3"}}, "image_quality": {"label": "Image compression ratio", "info": "Configure the compression ratio for main and thumbnail images. We recommend selecting a compression ratio of 80% or lower to ensure optimal store performance and user experience.", "options__0": {"label": "Original aspect ratio"}, "options__1": {"label": "90%"}, "options__2": {"label": "80%"}, "options__3": {"label": "70%"}}, "product_image_pc_thumbnail_postion": {"label": "Thumbnail position on desktop", "options__0": {"label": "Beside product image"}, "options__1": {"label": "Below product image"}}, "product_thumbnail_image_size": {"label": "Thumbnail size on desktop", "options__0": {"label": "Large"}, "options__1": {"label": "Center"}, "options__2": {"label": "Small"}}, "video_loop": {"label": "Turn on video loop"}, "video_autoplay": {"label": "Video autoplay", "info": "Videos cannot autoplay due to browser limitations"}, "youtube_simple_style": {"label": "Minimalist YouTube video player"}, "pc_magnifier_type": {"label": "Magnifier on desktop", "options__0": {"label": "Click to enlarge"}, "options__1": {"label": "Enlarge on hover"}}, "magnifier_interactive_type": {"label": "Main image magnifier mode", "options__0": {"label": "Mode 1"}, "options__1": {"label": "Mode 2"}}, "default_selected_variant": {"label": "SKU selected by default"}, "hide_variants": {"label": "Hide pictures of other SKUs after selecting a SKU"}, "group_header__0": {"label": "Mobile client"}, "product_mobile_thumbnail_image_hide": {"label": "Mobile device layout", "options__0": {"label": "2 columns"}, "options__1": {"label": "Hide thumbnails"}, "options__2": {"label": "Display thumbnails"}}, "product_mobile_image_fill_type": {"label": "Image fill method on mobile", "options__0": {"label": "Fit"}, "options__1": {"label": "Fill"}}, "product_mobile_image_ratio": {"label": "Image ratio on mobile", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "Follow first image"}, "options__2": {"label": "1:1"}, "options__4": {"label": "4:3"}}, "product_mobile_thumbnail_image_size": {"label": "Thumbnail size on mobile", "options__0": {"label": "Large"}, "options__1": {"label": "Center"}, "options__2": {"label": "Small"}}, "group_header__1": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"variant_sku": {"name": "SKU(Stock Keeping Unit)"}, "title": {"name": "Title"}, "dividing_line": {"name": "Divider", "settings": {"show_pc_line": {"label": "Display on PC"}, "show_mobile_line": {"label": "Display on mobile device"}, "dividing_line_color": {"label": "Divider color"}, "desktop_dividing_line_height": {"label": "Divider thickness on desktop"}, "dividing_line_style": {"label": "Divider style on mobile", "options__0": {"label": "White space divider"}, "options__1": {"label": "Full screen divider"}}, "dividing_line_height": {"label": "Divider thickness on mobile"}}}, "price": {"name": "Price", "settings": {"show_order": {"label": "Price/promotion display style", "options__0": {"label": "Selling price, original price"}, "options__1": {"label": "Original price, selling price"}, "options__2": {"label": "Promotion tag, selling price, original price"}, "options__3": {"label": "Selling price, original price, promotion tag"}, "options__4": {"label": "Selling price, promotion tag"}, "options__5": {"label": "Promotion tag, selling price"}}, "sale_font_size": {"label": "Selling price font size", "options__0": {"label": "Small"}, "options__1": {"label": "Medium"}, "options__2": {"label": "Large"}, "options__3": {"label": "Extra large"}}, "regular_font_size": {"label": "Original price (compare-at price) font size", "options__0": {"label": "Small"}, "options__1": {"label": "Medium"}, "options__2": {"label": "Large"}, "options__3": {"label": "Extra large"}}, "save_font_size": {"label": "Promotion tag font size", "options__0": {"label": "Small"}, "options__1": {"label": "Medium"}, "options__2": {"label": "Large"}, "options__3": {"label": "Extra large"}}, "discount_style": {"label": "Discount display format", "options__0": {"label": "Discount rate"}, "options__1": {"label": "Discount amount"}}, "save_style": {"label": "Promotion tag style", "info": "To edit promotion tag colors, go to ['Theme settings' > 'Colors'](/editor?locator=settings&category=color)", "options__0": {"label": "Button style"}, "options__1": {"label": "Text style"}}, "font_size_flexible": {"label": "Flexible font size configuration", "info": "When turned on, you can customize font size"}, "sale_price_pc_font_size": {"label": "Selling price font size on desktop"}, "sale_price_mobile_font_size": {"label": "Selling price font size on mobile"}, "regular_price_pc_font_size": {"label": "Original price (compare-at price) font size on desktop"}, "regular_price_mobile_font_size": {"label": "Original price (compare-at price) font size on mobile"}, "save_price_pc_font_size": {"label": "Promotion tag font size on desktop"}, "save_price_mobile_font_size": {"label": "Promotion tag font size on mobile"}, "sale_font_bold": {"label": "Selling price bold fonts"}}}, "variant_picker": {"name": "Variant picker", "settings": {"picker_type": {"label": "Product SKU style", "options__0": {"label": "Tiled"}, "options__1": {"label": "Select"}}, "sizes": {"label": "Display size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}, "layout_direction": {"label": "Display layout", "options__0": {"label": "Vertical layout"}, "options__1": {"label": "Horizontal layout"}}, "enabled_color_swatch": {"label": "Enable color palette", "info": "When activated, the color palette will display as Tile view by default. [Learn more](https://help.shopline.com/hc/en-001/articles/18291247740825-Quick-Add-to-Cart-of-Palette-Feature#h_01J0X1YZHS09MJ94XAYHX515ZV)"}, "color_swatch_type": {"label": "Palette style", "options__0": {"label": "Square"}, "options__1": {"label": "Round"}, "options__2": {"label": "Rounded rectangle"}}}}, "quantity_selector": {"name": "Quantity selector", "settings": {"width": {"label": "Display width", "options__0": {"label": "1/2 column width"}, "options__1": {"label": "Overall column width"}}, "layout_direction": {"label": "Display layout", "options__0": {"label": "Vertical layout"}, "options__1": {"label": "Horizontal layout"}}, "border_style": {"label": "Display style", "options__0": {"label": "Line style"}, "options__1": {"label": "Border style"}, "options__2": {"label": "No-border style"}}}}, "inventory": {"name": "Inventory status", "settings": {"inventory_threshold": {"label": "Low inventory threshold", "info": "Selecting of 0 means to display \"In stock\" all the time"}, "show_inventory_quantity": {"label": "Show stock quantity"}}}, "buy_buttons": {"name": "Purchase button", "settings": {"button_layout": {"label": "Buy button position", "options__0": {"label": "Built in page"}, "options__1": {"label": "Sticky floating footer"}, "options__2": {"label": "Built in page with floating"}}}}, "description": {"name": "Product description", "settings": {"location": {"label": "Display position of product description", "options__0": {"label": "Right of product image"}, "options__1": {"label": "Below product image"}}, "is_fold": {"label": "Product description collapse"}}}, "description_accordion": {"name": "Product description (Accordion)", "settings": {"title": {"label": "Title"}, "fold": {"label": "Folded display"}}}, "share": {"name": "Share", "settings": {"group_header__0": {"label": "Add relevant social media links in ['Theme settings' > 'Social media'](/editor?locator=settings&category=media_social)"}}}, "product_additional": {"name": "Custom content", "settings": {"title": {"label": "Title"}, "icon": {"label": "Logo", "options__0": {"label": "Don't display"}, "options__1": {"label": "Secure payment"}, "options__2": {"label": "Package"}, "options__3": {"label": "Email"}, "options__4": {"label": "Locate"}, "options__5": {"label": "Customer"}, "options__6": {"label": "Cha<PERSON>"}, "options__7": {"label": "Gifts"}, "options__8": {"label": "Mobile phone No."}, "options__9": {"label": "Help Center"}, "options__10": {"label": "Logistics"}, "options__11": {"label": "Discount tag"}}, "description": {"label": "Description"}, "custom_page": {"label": "Custom page"}}}, "html": {"name": "Custom HTML", "settings": {"html": {"label": "HTML"}}}, "icon": {"name": "Logo list", "settings": {"icon1": {"label": "Icon 1", "options__0": {"label": "Don't display"}, "options__1": {"label": "Secure payment"}, "options__2": {"label": "Package"}, "options__3": {"label": "Email"}, "options__4": {"label": "Locate"}, "options__5": {"label": "Customer"}, "options__6": {"label": "Cha<PERSON>"}, "options__7": {"label": "Gifts"}, "options__8": {"label": "Mobile phone No."}, "options__9": {"label": "Question mark"}, "options__10": {"label": "Logistics"}, "options__11": {"label": "Discount tag"}, "options__12": {"label": "medal"}, "options__13": {"label": "green"}}, "image1": {"label": "Image 1"}, "title1": {"label": "Title 1"}, "sub_title1": {"label": "Subtitle 1"}, "icon2": {"label": "Icon 2", "options__0": {"label": "Don't display"}, "options__1": {"label": "Secure payment"}, "options__2": {"label": "Package"}, "options__3": {"label": "Email"}, "options__4": {"label": "Locate"}, "options__5": {"label": "Customer"}, "options__6": {"label": "Cha<PERSON>"}, "options__7": {"label": "Gifts"}, "options__8": {"label": "Mobile phone No."}, "options__9": {"label": "Question mark"}, "options__10": {"label": "Logistics"}, "options__11": {"label": "Discount tag"}, "options__12": {"label": "medal"}, "options__13": {"label": "green"}}, "image2": {"label": "Image 2"}, "title2": {"label": "Title 2"}, "sub_title2": {"label": "Subtitle 2"}, "icon3": {"label": "Icon 3", "options__0": {"label": "Don't display"}, "options__1": {"label": "Secure payment"}, "options__2": {"label": "Package"}, "options__3": {"label": "Email"}, "options__4": {"label": "Locate"}, "options__5": {"label": "Customer"}, "options__6": {"label": "Cha<PERSON>"}, "options__7": {"label": "Gifts"}, "options__8": {"label": "Mobile phone No."}, "options__9": {"label": "Question mark"}, "options__10": {"label": "Logistics"}, "options__11": {"label": "Discount tag"}, "options__12": {"label": "medal"}, "options__13": {"label": "green"}}, "image3": {"label": "Image 3"}, "title3": {"label": "Title 3"}, "sub_title3": {"label": "Subtitle 3"}}}, "highlight": {"name": "Feature description", "settings": {"group_header__0": {"label": "To use this feature, create a namespace called \"highlights\" and a key with the name \"list\" for the product metafield, selecting the data type as \"multi-line text\". Once created, you can add values for the metafield field in specific products, and they will be displayed on the page."}}}, "text": {"name": "Text", "settings": {"text": {"label": "Text"}, "text_style": {"label": "Text style", "options__0": {"label": "Body"}, "options__1": {"label": "Subtitle"}, "options__2": {"label": "Uppercase"}}}}}}, "main-register": {"name": "Customer registration", "settings": {"padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}}, "main-search": {"name": "Search results", "settings": {"products_per_page": {"label": "Products per page"}, "columns_desktop": {"label": "No. of rows on desktop"}, "columns_mobile": {"label": "Number of columns on mobile", "options__0": {"label": "1 column"}, "options__1": {"label": "2 columns"}}, "group_header__0": {"label": "Product image"}, "product_image_ratio": {"label": "Product image ratio", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "image_fill_type": {"label": "Image fill method", "options__0": {"label": "Fit"}, "options__1": {"label": "Fill"}}, "show_secondary_image": {"label": "Display next product image on hover"}, "group_header__1": {"label": "sorting and filtering"}, "enable_filtering": {"label": "Turn on filtering"}, "filter_type": {"label": "Filter layout", "options__0": {"label": "Horizontal"}, "options__1": {"label": "Vertical"}, "options__2": {"label": "Drawers"}}, "enable_sorting": {"label": "Turn on sorting"}, "group_header__2": {"label": "Article"}, "show_article_author": {"label": "Display author"}, "show_article_date": {"label": "Display date"}, "group_header__3": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"image": {"name": "Image"}, "title": {"name": "Title"}, "price": {"name": "Price"}, "highlight": {"name": "Feature description"}, "text": {"name": "Text", "settings": {"text": {"label": "Text"}}}, "divider": {"name": "Divider"}, "brand": {"name": "Brand"}, "sku": {"name": "sku"}, "quick_add_button": {"name": "Quick add to cart button"}}}, "map": {"name": "Map", "settings": {"title": {"label": "Title"}, "store_info": {"label": "Address and opening hours"}, "address": {"label": "Address", "info": "Click and jump to corresponding address on Google Maps"}, "btn_style": {"label": "Button style", "options__0": {"label": "Main button"}, "options__1": {"label": "Default button"}}, "btn_text": {"label": "Button text"}, "pushpin": {"label": "Display drawing pin"}, "group_header__0": {"label": "Background"}, "bg_color": {"label": "Background color"}, "google_api_secret_key": {"label": "Google Maps API Key", "info": "Enter the Google Map API to show the map. [How to get the API key?](https://shoplineapphelp.zendesk.com/hc/articles/4411546876313-%E5%A6%82%E4%BD%95%E6%B3%A8%E5%86%8C-Google-Maps-API-%E5%AF%86%E9%92%A5)"}, "image": {"label": "Image", "info": "Display the image when Google Map is not configured"}, "image_position": {"label": "Image display area", "options__0": {"label": "Upper left"}, "options__1": {"label": "Above"}, "options__2": {"label": "Upper right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Center"}, "options__5": {"label": "Right"}, "options__6": {"label": "Bottom left"}, "options__7": {"label": "Below"}, "options__8": {"label": "Bottom right"}}}, "presets": {"presets__0": {"category": "Trust component", "name": "Map"}}}, "multi-media-splicing": {"name": "Content collage", "settings": {"title": {"label": "Title"}, "title_size": {"label": "Heading size", "options__0": {"label": "Large"}, "options__1": {"label": "Center"}, "options__2": {"label": "Small"}}, "desktop_layout": {"label": "Layout on desktop", "options__0": {"label": "Left larger than right"}, "options__1": {"label": "Right larger than left"}}, "mobile_layout": {"label": "Mobile device layout", "options__0": {"label": "Collage"}, "options__1": {"label": "List"}}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"video": {"name": "Video", "settings": {"cover_image": {"label": "Cover image"}, "internal_video": {"label": "Local upload"}, "external_video": {"label": "External video link", "info": "Enter the embedded link from YouTube or Vimeo"}, "video_alt": {"label": "Video alt text"}, "image_padding": {"label": "Image fill method", "info": "Images are fixed as 'Fit' in large image layout", "options__0": {"label": "Fill"}, "options__1": {"label": "Fit"}}}}, "product": {"name": "Products", "settings": {"product": {"label": "Products"}, "product_hover_show_next": {"label": "Display next product image on hover"}, "image_padding": {"label": "Image fill method", "info": "Images are fixed as 'Fit' in large image layout", "options__0": {"label": "Fill"}, "options__1": {"label": "Fit"}}}}, "collection": {"name": "Collection list", "settings": {"category": {"label": "Collection list"}, "image_padding": {"label": "Image fill method", "info": "Images are fixed as 'Fit' in large image layout", "options__0": {"label": "Fill"}, "options__1": {"label": "Fit"}}}}, "image": {"name": "Image", "settings": {"image": {"label": "Image"}, "image_padding": {"label": "Image fill method", "info": "Images are fixed as 'Fit' in large image layout", "options__0": {"label": "Fill"}, "options__1": {"label": "Fit"}}, "jump_link": {"label": "URL", "info": "Once the link is configured, you can redirect the page by clicking the image directly"}}}}, "presets": {"presets__0": {"category": "Graphic display", "name": "Content collage"}}}, "picture-floating": {"name": "Switch image on hover", "settings": {"title": {"label": "Title"}, "title_font": {"label": "Title font"}, "title_size": {"label": "Heading size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}, "title_split": {"label": "Stitched title"}, "description": {"label": "Description"}, "image_height": {"label": "Image height"}, "mobile_slide_duration": {"label": "Automatic time switch on mobile", "unit": "s"}, "is_fullscreen": {"label": "Full screen"}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image", "info": "Recommended minimum width of 820 px, height is adjustable"}, "link": {"label": "URL"}}}}, "presets": {"presets__0": {"category": "Graphic display", "name": "Switch image on hover"}}}, "product-recommendations": {"name": "Recommended products", "settings": {"title": {"label": "Title"}, "title_size": {"label": "Heading size", "options__0": {"label": "Large"}, "options__1": {"label": "Center"}, "options__2": {"label": "Small"}}, "products_to_show": {"label": "Display product quantity"}, "pc_cols": {"label": "No. of rows on desktop"}, "mobile_cols": {"label": "Number of columns on mobile", "options__0": {"label": "1 column"}, "options__1": {"label": "2 columns"}}, "enable_horizontal_slider": {"label": "Turn on left-right scroll", "info": "Apply when more products are displayed than columns available"}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__0": {"label": "Product image"}, "product_image_ratio": {"label": "Product image ratio", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "product_fill_type": {"label": "Image fill method", "options__0": {"label": "Fit"}, "options__1": {"label": "Fill"}}, "show_secondary_image": {"label": "Display next product image on hover"}, "group_header__1": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"image": {"name": "Image"}, "title": {"name": "Title"}, "price": {"name": "Price"}, "highlight": {"name": "Feature description"}, "text": {"name": "Text", "settings": {"text": {"label": "Text"}}}, "divider": {"name": "Divider"}, "brand": {"name": "Brand"}, "sku": {"name": "sku"}, "quick_add_button": {"name": "Quick add to cart button"}}}, "promotional-banner": {"name": "Promotion banner", "settings": {"background_image": {"label": "Background image"}, "background_image_width": {"label": "Background image width", "options__0": {"label": "1/1"}, "options__1": {"label": "1/2"}, "options__2": {"label": "2/3"}}, "background_color": {"label": "Background color"}, "front_image": {"label": "Promotion image"}, "front_image_width": {"label": "Promotion image width"}, "front_layout": {"label": "Image position", "options__0": {"label": "Image to right"}, "options__1": {"label": "Image to left"}}, "front_card_mode": {"label": "Display text color block"}, "front_card_border_radius": {"label": "Card corner radius"}, "front_card_background_color": {"label": "Card background color"}, "front_card_text_color": {"label": "Card text color"}, "group_header__0": {"label": "Text"}, "sub_title": {"label": "Subtitle"}, "title": {"label": "Title"}, "description": {"label": "Description"}, "btn_text": {"label": "Button text"}, "btn_link": {"label": "URL"}, "button_style": {"label": "Button style", "options__0": {"label": "Main button"}, "options__1": {"label": "Outline button"}, "options__2": {"label": "Underline"}}, "text_align": {"label": "Text alignment", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "text_color": {"label": "Font color"}}, "presets": {"presets__0": {"category": "Graphic display", "name": "Promotion banner"}}}, "promotional-image": {"name": "Promotion image", "settings": {"title": {"label": "Title"}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Promotion image"}, "link": {"label": "URL"}, "text": {"label": "Promotional text"}, "show_text_background": {"label": "Display text color block"}, "text_border_radius": {"label": "Text block corner radius"}, "text_background_color": {"label": "Text block color"}, "text_color": {"label": "Text color"}, "text_position": {"label": "Text position", "options__0": {"label": "Text to the left"}, "options__1": {"label": "Text to the right"}}, "icon": {"label": "Logo", "options__0": {"label": "Don't display"}, "options__1": {"label": "Secure payment"}, "options__2": {"label": "Package"}, "options__3": {"label": "Email"}, "options__4": {"label": "Locate"}, "options__5": {"label": "Customer"}, "options__6": {"label": "Cha<PERSON>"}, "options__7": {"label": "Gifts"}, "options__8": {"label": "Mobile phone No."}, "options__9": {"label": "Question mark"}, "options__10": {"label": "Logistics"}, "options__11": {"label": "Discount tag"}}, "custom_icon": {"label": "Custom icon"}}}}, "presets": {"presets__0": {"category": "Graphic display", "name": "Promotion image"}}}, "rich-text": {"name": "Rich text", "settings": {"desktop_content_position": {"label": "Content position on desktop", "info": "Automatically optimize display position on mobile", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "normal_width": {"label": "Use standard column width"}, "show_decoration": {"label": "Display decorative lines"}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"heading": {"name": "Title", "settings": {"heading": {"label": "Title"}, "heading_size": {"label": "Title text size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}}}, "text": {"name": "Description", "settings": {"text": {"label": "Body"}}}, "button": {"name": "Buttons", "settings": {"button_text": {"label": "Button text"}, "button_link": {"label": "URL"}, "button_style_secondary": {"label": "Appy button outline style"}, "button_text_2": {"label": "Button text 2"}, "button_link_2": {"label": "URL 2"}, "button_style_secondary_2": {"label": "Appy button outline style"}}}}, "presets": {"presets__0": {"category": "Graphic display", "name": "Rich text"}}}, "shoppable-image": {"name": "Shoppable image", "settings": {"image_pc": {"label": "Image on desktop", "info": "Recommended size: 1680 x 700 px"}, "image_mobile": {"label": "Image on mobile", "info": "Recommended size: 750 x 800 px; if no mobile image is uploaded, desktop image will be used by default"}, "image_full": {"label": "Full screen image"}, "group_header__0": {"label": "Text"}, "text_title": {"label": "Title"}, "description": {"label": "Description"}, "button_text": {"label": "Button text"}, "jump_link": {"label": "URL"}, "text_align": {"label": "Text alignment", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "text_position": {"label": "Text position", "options__0": {"label": "Upper left"}, "options__1": {"label": "Above"}, "options__2": {"label": "Upper right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Center"}, "options__5": {"label": "Right"}, "options__6": {"label": "Bottom left"}, "options__7": {"label": "Below"}, "options__8": {"label": "Bottom right"}}, "text_color": {"label": "Font color"}, "button_text_color": {"label": "Button text color"}}, "blocks": {"product": {"name": "Products", "settings": {"product": {"label": "Products"}, "horizontal_axis_position_pc": {"label": "X-axis on desktop"}, "vertical_axis_position_pc": {"label": "Y-axis on desktop"}, "horizontal_axis_position_mobile": {"label": "X-axis on mobile"}, "vertical_axis_position_mobile": {"label": "Y-axis on mobile"}}}, "text": {"name": "Text display", "settings": {"title": {"label": "Title"}, "desc": {"label": "Description"}, "button_text": {"label": "Button text"}, "button_href": {"label": "URL"}, "horizontal_axis_position_pc": {"label": "X-axis on desktop"}, "vertical_axis_position_pc": {"label": "Y-axis on desktop"}, "horizontal_axis_position_mobile": {"label": "X-axis on mobile"}, "vertical_axis_position_mobile": {"label": "Y-axis on mobile"}}}}, "presets": {"presets__0": {"category": "Product display", "name": "Shoppable image"}}}, "sign-up-and-save": {"name": "Email subscription", "settings": {"color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "full_screen_width": {"label": "Full screen width"}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"title": {"name": "Subscription title", "settings": {"title": {"label": "Subscription title"}, "fontSize": {"label": "Font size", "options__0": {"label": "Large"}, "options__1": {"label": "Center"}, "options__2": {"label": "Small"}}}}, "desc": {"name": "Subscription description", "settings": {"description": {"label": "Subscription description"}}}, "button": {"name": "Subscription field", "settings": {"button_text": {"label": "Button text"}, "placeholder": {"label": "Input box prompt text"}}}}, "presets": {"presets__0": {"category": "Customer operation", "name": "Email subscription"}}}, "slideshow": {"name": "Slideshow", "block_info": "[View the suggested sizes of image templates](https://shoplineapphelp.zendesk.com/hc/articles/4406422263577)", "settings": {"section_height": {"label": "Height on desktop", "options__0": {"label": "Fit to Image 1"}, "options__1": {"label": "450px"}, "options__2": {"label": "550px"}, "options__3": {"label": "650px"}, "options__4": {"label": "750px"}, "options__5": {"label": "Full screen"}}, "mobile_height": {"label": "Height on mobile", "options__0": {"label": "Fit to Image 1"}, "options__1": {"label": "250px"}, "options__2": {"label": "300px"}, "options__3": {"label": "400px"}, "options__4": {"label": "500px"}, "options__5": {"label": "Full screen"}}, "full_screen": {"label": "Full screen"}, "style": {"label": "Pagination style", "options__0": {"label": "Arrows"}, "options__1": {"label": "Progress bar"}, "options__2": {"label": "Dots"}}, "autoplay": {"label": "Turn on auto-switch"}, "autoplay_speed": {"label": "Change slides every", "unit": "s"}, "mobile_content_layout": {"label": "Image-text layout on mobile", "options__0": {"label": "Text inside image"}, "options__1": {"label": "Text below image"}, "options__2": {"label": "Text overlapping image"}}}, "blocks": {"image": {"name": "Image", "settings": {"image": {"label": "Image on desktop"}, "image_mobile": {"label": "Image on mobile"}, "image_show_position": {"label": "Image display area", "options__0": {"label": "Upper left"}, "options__1": {"label": "Above"}, "options__2": {"label": "Upper right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Center"}, "options__5": {"label": "Right"}, "options__6": {"label": "Bottom left"}, "options__7": {"label": "Below"}, "options__8": {"label": "Bottom right"}}, "overlay_opacity": {"label": "Layer mask opacity"}, "text_mask": {"label": "Text overlay"}, "text_mask_color": {"label": "Text overlay color", "options__0": {"label": "Dark"}, "options__1": {"label": "Light"}}, "pc_text_position": {"label": "Content position on desktop", "options__0": {"label": "Upper left"}, "options__1": {"label": "Above"}, "options__2": {"label": "Upper right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Center"}, "options__5": {"label": "Right"}, "options__6": {"label": "Bottom left"}, "options__7": {"label": "Below"}, "options__8": {"label": "Bottom right"}}, "mb_text_position": {"label": "Content position on mobile", "options__0": {"label": "Upper left"}, "options__1": {"label": "Above"}, "options__2": {"label": "Upper right"}, "options__3": {"label": "Left"}, "options__4": {"label": "Center"}, "options__5": {"label": "Right"}, "options__6": {"label": "Bottom left"}, "options__7": {"label": "Below"}, "options__8": {"label": "Bottom right"}}, "pc_text_color": {"label": "Font color"}, "btn_bg_color": {"label": "Button background color"}, "btn_text_color": {"label": "Button text color"}, "btn_border_color": {"label": "Button border color"}, "pc_text_align": {"label": "Content alignment on desktop", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "mobile_text_align": {"label": "Content alignment on mobile", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "group_header__0": {"label": "Content"}, "sub_title": {"label": "Subtitle"}, "title": {"label": "Main title"}, "title_size": {"label": "Main title font size"}, "subheading": {"label": "Body"}, "pc_content_width": {"label": "Content width on desktop", "options__0": {"label": "Adaptive"}}, "text_color": {"label": "Text color for mobile"}, "text_area_background_color": {"label": "Text area background color on mobile"}, "group_header__1": {"label": "Button or image redirection", "info": "If there's no button text, click the image to redirect"}, "link_text": {"label": "Button text"}, "link": {"label": "URL"}, "is_profile_link": {"label": "Use outline button style"}, "link_text_2": {"label": "Button text 2"}, "link_2": {"label": "URL 2"}, "is_profile_link2": {"label": "Use outline button style2"}}}}, "presets": {"presets__0": {"category": "Graphic display", "name": "Slideshow"}}}, "spacing": {"name": "Spacing adjustment", "settings": {"pc_height": {"label": "Height on desktop"}, "m_height": {"label": "Height on mobile"}, "background_color": {"label": "Color"}}, "presets": {"presets__0": {"category": "Other", "name": "Spacing adjustment"}}}, "text-columns-with-image": {"name": "Text columns with images", "settings": {"title": {"label": "Title"}, "title_font_size": {"label": "Heading size", "options__0": {"label": "Large"}, "options__1": {"label": "Center"}, "options__2": {"label": "Small"}}, "image_width": {"label": "Image width", "options__0": {"label": "Same as column width"}, "options__1": {"label": "Half of column width"}, "options__2": {"label": "1/3 of column width"}}, "image_ratio": {"label": "Image ratio", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "1:1"}, "options__3": {"label": "Round"}}, "pc_cols": {"label": "No. of rows on desktop"}, "text_align": {"label": "Content alignment"}, "show_block_bg": {"label": "Background", "options__0": {"label": "Transparent"}, "options__1": {"label": "List display"}}, "button_text": {"label": "Button text"}, "jump_link": {"label": "URL"}, "show_touch": {"label": "Turn on left-right scroll on mobile"}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "blocks": {"item": {"name": "Image", "settings": {"image": {"label": "Image"}, "title": {"label": "Title"}, "description": {"label": "Description"}, "button_text": {"label": "Button text"}, "jump_link": {"label": "URL"}}}}, "presets": {"presets__0": {"category": "Graphic display", "name": "Text columns with images"}}}, "text-with-image": {"name": "Graphic stitching module", "settings": {"layout": {"label": "Image-text layout", "options__0": {"label": "Title to the left"}, "options__1": {"label": "Title to the right"}}, "group_header__0": {"label": "Content"}, "title": {"label": "Title"}, "title_font_size": {"label": "Title text size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}, "content": {"label": "Content"}, "button_text": {"label": "Button text"}, "button_link": {"label": "URL"}, "group_header__1": {"label": "Image"}, "image_1": {"label": "Image 1", "info": "Recommended size: 1200x1370px"}, "url_1": {"label": "Jump link 1"}, "product_1": {"label": "Product 1"}, "image_2": {"label": "Image 2", "info": "Recommended size: 1200x1370px"}, "url_2": {"label": "URL 2"}, "product_2": {"label": "Product 2"}, "show_image_line": {"label": "Display image decorative line"}, "image_height": {"label": "Image height", "options__0": {"label": "Auto"}, "options__1": {"label": "High"}, "options__2": {"label": "Low"}}, "group_header__2": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "presets": {"presets__0": {"category": "Graphic display", "name": "Image with text"}}}, "video": {"name": "Video", "settings": {"title": {"label": "Title"}, "title_size": {"label": "Heading size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}, "cover": {"label": "Cover image"}, "internal_video": {"label": "Local upload"}, "external_video": {"label": "External video link", "info": "Enter the embedded link from YouTube or Vimeo"}, "video_auto_play": {"label": "Video autoplay", "info": "Mute by default on autoplay"}, "full_width": {"label": "Full screen width"}, "color_scheme": {"label": "Color", "options__0": {"label": "None"}, "options__1": {"label": "Color 1"}, "options__2": {"label": "Color 2"}, "options__3": {"label": "Color 3"}}, "group_header__0": {"label": "Padding by section"}, "padding_top": {"label": "Top padding"}, "padding_bottom": {"label": "Bottom padding"}}, "presets": {"presets__0": {"category": "Graphic display", "name": "Video"}}}}, "settings_schema": {"logo": {"name": "Logo", "settings": {"logo": {"label": "Logo"}, "desktop_logo_width": {"label": "Maximum LOGO width on PC", "info": "This setting is the maximum displayable width, the width will be adjusted according to the actual screen size"}, "desktop_logo_height": {"label": "Maximum LOGO height on PC"}, "mobile_logo_width": {"label": "Maximum LOGO width on mobile device", "info": "This setting is the maximum displayable width, the width will be adjusted according to the actual screen size"}, "mobile_logo_height": {"label": "Maximum LOGO height on mobile device"}}}, "color": {"name": "Color", "settings": {"group_header__0": {"label": "Basics"}, "color_page_background": {"label": "Background"}, "color_text": {"label": "Text"}, "color_light_text": {"label": "Text in light colors"}, "color_entry_line": {"label": "Lines and borders"}, "color_card_background": {"label": "Card background", "info": "Used for product, collection, and blog cards"}, "color_card_text": {"label": "Card text", "info": "Used for product, collection, and blog cards"}, "group_header__1": {"label": "Buttons"}, "color_button_background": {"label": "Button background"}, "color_button_text": {"label": "Button text"}, "color_button_secondary_background": {"label": "Secondary button background", "info": "Secondary buttons are usually in an outline state, including Add to cart, Quick add to cart, etc."}, "color_button_secondary_text": {"label": "Secondary button text"}, "color_button_secondary_border": {"label": "Secondary button border"}, "group_header__2": {"label": "Products"}, "color_sale": {"label": "Price"}, "color_discount": {"label": "Discount amount"}, "color_discount_tag_background": {"label": "Discount tag"}, "color_discount_tag_text": {"label": "Discount tag text"}, "group_header__3": {"label": "Other"}, "color_cart_dot": {"label": "Shopping cart prompt points"}, "color_cart_dot_text": {"label": "Shopping cart prompt text"}, "color_image_background": {"label": "Image background"}, "color_image_loading_background": {"label": "Loading image background"}, "color_mask": {"label": "<PERSON>e pop-up window background"}, "color_shadow": {"label": "Shadow"}, "group_header__4": {"label": "Color 1"}, "color_scheme_1_bg": {"label": "Background color"}, "color_scheme_1_text": {"label": "Font color"}, "group_header__5": {"label": "Color 2"}, "color_scheme_2_bg": {"label": "Background color"}, "color_scheme_2_text": {"label": "Font color"}, "group_header__6": {"label": "Color 3"}, "color_scheme_3_bg": {"label": "Background color"}, "color_scheme_3_text": {"label": "Font color"}}}, "font": {"name": "Typography", "settings": {"group_header__0": {"label": "Title"}, "title_font_family": {"label": "Typography"}, "title_letter_spacing": {"label": "Font-spacing"}, "title_font_size": {"label": "Font size"}, "title_line_height": {"label": "Font line height"}, "title_uppercase": {"label": "All Caps for texts"}, "group_header__1": {"label": "Body"}, "body_font_family": {"label": "Typography"}, "body_letter_spacing": {"label": "Font-spacing"}, "body_font_size": {"label": "Font size"}, "body_line_height": {"label": "Font line height"}}}, "layout": {"name": "Layout", "settings": {"page_width": {"label": "Page width"}, "section_vertical_gap": {"label": "Component vertical interval"}, "group_header__0": {"label": "Grid", "info": "Affects areas with multiple columns or rows"}, "grid_horizontal_space": {"label": "Horizontal spacing"}, "grid_vertical_space": {"label": "Vertical spacing"}}}, "button": {"name": "Buttons", "settings": {"btn_hover_animation": {"label": "Hover effect", "options__0": {"label": "Left-to-right sweep"}, "options__1": {"label": "Overlay projection"}, "options__2": {"label": "Enlarge on hover"}, "options__3": {"label": "Fill to the right"}}, "group_header__0": {"label": "Border"}, "btn_border_thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "btn_border_opacity": {"label": "Opacity"}, "btn_border_radius": {"label": "Border radius"}, "group_header__1": {"label": "Shadow"}, "btn_shadow_opacity": {"label": "Opacity"}, "btn_shadow_offset_x": {"label": "Vertical offset"}, "btn_shadow_offset_y": {"label": "Horizontal offset"}, "btn_shadow_blur": {"label": "Blur"}}}, "sku": {"name": "Variant picker", "settings": {"group_header__0": {"label": "Border"}, "sku_selector_border_thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "sku_selector_border_opacity": {"label": "Opacity"}, "sku_selector_border_radius": {"label": "Border radius"}, "group_header__1": {"label": "Shadow"}, "sku_selector_shadow_opacity": {"label": "Opacity"}, "sku_selector_shadow_offset_x": {"label": "Vertical offset"}, "sku_selector_shadow_offset_y": {"label": "Horizontal offset"}, "sku_selector_shadow_blur": {"label": "Blur"}}}, "input": {"name": "Inputs", "settings": {"group_header__0": {"label": "Border"}, "input_border_thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "input_border_opacity": {"label": "Opacity"}, "input_border_radius": {"label": "Border radius"}, "group_header__1": {"label": "Shadow"}, "input_shadow_opacity": {"label": "Opacity"}, "input_shadow_offset_x": {"label": "Vertical offset"}, "input_shadow_offset_y": {"label": "Horizontal offset"}, "input_shadow_blur": {"label": "Blur"}}}, "product": {"name": "Products", "settings": {"group_header__0": {"label": "Product list"}, "enable_quick_view": {"label": "Quick add to cart"}, "quick_view_button_pc_style": {"label": "Quick Add to cart style on desktop", "info": "If set as a button style, you can adjust the button position using the Product list > Quick add to cart button block", "options__0": {"label": "Buttons"}, "options__1": {"label": "Logo"}}, "quick_view_button_mobile_style": {"label": "Quick Add to cart style on mobile", "info": "If set as a button style, you can adjust the button position using the Product list > Quick add to cart button block", "options__0": {"label": "Logo"}, "options__1": {"label": "Buttons"}}, "product_title_show_type": {"label": "Product title display", "options__0": {"label": "Full display"}, "options__1": {"label": "Display up to 1 row"}, "options__2": {"label": "Display up to 2 rows"}}, "product_pc_title_show": {"label": "Display product title on desktop"}, "product_mobile_title_show": {"label": "Product title display on mobile"}, "group_header__1": {"label": "Product card discount tag", "info": "This only affects the promotion tag. You can set the tag display on the product details page in Product details > Price."}, "product_discount": {"label": "Display discount"}, "product_discount_tag_style": {"label": "Tag display rules", "options__0": {"label": "Sale tag"}, "options__1": {"label": "Save tag"}}, "product_discount_style": {"label": "Discount display format", "info": "This takes effect when the save tag is selected as the promotion tag.", "options__0": {"label": "Display discount amount"}, "options__1": {"label": "Display percentage"}}, "product_discount_size": {"label": "Promotion tag size", "options__0": {"label": "Center"}, "options__1": {"label": "Small"}}, "product_discount_position": {"label": "Promotion tag position", "options__0": {"label": "Upper left"}, "options__1": {"label": "Upper right"}, "options__2": {"label": "Bottom left"}, "options__3": {"label": "Bottom right"}}, "product_discount_radius": {"label": "Promotion tag radius"}}}, "product_card": {"name": "Product cards", "settings": {"product_card_style": {"label": "Style", "options__0": {"label": "Standard"}, "options__1": {"label": "Cards"}}, "product_card_image_padding": {"label": "Image margin", "info": "Spacing between image and background"}, "product_card_content_align": {"label": "Align product card content", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}}, "group_header__0": {"label": "Border"}, "product_card_border_thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "product_card_border_opacity": {"label": "Opacity"}, "product_card_border_radius": {"label": "Border radius"}, "group_header__1": {"label": "Shadow"}, "product_card_shadow_opacity": {"label": "Opacity"}, "product_card_shadow_offset_x": {"label": "Vertical offset"}, "product_card_shadow_offset_y": {"label": "Horizontal offset"}, "product_card_shadow_blur": {"label": "Blur"}}}, "collection_card": {"name": "Collection cards", "settings": {"collection_card_style": {"label": "Style", "options__0": {"label": "Standard"}, "options__1": {"label": "Cards"}}, "collection_card_image_padding": {"label": "Image margin", "info": "Spacing between image and background"}, "collection_card_content_align": {"label": "Content alignment", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}}, "group_header__0": {"label": "Border"}, "collection_card_border_thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "collection_card_border_opacity": {"label": "Opacity"}, "collection_card_border_radius": {"label": "Border radius"}, "group_header__1": {"label": "Shadow"}, "collection_card_shadow_opacity": {"label": "Opacity"}, "collection_card_shadow_offset_x": {"label": "Vertical offset"}, "collection_card_shadow_offset_y": {"label": "Horizontal offset"}, "collection_card_shadow_blur": {"label": "Blur"}}}, "blog_card": {"name": "Blog cards", "settings": {"blog_card_style": {"label": "Style", "options__0": {"label": "Standard"}, "options__1": {"label": "Cards"}}, "blog_card_image_padding": {"label": "Image margin", "info": "Spacing between image and background"}, "blog_card_content_align": {"label": "Content alignment", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}}, "group_header__0": {"label": "Border"}, "blog_card_border_thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "blog_card_border_opacity": {"label": "Opacity"}, "blog_card_border_radius": {"label": "Border radius"}, "group_header__1": {"label": "Shadow"}, "blog_card_shadow_opacity": {"label": "Opacity"}, "blog_card_shadow_offset_x": {"label": "Vertical offset"}, "blog_card_shadow_offset_y": {"label": "Horizontal offset"}, "blog_card_shadow_blur": {"label": "Blur"}}}, "other_card": {"name": "Other cards", "settings": {"group_header__0": {"label": "Border"}, "card_border_thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "card_border_opacity": {"label": "Opacity"}, "card_border_radius": {"label": "Border radius"}, "group_header__1": {"label": "Shadow"}, "card_shadow_opacity": {"label": "Opacity"}, "card_shadow_offset_x": {"label": "Vertical offset"}, "card_shadow_offset_y": {"label": "Horizontal offset"}, "card_shadow_blur": {"label": "Blur"}}}, "content": {"name": "Content Containers", "settings": {"group_header__0": {"label": "Border"}, "content_border_thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "content_border_opacity": {"label": "Opacity"}, "content_border_radius": {"label": "Border radius"}, "group_header__1": {"label": "Shadow"}, "content_shadow_opacity": {"label": "Opacity"}, "content_shadow_offset_x": {"label": "Vertical offset"}, "content_shadow_offset_y": {"label": "Horizontal offset"}, "content_shadow_blur": {"label": "Blur"}}}, "media_files": {"name": "Media", "settings": {"group_header__0": {"label": "Border"}, "media_border_thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "media_border_opacity": {"label": "Opacity"}, "media_border_radius": {"label": "Border radius"}, "group_header__1": {"label": "Shadow"}, "media_shadow_opacity": {"label": "Opacity"}, "media_shadow_offset_x": {"label": "Vertical offset"}, "media_shadow_offset_y": {"label": "Horizontal offset"}, "media_shadow_blur": {"label": "Blur"}}}, "dropdown_menu": {"name": "Dropdowns and pop-ups", "settings": {"group_header__0": {"label": "Border"}, "menu_modal_border_thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "menu_modal_border_opacity": {"label": "Opacity"}, "menu_modal_border_radius": {"label": "Border radius"}, "group_header__1": {"label": "Shadow"}, "menu_modal_shadow_opacity": {"label": "Opacity"}, "menu_modal_shadow_offset_x": {"label": "Vertical offset"}, "menu_modal_shadow_offset_y": {"label": "Horizontal offset"}, "menu_modal_shadow_blur": {"label": "Blur"}}}, "drawer": {"name": "Drawers", "settings": {"group_header__0": {"label": "Border"}, "drawer_border_thickness": {"label": "<PERSON><PERSON><PERSON><PERSON>"}, "drawer_border_opacity": {"label": "Opacity"}, "group_header__1": {"label": "Shadow"}, "drawer_shadow_opacity": {"label": "Opacity"}, "drawer_shadow_offset_x": {"label": "Vertical offset"}, "drawer_shadow_offset_y": {"label": "Horizontal offset"}, "drawer_shadow_blur": {"label": "Blur"}}}, "cart": {"name": "<PERSON><PERSON>", "settings": {"cart_type": {"label": "Cart style", "options__0": {"label": "Drawer"}, "options__1": {"label": "Enter the shopping cart page"}, "options__2": {"label": "Pop-up notification"}}, "group_header__0": {"label": "Empty cart recommendations"}, "cart_empty_recommend_title": {"label": "Title"}, "cart_empty_recommend_collection": {"label": "Recommend product collections"}, "cart_empty_recommend_product_to_show": {"label": "Maximum product quantity"}, "cart_empty_recommend_product_image_ratio": {"label": "Product image ratio", "options__0": {"label": "Original image ratio"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "cart_empty_recommend_product_image_fill_type": {"label": "Image fill method", "options__0": {"label": "Fit"}, "options__1": {"label": "Fill"}}}}, "checkout": {"name": "Checkout", "settings": {"group_header__0": {"label": "Banner"}, "co_banner_pc_img": {"label": "Image on desktop"}, "co_banner_phone_img": {"label": "Image on mobile", "info": "Display the image of PC size on mobile device if there aren't any images uploaded for mobiles."}, "co_banner_pc_height": {"label": "Height on desktop", "options__0": {"label": "Auto"}, "options__1": {"label": "High"}, "options__2": {"label": "Low"}}, "co_banner_phone_height": {"label": "Height on mobile", "options__0": {"label": "Auto"}, "options__1": {"label": "High"}, "options__2": {"label": "Low"}}, "co_banner_img_show": {"label": "Image display area", "options__0": {"label": "Top"}, "options__1": {"label": "Center"}, "options__2": {"label": "Bottom"}}, "co_full_screen": {"label": "Full-screen width on PC"}, "group_header__1": {"label": "Logo"}, "co_checkout_image": {"label": "Custom image"}, "co_logo_size": {"label": "Logo size", "options__0": {"label": "Small"}, "options__1": {"label": "Center"}, "options__2": {"label": "Large"}}, "co_logo_position": {"label": "Logo position", "options__0": {"label": "Left"}, "options__1": {"label": "Center"}, "options__2": {"label": "Right"}}, "group_header__2": {"label": "Content"}, "co_bg_image": {"label": "Background image"}, "co_background_color": {"label": "Background color"}, "co_form_bg_color": {"label": "Background color of sheet", "options__0": {"label": "White"}, "options__1": {"label": "Transparent"}}, "group_header__3": {"label": "Order summary"}, "co_order_bg_image": {"label": "Background image"}, "co_order_background_color": {"label": "Background color"}, "group_header__4": {"label": "Typography"}, "co_type_title_font": {"label": "Title"}, "co_type_body_font": {"label": "Body"}, "group_header__5": {"label": "Color"}, "co_color_btn_bg": {"label": "Button background"}, "co_color_err_color": {"label": "Error reporting", "info": "Error notification & highlighted border"}, "co_color_msg_color": {"label": "Highlighted information", "info": "Link, checkbox, input box border & drop-down menu"}}}, "media_sosial": {"name": "Social media", "settings": {"group_header__0": {"label": "Account", "info": "Guide customers to your social media account"}, "show_official_icon": {"label": "Official icon style"}, "social_facebook_link": {"label": "Facebook"}, "social_twitter_link": {"label": "X（Twitter）"}, "social_pinterest_link": {"label": "Pinterest"}, "social_instagram_link": {"label": "Instagram"}, "social_snapchat_link": {"label": "Snapchat"}, "social_tiktok_link": {"label": "TikTok"}, "social_youtube_link": {"label": "Youtube"}, "social_vimeo_link": {"label": "Vimeo"}, "social_tumblr_link": {"label": "Tumblr"}, "social_linkedin_link": {"label": "Linkedin"}, "social_whatsapp_link": {"label": "WhatsApp"}, "social_line_link": {"label": "Line"}, "social_kakao_link": {"label": "<PERSON><PERSON><PERSON>"}, "group_header__1": {"label": "Share", "info": "Turn on sharing on product details page and blog page"}, "show_official_share_icon": {"label": "Official icon style"}, "show_social_name": {"label": "Display social media name"}, "share_to_facebook": {"label": "Share on Facebook"}, "share_to_twitter": {"label": "Tweet on <PERSON> (Twitter)"}, "share_to_pinterest": {"label": "Pin on Pinterest"}, "share_to_line": {"label": "Share on LINE"}, "share_to_whatsapp": {"label": "Share on WhatsApp"}, "share_to_tumblr": {"label": "Share on Tumblr"}}}, "search_behavior": {"name": "Search behavior", "settings": {"show_search_goods_price": {"label": "Display prices of recommended products"}}}, "breadcrumb": {"name": "Breadcrumbs", "settings": {"show_pc_breadcrumb": {"label": "Display breadcrumbs on desktop"}, "show_mobile_breadcrumb": {"label": "Display breadcrumbs on mobile"}}}, "favicon": {"name": "Favicon", "settings": {"favicon_image": {"label": "Site icon", "info": "Size: 32 x 32 px"}}}}}