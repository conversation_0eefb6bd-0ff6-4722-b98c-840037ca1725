{"blog": {"blog": {"next": "Kitas", "previous": "<PERSON><PERSON><PERSON><PERSON>", "read_more": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "related_articles": "<PERSON><PERSON><PERSON><PERSON>"}, "blog_list": {"empty_description": "Šiame tinklaraštyje nėra jokių tinklaraščio įrašų.", "readmore": "<PERSON><PERSON><PERSON><PERSON>", "title": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view_more": "<PERSON><PERSON><PERSON>"}, "comment": {"comment": "k<PERSON><PERSON><PERSON><PERSON>", "comment_audit_tip": "Jūsų komentaras pateiktas ir jį reikia per<PERSON>.", "comment_empty": "Prašome užpildyti komentarus", "email": "<PERSON><PERSON><PERSON>", "email_empty": "<PERSON><PERSON> pa<PERSON><PERSON> la<PERSON> negal<PERSON> bū<PERSON> t<PERSON>", "email_format": "Įveskite teisingą el. pa<PERSON>to ad<PERSON>", "form_title": "k<PERSON><PERSON><PERSON><PERSON>", "loadmore": "pakrauti daugiau", "name": "<PERSON><PERSON><PERSON> ir pava<PERSON>", "name_empty": "Prašome įrašyti vardą", "submit": "Pat<PERSON><PERSON><PERSON>", "submit_audit_tip": "<PERSON><PERSON><PERSON><PERSON><PERSON>, la<PERSON><PERSON>", "success": "Komentaras p<PERSON>elbt<PERSON> sėk<PERSON>ai", "title": "{{comment_length}} komentarai", "moderated_tip": "<PERSON><PERSON><PERSON><PERSON>, kad p<PERSON><PERSON>, komenta<PERSON> reikia <PERSON>", "post_comment": "Skelbti komentarą", "single_count": "{{count}}komentaras "}, "general": {"back_to": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{{collectionTitle}}}", "back": "Atgal į tinklaraštį "}}, "cart": {"b2b": {"amount": {"increase": {"desc": "<PERSON><PERSON> prekė parduodama tik nuo sumos {{num}}"}, "most": {"desc": "<PERSON><PERSON> prekė parduodama tik suma {{num}} ne daugiau kaip"}, "noIncrement": {"desc": "<PERSON><PERSON> prekė parduodama tik prieaugiu {{num}}"}}}, "cart": {"buy_now": "ĮSIGYKITE DABAR", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "confirm": "OK", "got_it": "<PERSON><PERSON><PERSON>", "added_to_cart": "Pridėta prie jū<PERSON>ų vežimėlio", "cart_subtotal": "<PERSON><PERSON><PERSON><PERSON> ve<PERSON> suma", "cart_view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> vežimėlį"}, "checkout": {"max_amount_limit": "Viršijote maksimalią apmokėjimo sumą"}, "checkout_proceeding": {"checkout": "PIRKIMO UŽBAIGIMAS", "checkout_proceed": "Toliau nurodytos prekės virš<PERSON> pirkimo apribojimą, vis dar tęskite pirkimą?", "continue": "<PERSON><PERSON><PERSON><PERSON>", "continue_shopping": "TĘSTI APSIPIRKIMĄ", "day": "dienos", "remove": "<PERSON><PERSON><PERSON>", "return_to_cart": "Grįžti į vežimėlį", "subtotal": "Ta<PERSON><PERSON><PERSON> suma", "tax_included": "Mokesčiai įskaičiuoti"}, "couponCode": {"exceedLimit": "Nuolaidų kodai viršija kiekio limitą", "existCode": "<PERSON><PERSON><PERSON><PERSON> kodas jau yra", "list": {"unavailable": "Baigėsi visų nuolaidų kodų galiojimo laikas"}, "notSupport": {"stacked": "Nuolaidų kodai nesukeliami"}, "unavailable": "Baigėsi nuolaid<PERSON> kodas"}, "discount": {"coupon_code_amount": "<PERSON><PERSON><PERSON>", "coupon_code_length_limit": "<PERSON><PERSON><PERSON> kodas negali viršyti {{range}} simbolių", "expected_discount": "<PERSON><PERSON><PERSON><PERSON>"}, "discount_item": {"buy_limit5": "<PERSON>o pa<PERSON>iu metu galite pirkti daugiausia {{stock}} prekių su nuolaida"}, "discount_price": {"buy_limit1": "Tik {{stock}} vnt. liko su <PERSON>ia nuo<PERSON>a", "buy_limit2": "<PERSON>o pa<PERSON>iu metu galite pirkti daugiausia {{stock}} prekių su nuolaida", "buy_limit3": "Galite tik p<PERSON>ti <PERSON> {{stock}} vnt. su nuolaidomis"}, "error": {"default": "Nepavyko įkelti išteklių", "noshipping": "Nėra pasirinktinio krovos plano", "order": "Nepavyko pateikti užsakymo užklausos", "renew": "Nepavyko atnaujinti duomenų"}, "general": {"continue_as_a_guest": "Tęskite kaip s<PERSON>", "support_oneTime_purchase_only": "Produktas palaikomas tik <PERSON>iam pirk<PERSON>ui", "support_subscription_only": "Produktas palaikomas tik pirkimui su po prenumerata"}, "item": {"add_limit2": "<PERSON><PERSON><PERSON><PERSON> vien<PERSON> pre<PERSON> k<PERSON> – 99999 vienetai", "click_to_view": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad <PERSON>", "custom_preview": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "empty_cart": "Jūsų vežimėlis šiuo metu tušč<PERSON>.", "item_left": "{{stock}} vnt. <PERSON>", "market": {"illegal": {"excludedState": "<PERSON><PERSON><PERSON> pre<PERSON> neleg<PERSON> ir ji pa<PERSON>lint<PERSON> i<PERSON>"}}, "none_existent": "<PERSON><PERSON><PERSON>"}, "next_step": {"calculated_taxes_fees": "Mokesčiai ir siuntimas apskaičiuotas galutiniam pirkimui", "included_taxes_fees": "Į kainą įskaičiuoti mokesčiai ir pristatymas apskaič<PERSON>oja<PERSON> at<PERSON>", "taxes_and_shipping_policy_at_checkout_html": "Mokesčiai ir <a href=\"{{ link }}\">siunt<PERSON>s</a> apskaičiuoti atsiskaitant", "taxes_included_and_shipping_policy_html": "<PERSON><PERSON><PERSON> įtrauktas. <a href=\"{{link}}\">Siuntimas</a> apskaičiuotas atsiskaitant.", "taxes_included_but_shipping_at_checkout": "Mokes<PERSON> įtrauktas atsiskaitant ir siuntimo mokest<PERSON> a<PERSON>"}, "notices": {"change_password_after_verification": "G<PERSON><PERSON> keisti slaptažodį po patikrinimo", "excess_product": "Toliau nurodytos prek<PERSON> virš<PERSON> p<PERSON> a<PERSON>, prašome pakeisti kiekį.", "product_amount_limit": "Atsiprašome! Prekių skaičius krepšelyje viršija apribojimą.", "product_selected_invalid": "Atsipraš<PERSON>, jūsų pasirinktos prekės negalima įsigyti. Prašome pasirinkti iš naujo", "store_nonexistent": "Parduotuvė neeg<PERSON>"}, "order": {"invalid_order_number": "neteisingas užsakymo numeris", "paymentVoucher": {"downloadDoc": "Atsisiųsti rinkmeną", "format": "Formatas", "formatLimit": "Palaiko .jpg, .png, .pdf rink<PERSON><PERSON>, kai rinkmenos dyd<PERSON> ma<PERSON> nei 20 MB. Galite įkelti daugiausiai 5 vaizdus", "uploadAmount": "{{num}} kuponas (-ai) buvo įkeltas (-i)", "uploadLimit": "Prašome įkelti .jpg, .png, .pdf <PERSON><PERSON><PERSON>, kuri<PERSON> dydis yra ma<PERSON> nei 20MB"}, "payment_voucher_confirmation": "<PERSON><PERSON><PERSON><PERSON>", "shipping_address": "Pristat<PERSON><PERSON>", "sign_in": "arba prisijungti <PERSON> bū<PERSON>s", "tax_included": "Įskaitant {{price}} m<PERSON><PERSON><PERSON>ius", "try_again": "Įkelti nepavyko. <PERSON><PERSON><PERSON><PERSON>, pabandykite dar kartą.", "try_later": "<PERSON><PERSON><PERSON>, bandykite dar kartą vėliau.", "upload_bank_transfer_vouhcer_for_confirmation": "Įkelkite banko pavedimo k<PERSON> ir mes patvir<PERSON>si<PERSON>.", "upload_image_within_10_m_b": "Įkelkite JPG, PNG, GIF vaizdus 10 MB"}, "payment": {"channel": "Mokė<PERSON><PERSON> kanal<PERSON>", "create_time": "Sukurta", "free": "<PERSON><PERSON><PERSON><PERSON>", "log_in": "<PERSON><PERSON><PERSON> registruotis", "payment_details": "Mokėjimo duomenys", "pending_review": "Mokė<PERSON><PERSON>", "reference_number": "Sandorio nuorodos numeris", "status": "Būklė", "subtotal_price": "Ta<PERSON><PERSON><PERSON> suma", "sum": "<PERSON><PERSON>", "total_sum": "<PERSON><PERSON><PERSON> suma", "update_time": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "policy": {"privacy_policy": "Privatumo politika"}, "product": {"unavailable_sale": "Šis produktas nepalai<PERSON> pardavi<PERSON>u"}, "promotion": {"no_promotion": "Netaikoma nuolaida"}, "shpping": {"service_terms": "pas<PERSON><PERSON> te<PERSON> s<PERSON>"}, "stock": {"limit": "Liko tik {{stock}}"}, "subscription": {"information": "Prenumerata"}, "tips": {"agree_with_the_shop": "<PERSON><PERSON><PERSON> su", "change_binding_after_verification": "Galite prijungti / keisti {{value}} po pat<PERSON><PERSON><PERSON>", "forget_password": "Pamiršote slaptažodį", "proceed_to_checkout": "Pereikite prie apmokėjimo", "reset_password": "Atkuriate paskyros {{account}} slaptažodį"}, "cart_error": "<PERSON><PERSON><PERSON> at<PERSON>u<PERSON> k<PERSON>. Bandykite dar kartą.", "cart_quantity_error_html": "Į savo vežimėlį gali įdėti tik {{quantity}} prekes."}, "customer": {"account": {"account_binding": "<PERSON><PERSON><PERSON>", "account_information": "Paskyra", "bind": "<PERSON><PERSON><PERSON>", "birthday": "Gimimo data", "change_password": "<PERSON><PERSON><PERSON><PERSON> slaptažodį", "confirm_btn": "<PERSON><PERSON><PERSON><PERSON>", "confirm_subscription_modal_btn": "Patvirtinti prenumeratą", "confirm_subscription_modal_content": "Pateikėte prenumeratą anksčiau. Patvirtinimo el. pašto laiškas buvo išsiųstas šiuo el. pašto adresu. Galite patvirtinti savo el. pašto laišku arba spausdami šį mygtuką.", "confirm_subscription_modal_title": "Patvirtinkite savo el. pašto prenumeratą.", "credit_card_number": "Kreditin<PERSON><PERSON> k<PERSON> Nr.", "deadline": "<PERSON><PERSON><PERSON> ataskaitos tikslu, j<PERSON><PERSON><PERSON> užsakymas bus vis dar matomas. Jūsų paskyros duomenys bus pašalinti po 9 dienų, galite atša<PERSON>ti procedūrą prieš tą datą", "delete_account": "Uždaryti paskyrą", "delete_account_email": "El. <PERSON>", "delete_account_notify": "Galite teikti prašymą užverti šią paskyrą. <PERSON><PERSON><PERSON><PERSON> bet kokia informacija, identifikuojant šią paskyrą, bus pa<PERSON>lint<PERSON>, įskaitant:", "email": "El. <PERSON>", "email_repeat_tip": "El. paštas buvo registruotas", "email_subscribe_invalid_tip": "Negaliojantis el. pašto adresas", "first_name": "Vardas", "gender": "Lyt<PERSON>", "gender_female": "<PERSON><PERSON><PERSON>", "gender_male": "<PERSON><PERSON><PERSON>", "gender_secret": "Nesakyti", "ip_address": "IP adresas", "last_name": "Pa<PERSON><PERSON>", "modify_email": "Keisti el. paš<PERSON>ą", "modify_email_modal_title": "Keisti el. paš<PERSON>ą", "modify_phone": "Keisti telefoną", "modify_phone_modal_title": "Keisti telefoną", "name": "<PERSON><PERSON><PERSON> ir pava<PERSON>", "next": "<PERSON><PERSON>", "nickname_empty_error": "<PERSON><PERSON>, tiek pavard<PERSON>s eilutės negali būti tu<PERSON>", "not_set": "Nenustatyta", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "personal__info": "Asmeninė informacija", "personal__information": "Asmeninė informacija", "phone": "Mobilusis telefonas", "reset_password_error_hint": "Prašome pirma susieti el. paštą ar mobilųjį telefoną", "revoke": "Atšaukti uždarymą", "revoke_btn": "Atšaukti išėjimą", "revoke_message": "Šiai paskyrai taikoma uždarymo procedūra, paskyr<PERSON> duomenys bus pa<PERSON>lintos {{time}}, galite at<PERSON><PERSON><PERSON> procedūrą prieš tą datą", "save_btn": "IŠSAUGOTI", "select__date": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "subscribe_confirm_tip": "Dėkojame už jūsų prenumeratą.", "timeout_tip": "Baigėsi operacijos skirtasis laikas, patikrinkite dar kartą", "unknow_error_tip": "<PERSON><PERSON><PERSON><PERSON>, bandykite dar kartą vėliau", "unregister": "<PERSON><PERSON><PERSON><PERSON><PERSON> i<PERSON>", "username": "Pavadinimas", "verify_email_message": "<PERSON><PERSON><PERSON> p<PERSON>, turime patikrinti jūsų tapatybę\nĮ jūsų el. paštą {{email}} bus išsiųstas patikros el. pašto <PERSON>", "verify_phone_message": "<PERSON><PERSON>š <PERSON>ždar<PERSON>ami p<PERSON>ą, turime patikrinti jūsų tapatybę\nĮ jūsų mobilųjį telefoną {{phone}} bus išsiųstas patikros praneš<PERSON>s", "address": "<PERSON><PERSON><PERSON>", "delete_deadline_tip": "Vartotojas bus pašalintas {{time}}", "invalidEmail": "Negaliojantis el. pašto adresas"}, "activate": {"account_activated": "Paskyra buvo aktyvuota", "apply_code": "Taikykite kodą", "apply_success": "Tai<PERSON><PERSON>", "button": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "copy_success": "<PERSON><PERSON><PERSON> k<PERSON> n<PERSON>", "discount_title": "Aktyvuokite paskyrą ir gaukite nuolaidos kodą", "normal_title": "Aktyvuokite paskyrą", "normal_title_with_discount_code": "", "subscription_text": "Prenumeruokite el. p<PERSON><PERSON><PERSON>, kad gautumėte naujausią informaciją, prieigą prie išskirtinių sandorių ir dar daugiau.", "success_title": "<PERSON><PERSON><PERSON><PERSON>, gavus did<PERSON> nuolaid<PERSON>", "token_expired": "Jūsų nuoroda nebegalioja"}, "address": {"add_address": "Pridėti nauj<PERSON>", "address": "<PERSON><PERSON><PERSON>", "adress_full_tip": "50 adresai buvo išsaugoti ir negali būti prid<PERSON>ti <PERSON> metu", "common_max": "Ma<PERSON>ia<PERSON> nei {{max}} simbolių!", "common_min": "daugiau nei {{min}} simboliai", "default": "<PERSON><PERSON><PERSON><PERSON>", "delete__address": "<PERSON><PERSON><PERSON>", "edit_last_name_hint": "Prašome įvesti savo pavardę!", "edit_phone_hint": "Prašome įvesti savo mobilųjį telefoną!", "edit_shipping_address": "REDAGUOTI DIUNTIMO ADRESĄ", "max_address_tip": "Sukurti iki 50 adresų", "no_adress_data": "Šiuo metu nėra pristatymo adreso", "set_default_address": "Nustatyti kaip numatytąjį adresą", "address1": "<PERSON><PERSON><PERSON><PERSON> ", "address2": "Išsamus ad<PERSON> 2", "address_full_tip": "50 adresai buvo išsaugoti ir negali būti prid<PERSON>ti <PERSON> metu", "city": "Miestas", "company": "Įmonė", "country": "Šalis / Regionas", "district": "<PERSON><PERSON><PERSON>", "first_name": "Vardas ", "last_name": "Pa<PERSON><PERSON>", "no_address_data": "Šiuo metu nėra pristatymo adreso", "phone": "Telefono numeris", "province": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zip_code": "<PERSON><PERSON><PERSON> k<PERSON>"}, "company": {"account": {"billTitle": "Įmonės atsiskaitymo adresas", "link": "Įmonės sąska<PERSON>s para<PERSON>ška", "shipping": "Įmonės pristatymo adresas", "title": "Įmonės sąska<PERSON>s para<PERSON>ška"}, "address": {"placeholder": "<PERSON><PERSON><PERSON><PERSON>"}, "address2": {"placeholder": "Išsamus ad<PERSON> 2"}, "bill": {"sameAsCompany": "Įmonės atsiskaitymo adresas toks pat kaip įmonės pristatymo adresas"}, "billing": {"address": {"placeholder": "<PERSON><PERSON><PERSON><PERSON>"}, "address2": {"placeholder": "Išsamus ad<PERSON> 2"}, "city": {"placeholder": "Miestas"}, "country": {"placeholder": "Šalis/Regionas"}, "district": {"placeholder": "<PERSON><PERSON><PERSON>"}, "phone": {"placeholder": "Mobiliojo telefono numeris"}, "province": {"placeholder": "Provincija"}, "zip": {"placeholder": "<PERSON><PERSON><PERSON>"}}, "billingSameAsShipping": {"placeholder": "Įmonės sąskaitos išrašymo adresas sutampa su pristatymo adresu"}, "billing_address": {"title": "Įmonės atsiskaitymo adresas"}, "city": {"placeholder": "Miestas"}, "company_name": {"placeholder": "Įmonės pavadinimas"}, "country": {"placeholder": "Šalis/Regionas"}, "create": {"limit": "Parduotuvės įmonių skaičius pasiek<PERSON>. Dėl pagalbos susisiekite su pardavėju.", "success": "Ačiū. Forma pateikta."}, "district": {"placeholder": "<PERSON><PERSON><PERSON>"}, "email": {"placeholder": "<PERSON><PERSON><PERSON>"}, "field": {"companyName": "Įmonės pavadinimas", "email": "El. <PERSON>"}, "first_name": {"placeholder": "<PERSON><PERSON><PERSON><PERSON>"}, "form": {"submit": "Pat<PERSON><PERSON><PERSON>"}, "last_name": {"placeholder": "Vardai"}, "phone": {"placeholder": "Mobiliojo telefono numeris"}, "province": {"placeholder": "Provincija"}, "register": {"button": "Pat<PERSON><PERSON><PERSON>", "success": {"description": "Forma sėkmingai patei<PERSON>.", "title": "<PERSON><PERSON><PERSON><PERSON>, kad ją už<PERSON>!"}, "title": "Bendrovės sąskaitos paraiška"}, "shipping_address": {"title": "Įmonės pristatymo adresas"}, "zip": {"placeholder": "<PERSON><PERSON><PERSON>"}}, "email": {"mail_common": "<PERSON><PERSON><PERSON>"}, "forget_password": {"forget_password_tips": "Pamiršote slaptažodį", "reset_password": "ATKURTI SLAPTAŽODĮ", "tips_password_can_be_changed_after_verification": "G<PERSON><PERSON> keisti slaptažodį po patikrinimo", "tips_reset_password": "Atkuriate paskyros {{account}} slaptažodį", "token_expired": "Jūsų <PERSON>žodžio atkūrimo nuoroda nebegalioja"}, "general": {"account": "PASKYRA", "and_button": "ir", "apr": "bal.", "april": "balan<PERSON>", "aug": "rugp.", "august": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "back_to__user__center": "Grįžti į vartotojo centrą", "bind__change_email": "Prijungti / keisti el. paštą", "bind__change_phone": "Prijungti / keisti mobiliojo telefono numerį", "bind_success": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> (-usi)", "cancel": "ATŠAUKTI", "cancel_common": "<PERSON><PERSON><PERSON><PERSON>", "cancel_mobile_button": "<PERSON><PERSON><PERSON><PERSON>", "clear_button": "Išvalyti", "company_empty_hint": "Įmonės pavadinimo laukas negali būti t<PERSON>", "company_existed": "Įmonės pavadinimas jau egzistuoja", "company_frequent_operation": "Pakartokite veiksmą, bandykite dar kartą vėliau", "company_registry_closed": "Prekybininkas neįjungė įmonės sąska<PERSON>s taikymo funkci<PERSON>, susisiekite su prekybininku, kad pateiktumėte paraišką.", "confirm": "OK", "confirm_button": "OK", "date_format": "yyyy-MM-dd", "dec": "gruod.", "december": "gru<PERSON><PERSON>", "edit": "Red<PERSON><PERSON><PERSON>", "email": "<PERSON>. p<PERSON><PERSON><PERSON> ad<PERSON>", "email_empty_hint": "Prašome įvesti el. pašto adres<PERSON>", "email_error_hint": "Prašome įvesti el. pašto adresą galiojančiu formatu ir mažiau nei 50 simbolių", "enter_verification_code": "Prašome įvesti pat<PERSON><PERSON><PERSON> kodą", "feb": "vas.", "february": "vasaris", "first_day": "1", "fr": "Pn", "fri": "pn", "friday": "penktadienis", "image_size_less_than_tips": "Vaizdų turi būti ma<PERSON> nei {{max}} MB", "jan": "saus.", "january": "sausis", "jul": "liep.", "july": "liepa", "jun": "birž.", "june": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "mar": "kov.", "march": "kovas", "max_image_upload_tips": "Galite įkelti maksimalų {{max}} v<PERSON>zd<PERSON> skaičių", "may": "gegužė", "may_short": "geg.", "mo": "Pr", "mon": "pr", "monday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "network_error_message": "<PERSON><PERSON><PERSON>", "nov": "lapkr.", "november": "<PERSON><PERSON><PERSON><PERSON>", "oct": "spal.", "october": "spalis", "ok": "OK", "okay_common": "OK", "orders": "Užsakymai", "password": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "password_empty_hint": "Prašome įvesti slaptažodį", "phone": "Mobiliojo telefono numeris", "phone_empty_message": "Prašome įvesti mobiliojo telefono numerį", "phone_error_message": "Prašome įvesti mobiliojo telefono numerį galiojančiu formatu", "phone_number_error_message": "Prašome įvesti galiojantį telefono numerį", "register": "<PERSON><PERSON><PERSON><PERSON>", "repeat_passport_error": "Jūsų įvestieji slaptažodžiai neatitinka", "repeat_password": "<PERSON><PERSON> na<PERSON> įvesti slaptažodį", "resend": "Siųsti iš naujo", "sa": "Št", "sat": "št", "saturday": "šeštadienis", "save": "IŠSAUGOTI", "select__date": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "select_date": "<PERSON><PERSON><PERSON><PERSON> da<PERSON>", "send": "Si<PERSON>sti", "send_error": "Prašome pirma išsiųsti patvirtinimo kodą", "send_verification_code_hint": "Prašome įvesti slaptažodį dar kartą", "sep": "rugs.", "september": "rugsė<PERSON>s", "set_password": "Slaptažodis turi būti sudarytas iš 6-18 simbolių, sudarytų iš skai<PERSON>, didž<PERSON>ųj<PERSON> ir mažųj<PERSON> raidžių", "sign__out": "<PERSON>si<PERSON><PERSON><PERSON>", "sign_in": "PRISIJUNGTI", "sign_in_activate": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> buvo išsiųstas į jūsų paskyrą {{account}}. <PERSON><PERSON><PERSON><PERSON> paskyr<PERSON>, galite ją a<PERSON>y<PERSON>oti", "sign_in_activate_title": "Paskyros aktyvinimas", "sign_up": "REGISTRUOTIS", "su": "Sk", "subscription_desc": "Gaukite paskutines naujienas apie parduotuvės ve<PERSON>", "sun": "sk", "sunday": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "th": "Kt", "thu": "kt", "thursday": "ketvirtadienis", "time_format": "HH:mm", "today": "Šiandien", "tu": "An", "tue": "an", "tuesday": "<PERSON><PERSON><PERSON><PERSON>", "update_passport": "<PERSON><PERSON><PERSON> slaptažodį", "user_agreement_tip": "Prašome pirma perskaityti ir sutikti su vartotojo sutartimi", "username": "El. pa<PERSON><PERSON> ad<PERSON> / mobiliojo telefono numeris", "username_empty_hint": "Prašome įvesti el. pašto adresą ar mobilųjį telefono numerį", "verification_code": "<PERSON><PERSON><PERSON><PERSON> koda<PERSON>", "verification_code_sent_tip": "Patikrinimo kodas buvo išsiųstas į jūsų paskyrą", "verift_account_tip": "Prašome patvirtinti savo paskyrą ir atkurti slaptažodį", "we": "Tr", "wed": "tr", "wednesday": "trečiadienis", "bind_email": "Susieti el. pa<PERSON>ą", "bind_phone": "Susieti mobilųjį telefoną", "continue_as_a_guest": "Tęskite kaip s<PERSON>", "email_tip": "Išsiųsime jums <PERSON>, kad atkurtumėte savo slaptažodį", "login_method": "arba prisijungti <PERSON> bū<PERSON>s", "new_email": "Naujas el. paštas", "new_phone": "Naujas telefonas", "phone_tip": "Išsi<PERSON>si<PERSON> jums <PERSON>, kad atkurtumėte savo slaptažodį", "proceed_to_checkout": "Pereikite prie apmokėjimo", "title": "Puslapio pavadinimas", "verification_code_success": "<PERSON><PERSON><PERSON><PERSON> k<PERSON> buvo <PERSON> jū<PERSON> {{type}} <em>{{value}}</em> prisijungimui, prašome įvesti jį toliau."}, "login": {"member_login_common": "<PERSON><PERSON>", "subscribe_confirm_tip": "Dėkojame už jūsų prenumeratą. Galite prisijungti geresnei patirčiai."}, "message": {"format_error": "Nepalaikomas pranešimo formatas", "input_placeholder": "Ar norėtumėte palikti pranešimą?", "message": "Pranešimas", "send": "Si<PERSON>sti"}, "phone": {"mobile_common": "Telefono Nr."}, "register": {"privacy_policy": "Privatumo politika", "terms_of_service": "pas<PERSON><PERSON> te<PERSON> s<PERSON>", "go_to_login": "<PERSON><PERSON><PERSON> registruotis", "tips_agree_with_the_shop": "<PERSON><PERSON><PERSON> su"}, "subscribe": {"email_has_bind": "Šis el.paštas jau priklauso kitiems paskyros", "phone_has_bind": "Šis telefonas jau p<PERSON>lauso kitiems paskyros"}, "subscription": {"email": "El. <PERSON>", "email_subscribe_error_tip": "Šis pašto adresas buvo susietas su kita paskyra", "email_subscription_text": "Gaukite prenumeratos rėmimo duomenis el. Paštu", "line": "„LINE“", "messenger": "<PERSON>", "phone": "Mobilusis telefonas", "phone_subscribe_error_tip": "Šis mobilusis telefonas buvo susietas su kita paskyra", "phone_subscription_text": "Prenumeruoti telefono skatinimą", "reason_other_length": "Įveskite iki 300 simbolių", "reason_other_placeholder": "Įveskite turinį", "save_button": "IŠSAUGOTI", "subscribe_button": "PRENUMERUOTI", "subscription_cancel_text": "Neprenumeruoti", "subscription_hint": "Prenumeruota", "subscription_title": "Ptenumerata", "unsubscribe": "Neprenumeruoti", "unsubscribe_btn": "Neprenumeruoti", "unsubscribe_confirm_text": "Neprenumeruoti", "unsubscribe_feedback_text": "Daugiau iš mūsų rinkodaros naujienų negausite. Galite bet kada vėl užsiprenumeruoti", "unsubscribe_feedback_title": "Atsisakėte <italic>{{name}}</italic> ad<PERSON>ų sąrašo prenumeratos", "unsubscribe_reason1": "Nebenoriu gauti to<PERSON>ų el. pašto <PERSON>", "unsubscribe_reason2": "Gavau per daug el. pa<PERSON><PERSON>", "unsubscribe_reason3": "Atsiskaiau šių el. pašto laiškų prenumeratos", "unsubscribe_reason4": "<PERSON><PERSON> p<PERSON><PERSON><PERSON> man neak<PERSON>s", "unsubscribe_reason5": "El. pa<PERSON><PERSON> la<PERSON> turinys per ilgas", "unsubscribe_reason6": "<PERSON><PERSON>", "unsubscribe_subtitle": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad pasitraukė<PERSON>! Prašome nurodyti, kod<PERSON><PERSON> atsisakėte prenumeratos:", "unsubscribe_success": "Prenumerata atšaukta ir greitai būsite nukreipta(s) į pradžios puslapį", "unsubscribe_tip": "Užsiprenumeravę daugiau nebegausite paskutinio skatinimo.", "unsubscribe_title": "Neprenumeruoti"}, "unsubscribe": {"content_placeholder": "Įveskite turinį", "goto_btn": "Įeiti į parduotuvę", "notyet": "<PERSON><PERSON><PERSON> dar neesate prenumeravę", "notyet_desc": "Galite prenumeruoti eiti į parduotuvę", "success": "Prenumerata atšaukta", "success_tip": "<PERSON><PERSON><PERSON> prenumeratą iš {{storeName}}"}}, "general": {"404": {"subtext": "<PERSON><PERSON><PERSON><PERSON>, k<PERSON>o <PERSON>, ne<PERSON><PERSON><PERSON><PERSON>.", "title": "404 puslapis nerastas"}, "abandon": {"Order": {"risk": "Per daug užklausų"}}, "address": {"address": "<PERSON><PERSON><PERSON>"}, "contact_us": {"contact_us": "SUSISIEKITE SU MUMIS", "send_success": "<PERSON><PERSON><PERSON><PERSON>, kad k<PERSON><PERSON> į mus ir atsakysime jums kuo skubiau.", "success_feedback": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kad susis<PERSON>te su mumis. Mes atsakysime kuo gre<PERSON>č<PERSON>u."}, "footer": {"copyright": "© {{year}} {{{storeName}}}", "subscribe_format_error": "El. pašto adreso formato klaida", "subscribe_success": "Prenumeruoti sėkmę"}, "general": {"accept": "<PERSON><PERSON><PERSON><PERSON>", "decline": "Atsisakyti", "home": "<PERSON><PERSON><PERSON><PERSON> lapas", "link": "Privatumo politika", "more": "Daugiau", "no_data": "Nėra prieinamų duomenų", "pack_up": "<PERSON><PERSON><PERSON><PERSON>", "privacy_policy": "Privatumo politika", "tel_phone": "Telefono Nr.", "tips": "Kad <PERSON> jūsų patirtį,mes naudo<PERSON> slapukus, kad prisimintumėm jūsų prisijungimo duomenis,rinkti statistiką ir pateikti turinį pritaikytą jūsų pomėgiams. Paspasukite “Priimti” kad priimtumėte slapukus arba peržiūrėkite mūsų “Privatumo Politika”.", "title": "<PERSON><PERSON><PERSON> jū<PERSON> privatumu", "username": "<PERSON><PERSON><PERSON> ir pava<PERSON>", "email_tip": "Netinkama paš<PERSON>. Prašome įvesti iš naujo", "proofing_tip": "ATSIPRAŠOME, JŪSŲ IEŠKOMA PARDUOTUVĖ DABAR UŽDARYTA,<br/> PRAŠOME BANDYTI VĖLIAU."}, "header": {"cart": "<PERSON><PERSON><PERSON><PERSON>ž<PERSON>", "center": "Centras", "close": "Uždaryta", "login": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "no_currency_fallback": "Nerasta reikiama valiuta", "search_input_placeholder": "Įveskite čia ir bakstelėkite paiešką"}, "order_tracking": {"code": "<PERSON><PERSON><PERSON><PERSON>", "email": "El. <PERSON>", "form_error": "Užklausa nepavyko", "orderId": "Užsakymo ID", "orderId_error": "Užsakymo <PERSON> laukas negali būti t<PERSON>", "other_email": "<PERSON>. p<PERSON><PERSON><PERSON> ad<PERSON>", "other_phone": "Mobiliojo telefono numeris", "phone": "Mobilusis telefonas", "query_illegal": "tvarka <PERSON>", "risk_black_user": "Šiai paskyrai gresia pavojus", "risk_interception": "tvarka <PERSON>", "submit_btn": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> užsakymą", "username": "Pašto / telefono numeris"}, "password": {"back": "Atgal", "entry_store": "Įveskite parduotuvę", "forget_password": "Slaptažodžio prieiga", "goto_admin": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> dabar", "i_am_admin": "<PERSON><PERSON> (-<PERSON>)", "input_password": "Įveskite slaptažodį", "password_is_error": "<PERSON>eising<PERSON>, prašome bandyti dar kartą", "log_in_here": "Prisijungt<PERSON> č<PERSON>", "use_password_enter": "Naudokite slaptažodį, kad įeitumėte į parduotuvę", "you_are_admin": "Ar estate savininkas (-ė)?"}, "policy": {"service": "pas<PERSON><PERSON> te<PERSON> s<PERSON>", "shipping": "Siuntimo politika"}, "activate_account_discount": "Aktyvuokite paskyrą ir gaukite nuolaidos kodą", "handle_success": "Sėkminga operacija", "search": {"search": "Įveskite čia ir bakstelėkite paiešką", "search_hint": "<PERSON><PERSON><PERSON><PERSON> “{{key}}”"}}, "order": {"buy_again": "Pirk<PERSON> dar kartą", "checkout_order": {"pay_amount": "mokėti sumą", "receiver_account": "gav<PERSON><PERSON> s<PERSON>", "reference_number": "šaltinio numeris", "upload_time": "įkėlimo laikas", "upload_voucher": "Įkelti kuponą", "voucher_amount_limit": "Galite įkelti tik iki 5 kuponų."}, "detail": {"dutyFee": "<PERSON><PERSON><PERSON><PERSON>", "orderstatus": {"detail": "Detalė"}}, "order_details": {"details": "Užsakymo duomenys", "insuranceService": "<PERSON><PERSON><PERSON>", "insuranceService_applyClaim": "Krieptis d<PERSON>l pretenzij<PERSON>", "insuranceService_content_line1__email": "1. {{serviceName}} išsiuntė polisą jūsų el. paštu \"{{email}}\". Jei negavote poliso duomenų, prašome susisiekti su parduotuvės klientų aptarnavimo tarnyba. ", "insuranceService_content_line1__phone": "1. Politika įsigyta per {{serviceName}}. Jei turite kitų klausimų, susisiekite su parduotuvės klientų aptarnavimo skyriumi.", "insuranceService_content_line2": "2. <PERSON><PERSON> buvo patirta(s) <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, v<PERSON><PERSON><PERSON>s ar susidar<PERSON> kita situacija, galite pateikti pretenziją.  ", "order": "Užsakymai", "remark": "<PERSON><PERSON><PERSON><PERSON><PERSON> pastaba", "time": "Užsakyta:", "adjust": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "products": {"price": "<PERSON><PERSON>", "quantity": "<PERSON><PERSON><PERSON>"}, "tax_included": "Įskaitant {{price}} m<PERSON><PERSON><PERSON>ius"}, "order_list": {"no_more_info": "Nėra daugiau informacijos", "no_record": "<PERSON><PERSON>o metu nėra jokio užsaky<PERSON> įrašo, e<PERSON><PERSON> a<PERSON><PERSON>", "package_amount": "<PERSON><PERSON><PERSON>ė {{num}}({{item}} prekės)", "total_amount": "{{transPackages}} preki<PERSON> iš viso", "item_amount": "prekės ({{item}})"}, "order_status": {"canceled": "<PERSON><PERSON><PERSON><PERSON>", "click_to_track": "Prašome sekti savo pakuotę spustelėdami toliau pateiktą nuorodą:", "contact_us": "<PERSON><PERSON><PERSON> su mumis", "help_tips": "Reikia pagalbos? Prašome susisiekti su mumis: {{email}}", "payment_voucher_transfer_success": "Mokėjimas sėkmingai pervestas", "payment_voucher_waiting_for_payment": "<PERSON><PERSON><PERSON>", "sequence": "Nr.{{id}}", "tracking": "<PERSON><PERSON><PERSON>"}, "payment": {"authorized": "Įgaliotas", "billing_address": "Sąskaitos teikimo adresas", "detail": "Detalė", "method": "<PERSON><PERSON><PERSON><PERSON><PERSON> b<PERSON>", "paid": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partially_paid": "<PERSON><PERSON><PERSON>", "partially_refund": "<PERSON><PERSON><PERSON>", "paying": "Mokė<PERSON><PERSON>", "payment": "Mokė<PERSON><PERSON>", "payment_status": "Mok<PERSON><PERSON><PERSON> b<PERSON>", "refund": "Grąžinta", "unpaid": "<PERSON><PERSON>umok<PERSON><PERSON>"}, "shipping": {"info": "Informacija", "no_info": "Dar nėra pristatymo informacijos, praš<PERSON> tikrinti vėliau.", "package_tracking": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "partially_shipped": "<PERSON><PERSON><PERSON>", "preparing_order": "Prekių ruošimas", "processing_amount": "<PERSON><PERSON><PERSON><PERSON><PERSON> ({{num}} pre<PERSON><PERSON><PERSON>)", "shipped_order": "Prist<PERSON><PERSON><PERSON>"}, "fulfillment": {"createTime": "<PERSON><PERSON><PERSON><PERSON><PERSON> la<PERSON>:{{time}}"}}, "products": {"collection": {"collection": "Kategorija"}, "facets": {"in_stock_only": "<PERSON><PERSON>", "clear": "<PERSON><PERSON><PERSON>", "clear_all_filter": "Ištrinkite viską", "confirm": "<PERSON><PERSON><PERSON><PERSON>", "filter_and_sort": "Ekranas ir klasifikacija", "filter_button": "<PERSON><PERSON><PERSON>", "less_than": "ma<PERSON><PERSON><PERSON> nei", "max_price": "Aukščiausia kaina yra {{ price }}", "more_than": "daugiau nei", "price": {"label": "<PERSON><PERSON>"}, "product_count": "{{product_count}} produkt<PERSON> dalis", "reset": "Atstaty<PERSON>", "sort_button": "Klasifikacija", "stock": {"label": "<PERSON><PERSON><PERSON>", "value": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}}, "general": {"load_more_btn": "Įkeliama daugiau elementų", "loading_btn": "Įkelti daugiau", "no_product_data_found": "Duomenų apie prekę nerasta Prašome bandyti dar kartą vėliau", "sold_out": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "load_more": "Įkeliama daugiau elementų", "load_more_items": "{{total}} preki<PERSON> krova {{current_offset}} ~ {{last_current_offset}}", "unavailable": "<PERSON><PERSON><PERSON><PERSON>", "variant_property_color": "Spalva"}, "giftcard": {"seoTitle": "<PERSON><PERSON><PERSON><PERSON> {{ shop }} do<PERSON><PERSON> kortel<PERSON>s likutis yra {{ value }}!", "balance": "<PERSON><PERSON><PERSON>", "buy_giftcard": "Įsigyti do<PERSON><PERSON> kortel<PERSON>", "description": "Štai j<PERSON>ų {{price}} do<PERSON><PERSON> kortelė {{shop}}!", "expired_date": "Gali<PERSON><PERSON><PERSON> terminas", "invalid_tip": "<PERSON><PERSON><PERSON><PERSON> do<PERSON> kort<PERSON>, prašome įsigyti kitą", "permanent": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "receive_at": "Nebegalioja nuo", "receive_tip": "Prašome priimti savo dovanų kortelė", "title": "Jūsų dovan<PERSON> kortelė", "use_giftcard": "Naudoti dovanų kortelė", "use_giftcard_tip": "Atskaitymui galite įvesti dovanų kortelė<PERSON> numerį atsiskaitant"}, "product_details": {"activity_toast_price_limit": "<PERSON><PERSON><PERSON><PERSON> kiek<PERSON> le<PERSON>i ribotai prekei - {{num}}", "activity_toast_product__limit": "Įsigijimas apribotas iki {{stock}} remiamų prekių", "activity_toast_title__limit": "Toliau nurodytos prekės viršija įsigijimo apribojimą", "amount": "<PERSON><PERSON><PERSON>", "buy_more_save_more": "Pirkite daugiau Sutaupykite!", "cancel": "<PERSON><PERSON><PERSON><PERSON>", "country_region": "Šalis / regionas", "default_placeholder": "Prašome pasirinkti {{attrName}}", "description": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "each_price": "{{price}}/kiekviena", "email": "El. <PERSON>", "enter_email_address": "Prašome įvesti savo el. pašto adres<PERSON>", "enter_valid_email_address": "Prašome įvesti galiojantį el. pašto adres<PERSON>", "give_us_more_of_your_information": "<PERSON><PERSON><PERSON> suteikti mums daugiau informacijos, kad gal<PERSON>te suteikti jums daugiau tinkamų paslaugų", "go_to_amazon_by_link": "Pirkite Amazon", "go_to_rakuten_by_link": "Pirkite LOTTE", "go_to_yahoo_by_link": "Pirkite Yahoo", "in_stock": "Atsargose", "inventory_in_stock": "<PERSON><PERSON><PERSON>", "inventory_in_stock_show_count": "Atsargos: Liko {{ quantity }}", "inventory_low_stock": "Žemas atsargų lygis", "inventory_low_stock_show_count": "Žemas atsargų lygis: Liko {{ quantity }}", "inventory_out_of_stock": "<PERSON><PERSON><PERSON><PERSON>", "inventory_out_of_stock_continue_selling": "<PERSON><PERSON><PERSON>", "inventory_quantity": "Atsargų duoimenys", "leave_us_message": "Prašome įvesti savo pranešimą", "leave_us_message_get_back": "Prašome palikti mums p<PERSON> ir kuo skubiau pateiksime atsakymą", "link_preview_does_not_support": "<PERSON><PERSON><PERSON><PERSON>žiūros operacija nepalaikoma", "load_more": "<PERSON><PERSON><PERSON>", "maximum_length_of_message": "<PERSON><PERSON><PERSON><PERSON> jū<PERSON> praneš<PERSON>ų ilgis turi būti ne daugiau nei 1500 žodžių", "message": "Jūsų praneš<PERSON>s", "moq_increment": "{{num}} daugybė", "moq_increment_tips": "Papildomas pirkinių kiekis palaiko tik didelį {{num}} skaičių", "moq_max_tips": "Ši spefifikacija turi būti įsigyta iki {{num}} vnt.", "moq_maximum": "Daugiausia prekių {{num}}", "moq_min_tips": "<PERSON><PERSON> specifikacija turi būti įsigyta {{num}} ar daug<PERSON>u", "moq_minimum": "Mažiausiai prekių {{num}}", "more_payment_options": "daugiau mokėjimo <PERSON>inkč<PERSON>ų", "name": "<PERSON><PERSON><PERSON> ir pava<PERSON>", "optional": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "phone_number": "Telefono Nr.", "price": "<PERSON><PERSON>", "price_break_title": "<PERSON><PERSON><PERSON><PERSON>", "price_breaks_view_less": "Paslėpti", "price_breaks_view_more": "<PERSON><PERSON><PERSON>", "product_enquiry": "<PERSON>k<PERSON><PERSON>", "submission_failed": "<PERSON>epav<PERSON><PERSON>, prašome bandyti dar kartą", "submission_successfully": "Pateikt<PERSON>", "submit": "Pat<PERSON><PERSON><PERSON>", "table_each_price": "{{price}}kiekviena", "table_each_price_ssr": "{{{priceDom}}}kiekviena", "tax_included": "mokesčiai įskaičiuoti", "view_product_detail": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> duomen<PERSON>", "copy_button_text": "<PERSON><PERSON><PERSON>", "copy_success_text": "Nuoroda nukopijuota į mainų sritį", "include_taxes": "<PERSON><PERSON><PERSON> įtrauktas", "share_button_text": "<PERSON><PERSON><PERSON>", "shipping_policy_html": "Aps<PERSON>čiuokite <a href=\"{{ link }}\">siuntimo mokestį</a> atsiskaitydami", "view_details": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> išsamią informaciją"}, "product_list": {"add_to_cart": "PRIDĖTI PRIE VEŽIMĖLIO", "filter": "Filtras", "filters_selected": "buvo pasirinkti {{count}}", "from_price_html": "Nuo {{price}}", "infinite_btn_desc": "{{total}} prekių {{last_current_offset}}", "less": "ma<PERSON><PERSON>u <PERSON>", "load_more_desc": "{{total}} prekių {{current_offset}} ~ {{last_current_offset}}", "load_more_tip": "Įkeliama daugiau prekių", "more": "daugiau žymų", "new_arrivals": "<PERSON><PERSON><PERSON>", "new_products": "<PERSON>uo naujausio iki seniausio sukūrimo laiko", "no_product": "Šioje kategorijoje prekių nėra", "price_break_tag": "<PERSON><PERSON><PERSON><PERSON> ka<PERSON>a p<PERSON>", "price_high_to_low": "Kaina nuo aukščiausios iki maž<PERSON>usios", "price_low_to_high": "Kaina nuo mažia<PERSON> iki <PERSON>", "product_has_been_removed": "<PERSON><PERSON><PERSON> p<PERSON> i<PERSON>", "product_sold_out": "IŠPARDUOTA", "products": "<PERSON><PERSON><PERSON><PERSON>", "quick_shop": "Greitas apsipirkimas", "recommend": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "sale": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "save_byjs": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{priceDom}}", "save_ratio": "{{price}}% nuolaida", "select_product_all_options": "Pasirinkta produkto varianta yra nepilna arba nepasiekiama. Prašome pasirinkti dar kartą", "sort_button": "Klasifikacija", "top_sellers": "Populiariausi pardavėjai"}, "product_search": {"back_to_home": "Atgal į pradžios puslapį", "ount_results": "{{count}} Rezultatai", "results_found_for": "{{count}} rezultatai rasti \"{{keyword}}\"", "search": "Pa<PERSON>š<PERSON>", "search_no_results": "<PERSON><PERSON><PERSON><PERSON> \"{{keyword}}\" paieška neteikia jokių rezultatų"}, "recently_viewed_products": {"recently_viewed_products": "Naujausiai <PERSON><PERSON>"}}, "reduction_code": {"giftcard": "Dovan<PERSON> kortelės"}, "sales": {"discount_coupon": {"active_date_until": "<PERSON><PERSON><PERSON> iki {{endTime}}", "applied": "Tai<PERSON><PERSON>", "applied_successfully": "Nuolaidos koda<PERSON> sėkmingai pritaikytas", "apply": "<PERSON><PERSON><PERSON>", "buy_x_get_y_all_discount_content": "Gaukite {{benefitCount}} vnt. su {{discount}} nuolaida", "buy_x_get_y_all_free_content": "Gaukite {{benefitCount}} vnt. nemokamai", "buy_x_get_y_discount_content": "{{discount}} nuolaida", "buy_x_get_y_free_content": "NEMOKAMA", "buy_x_get_y_threshold": "Įsigykite {{threshold}} ir gaukite {{benefitCount}} vnt. ne<PERSON><PERSON><PERSON>i", "coupon_content_level": "Skirta {{level}}", "coupon_discount_content": "{discount} nuolaida", "coupon_not_found": "<PERSON>uo<PERSON><PERSON> k<PERSON>", "failed_to_apply_coupon_code": "Pateikti nepavyko", "free__shipping": "<PERSON>emoka<PERSON> pristat<PERSON>", "get_code": "Gaukite nuolaidų k<PERSON>", "popup_label": "Parduo<PERSON><PERSON><PERSON><PERSON>:", "shipping_rate_below": "<PERSON><PERSON><PERSON><PERSON> yra ma<PERSON> negu {value}", "specified_customer": "Dėl tam tikrų klientų", "threshold": "Pirkite {threshold}"}, "flash": {"day": "diena", "days": "dienos"}, "general": {"activity_banner_benefit": "{{benefit}} nuolaida", "activity_banner_copy_success_text": "<PERSON><PERSON><PERSON> k<PERSON> sėkmingai nukopijuotas", "activity_banner_free_shipping": "<PERSON><PERSON><PERSON><PERSON> siunt<PERSON>", "activity_selector_new_arrivals": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "activity_selector_price_high_to_low": "<PERSON><PERSON> nuo did<PERSON> iki <PERSON>", "activity_selector_price_low_to_high": "<PERSON>na nuo ma<PERSON> i<PERSON>", "activity_selector_recommended": "Rekomenduoti", "apply_common": "<PERSON><PERSON><PERSON>", "coupon_code_copied": "<PERSON><PERSON><PERSON> k<PERSON> sėkmingai nukopijuotas", "coupon_code_copy": "<PERSON><PERSON><PERSON>", "flash_sale_text": "MOMENTINIS PARDAVIMAS", "flash_sale_tip": "Pirkinio riba: {{count}} prekių", "hot_sale": "<PERSON><PERSON><PERSON><PERSON><PERSON>!", "product_tabs_benefit_amount_locked": "Gaukite {{benefitAmount}} nuolaidą", "product_tabs_benefit_amount_unlocked": "{{benefitAmount}} nuolaida atrak<PERSON>a!", "product_tabs_benefit_discount_locked": "Gaukite {{offPercent}}% nuolaidą", "product_tabs_benefit_discount_unlocked": "{{offPercent}}% nuolaida atrakinta!", "product_tabs_benefit_free_locked": "Nemokama prekė", "product_tabs_benefit_free_unlocked": "Nemokama prekė atrak<PERSON>a!", "product_tabs_benefit_unlocked_tip": "Dabar prekei galite m<PERSON>gautis nuo<PERSON>a", "product_tabs_code_benefit_amount_unlocked": "Gaukite {{benefitCount}} prekę (-es) su {{benefitAmount}} nuolaida", "product_tabs_code_benefit_discount_unlocked": "Įsigykite {{benefitCount}} vnt. su {{offPercent}}% nuolaida", "product_tabs_code_benefit_free_unlocked": "Gaukite {{benefitCount}} vnt. nemokamai", "product_tabs_threshold": "Pirkite {{threshold}}", "purchase_limit": "<PERSON><PERSON><PERSON> iš viso apribotas {{count}} rekla<PERSON><PERSON><PERSON><PERSON> (-ių) prekės (-ių)", "purchase_limit_each_item": "<PERSON><PERSON>ys apribotas {{count}} kiekvieno varianto preke (-ėmis)", "purchase_limit_each_product": "Pirkinys apribotas {{count}} kiekvieno reklaminio produkto preke (-ėmis)", "select_promotional_product": "{{selectCount}} pasirinkt<PERSON> <PERSON><PERSON>", "sold": "{{count}} parduota"}, "gift": {"got_all_gifts_tip": "Sveikiname! <PERSON><PERSON><PERSON> {{saved}} NEMOKAMĄ DOVANĄ!", "not_meet_amount_tip": "Įsigykite {{threshold}} <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>s {{willSave}} NEMOKAMA DOVANA", "not_meet_quantity_tip": "Įsigykite {{threshold}} daug<PERSON><PERSON>, mėgaukitės {{willSave}} NEMOKAMA DOVANA", "select": "<PERSON><PERSON><PERSON><PERSON>", "select_gift_tip": "Sveikiname! Galite m<PERSON> iki {{willSave}} NEMOKAMA DOVANA", "spend_more_money_get_more_tip": "Sveikiname! Jau gavau {{saved}} nemokamą dovaną! Išleiskite {{threshold}} daugiau, mėgaukitės iki {{willSave}} NEMOKAMA DOVANA", "spend_more_quantity_get_more_tip": "Sveikiname! Jau gavau {{saved}} nemokamą dovaną! Įsigykite {{threshold}} daugia<PERSON>, m<PERSON>gaukit<PERSON><PERSON> iki {{willSave}} NEMOKAMA DOVANA"}, "promotion": {"discount_tag": {"label": "Nuolaida:"}}}, "trade": {"order_summary": {"rounding": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "payments-name": {"COD": "Apmokėti gryn<PERSON> p<PERSON>", "MultiplePayment": "Kelių kanalų mokėjimai", "Paypal": "<PERSON><PERSON>", "adjustPay": "manual", "mpay": {"alipay": "<PERSON><PERSON><PERSON> (For Real-name user of the Mainland China.)", "mpay": "MPay (For MPay Users)"}, "third-part": "<PERSON><PERSON><PERSON><PERSON><PERSON> kort<PERSON>"}, "reduction_code": {"giftcard": "Dovan<PERSON> kortelės"}}, "transaction": {"contact": {"discounts_stack_not": "Nuolaida taikoma šiam užsakymui. Negalite pridėti kitos nuolaidos"}, "couponCode": {"freeShipping": {"existCode": "Dublikuoti nemokamo siuntimo nuo<PERSON> kodus"}}, "discount": {"code": {"country_not_support": "Mokėjimas Nemokamo siuntimo k<PERSON>o koda<PERSON> - <PERSON><PERSON><PERSON>", "logistics-fee-over-limit": "Nemokamo siuntimo kupono kodas - siuntimo išlaidos virš<PERSON>"}, "code_error": "<PERSON>uolaidos kodas įvestas neteisingai", "code_item_unmatched": "{{discountCode}} įvestas te<PERSON><PERSON>, bet prekių kaina ar kiekis neatitinka reikalavimų", "code_overdue": "<PERSON><PERSON><PERSON> k<PERSON>gal<PERSON>", "code_user_exceedLimit": "Vartotojas naudoja veiklą daugiau kartų nei riba", "code_user_notSupport": "Vartotojas negali naudoti ve<PERSON>", "coupon_code": "<PERSON><PERSON><PERSON> kodas", "coupon_invalid_for_the_shipping_method": "{{discountCode}} k<PERSON><PERSON> galioja siuntimo bū<PERSON>i", "discount_codes_cannot_stack": "<PERSON>au yra pasirinktas renginys produktui, kurio negalima naudoti su kitais pasirenkamais reng<PERSON>.", "use_coupon_alone": "<PERSON><PERSON> k<PERSON>o kodas negali būti naudojamas kartu su kitų akcijų ar nuolaidų pasiūlymais"}, "general": {"discount_off": "<PERSON><PERSON><PERSON><PERSON>", "free_charge": "<PERSON>emoka<PERSON> pristat<PERSON>", "free_shipping": "<PERSON>emoka<PERSON> pristat<PERSON>", "order_seller_discount": "Pardavėjo(s) papildoma nuolaida", "order_tax": "Mokesčiai", "order_tip": "<PERSON><PERSON><PERSON>", "payment_subtotal_cost": "Ta<PERSON><PERSON><PERSON> suma", "point_discount": "Nuolaida už taškus", "shipping_fee": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "trade_coupon_discount": "<PERSON><PERSON><PERSON><PERSON>"}, "item": {"invalid_product": "Negaliojan<PERSON><PERSON>", "remove_invalid_product": "<PERSON><PERSON><PERSON>", "removed": "NEGALIOJANTIS", "sold_out": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "lack_multilingualism": "* Šio pareiškimo įkelti nepavyko. Susisiekite su klientų aptarnavimo tarnyba, kad paaiškintumėte situaciją*", "notices": {"insufficient_product_inventory": "Nepakankamas pre<PERSON> inventorius", "network": "<PERSON><PERSON><PERSON>", "product_expired": "Baigėsi prekės galioji<PERSON> terminas", "product_limit": "Viršytas <PERSON> apribojimas"}, "order": {"deleted": "NEGALIOJANTIS", "rebuy": "Pirk<PERSON> dar kartą"}, "payment": {"continue_shopping": "Tęsti apsip<PERSON>ą", "more": "Daugiau", "saved": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "total": "<PERSON><PERSON><PERSON> suma"}, "payment_disconut_channelinvalid_code": {"tips": "Šis n<PERSON>laid<PERSON> kodas {{discountCode}} negalioja pardavimo kanalui"}, "policy": {"refund_policy": "Grąžinimo politika"}, "refund": {"cost_points": "<PERSON><PERSON><PERSON><PERSON> {{value}} taškai", "deduct_point": "Galite naudoti {{deductMemberPointNum}} ta<PERSON><PERSON>, kad sutaupyt<PERSON>te {{deductMemberPointAmount}}"}}, "unvisiable": {"applepay": {"cancel": "<PERSON><PERSON><PERSON><PERSON>", "enquire": "Tęsti užsakymo teikimą su „Apple Pay“?", "paywith": "<PERSON><PERSON><PERSON><PERSON>"}, "customer": {"error_message_1": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kad gaučiau el. <PERSON>, kad gaučiau nau<PERSON>, prieigą prie išskirtinių pasiūlymų ir dar daugiau.", "error_message_1001": "Slap<PERSON>žod<PERSON><PERSON> klaid<PERSON>", "error_message_1002": "Vartotojas neegzistuoja", "error_message_1003": "Jūsų paskyra neprieinama dėl sutrikimo; prašome susisiekti su pardavėju, kad bū<PERSON><PERSON> atšauktas draudimas", "error_message_1004": "Naudokite dažnai. Prašome bandyti iš naujo vėliau", "error_message_1005": "Baigėsi kodo laikas", "error_message_1006": "<PERSON><PERSON>", "error_message_1007": "Dažnai naudokite patikros kodą. Prašome atgauti patikros kodą", "error_message_1008": "Prisijungti nekonfigūruojant trečiosios šalies kanalų", "error_message_1009": "Nepavyksta iškviesti trečiosios šalies sąsajos", "error_message_1010": "Nepavyksta išsiųsti", "error_message_1011": "Paskyra nesuaktyvinta Norėdami užpildyti slaptažodį ir kitą informaciją, užsiregistruokite.", "error_message_1012": "<PERSON><PERSON><PERSON><PERSON>", "error_message_1013": "<PERSON><PERSON><PERSON><PERSON>", "error_message_1014": "Nepavyksta patvir<PERSON> patik<PERSON> kodo", "error_message_1015": "Praš<PERSON> patvirtinti dinaminį patikros kodą", "error_message_1016": "<PERSON><PERSON><PERSON><PERSON>", "error_message_1017": "Paskyros numeriui kyla rizika", "error_message_1018": "<PERSON><PERSON><PERSON> buvo susieta", "error_message_1020": "Baigėsi prisijungi<PERSON> la<PERSON>s", "error_message_1021": "Baigėsi prisijungi<PERSON> la<PERSON>s", "error_message_1022": "Paskyros numeris buvo susietas", "error_message_1023": "<PERSON><PERSON><PERSON><PERSON>", "error_message_1024": "<PERSON><PERSON><PERSON><PERSON>", "error_message_1035": "Prašome atsiųsti patvirtinimo kod<PERSON>", "error_message_2": "<PERSON><PERSON><PERSON><PERSON>", "error_message_2001": "Mobilusis buvo registruotas", "error_message_2002": "El. paštas buvo registruotas", "error_message_2003": "Nebandykite ta<PERSON>", "error_message_2004": "Baigėsi kodo laikas", "error_message_2005": "<PERSON><PERSON>", "error_message_2006": "Per daug tikrinimo kartų, praš<PERSON> gauti kitą kodą", "error_message_2007": "<PERSON><PERSON><PERSON><PERSON>, bandykite dar kartą vėliau", "error_message_2014": "Sąskaitoms gresia pavojus", "error_message_2016": "Sąskaitoms gresia pavojus", "error_message_2020": "<PERSON><PERSON><PERSON>, bandykite vėliau", "error_message_3": "Viešnagė yra per ilga, praš<PERSON> atnaujinti puslapį.", "error_message_3001": "<PERSON><PERSON><PERSON><PERSON>, bandykite dar kartą vėliau", "error_message_3002": "<PERSON><PERSON><PERSON><PERSON><PERSON>, kurie nėra susieti mobiliaisiais telefonais / pa<PERSON><PERSON>, bando pakeisti savo <PERSON>ž<PERSON>", "error_message_3003": "Nebandykite ta<PERSON>", "error_message_3004": "Baigėsi kodo laikas", "error_message_3005": "<PERSON><PERSON>", "error_message_3006": "Per daug tikrinimo kartų, praš<PERSON> gauti kitą kodą", "error_message_3007": "<PERSON><PERSON><PERSON><PERSON>", "error_message_3008": "El. paštas neegzistuoja", "error_message_3009": "Mobilusis buvo susietas su kitomis paskyromis", "error_message_3010": "El. paštas buvo susietas su kitomis paskyromis", "error_message_3014": "Nepavyko pateikti SMS / pa<PERSON><PERSON>", "error_message_3015": "Paskyra buvo aktyvuota", "error_message_3021": "<PERSON><PERSON><PERSON>, bandykite vėliau", "error_message_3022": "<PERSON><PERSON>rod<PERSON> ne<PERSON>", "error_message_3023": "Nuoroda netinkamai suformuota", "error_message_3024": "Nuorodos parametro klaida", "error_message_3026": "Paskyros formato klaida", "error_message_3027": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> negali būti toks pats ka<PERSON> p<PERSON>。", "error_message_3029": "<PERSON><PERSON> kas ne<PERSON> mobiliojo telefono numerio aktyvavimas", "error_message_4": "<PERSON><PERSON><PERSON><PERSON>", "error_message_5": "<PERSON><PERSON><PERSON><PERSON>", "error_message_6": "<PERSON><PERSON><PERSON><PERSON>", "error_message_7": "Slaptažodis turi būti sudarytas iš 6-18 simbolių, sudarytų iš skai<PERSON>, didž<PERSON>ųj<PERSON> ir mažųj<PERSON> raidžių"}, "editorSwitchCountryTip": "Redagavimo priemonėje nepalaikomas šalių keitimas", "shopby": {"button": {"show": "Saugiau ir greič<PERSON>u"}}}, "footer": {"subscribe": {"error": "Prenumerata <PERSON>vyko"}}, "onboarding": {"product_title": "Produkto pavadinimo pavyzdys"}, "search": {"product_list": {"title": "<PERSON><PERSON><PERSON><PERSON>"}}}