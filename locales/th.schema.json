{"sections": {"announcement-bar": {"name": "กระดานข่าว", "settings": {"enable_sticky": {"label": "กระดานข่าวถูกตรึงไว้ด้านบน"}, "sticky_mode": {"label": "ตำแหน่งที่แสดง", "options__0": {"label": "ปักหมุดด้านบนทั้งหมด"}, "options__1": {"label": "ปักหมุดดเฉพาะ PC"}, "options__2": {"label": "ปักหมุดดเฉพาะ บนมือถือ"}}, "enable_autoplay": {"label": "เปิดใช้งานการสลับอัตโนมัติ"}, "autoplay_speed": {"label": "ช่วงการสลับ"}, "show_social_media": {"label": "แสดงไอคอนโซเชียลมีเดียบนPC", "info": "คุณสามารถไปที่  [การตั้งค่าธีม-โซเชียลมีเดีย](/editor?locator=settings&category=media_social) เพื่อดูลิงก์เพิ่มเติม โซเชียลมีเดียจะไม่แสดงหากเค้าโครงกระดานข่าวกะทัดรัด เรียงต่อกัน หรือเลื่อนไปทางซ้าย/ขวาอัตโนมัติ"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"item": {"name": "ประกาศ", "settings": {"notice_link_text": {"label": "ข้อความ"}, "notice_link_mb_text": {"label": "ข้อความบนมือถือ", "info": "หากไม่ได้กรอก บนมือถือจะแสดงข้อความบนพีซีตามค่าเริ่มต้น"}, "notice_link": {"label": "ลิงก์กระโดดข้าม"}, "announcement_division_bottom": {"label": "แสดงเส้นแบ่ง"}, "notice_text_color": {"label": "สีของข้อความ"}, "notice_bg_color": {"label": "สีพื้นหลัง"}}}}}, "apps": {"name": "แอปพลิเคชัน", "settings": {"include_margins": {"label": "ทำให้ระยะขอบของส่วนเหมือนกับธีม"}}}, "blog": {"name": "บล็อก", "settings": {"title": {"label": "หัวข้อ"}, "title_align": {"label": "การจัดตำแหน่งหัวข้อ"}, "blog_collection": {"label": "คอลเลกชั่นบล็อก"}, "limit": {"label": "จำนวนบล็อก", "unit": "รายการ"}, "pc_cols": {"label": "จำนวนคอลัมน์บนคอมพิวเตอร์"}, "mobile_cols": {"label": "จำนวนคอลัมน์บนมือถือ", "options__0": {"label": "1 คอลัมน์"}, "options__1": {"label": "2 คอลัมน์"}}, "enable_mobile_slide": {"label": "ปัดไปทางซ้ายและขวาบนมือถือ"}, "mobile_pagination_style": {"label": "ด้านข้างมือถือเลื่อนสไตล์การพลิกหน้าซ้ายและขวา", "options__0": {"label": "แถบความคืบหน้า"}, "options__1": {"label": "จุดเล็ก"}, "options__2": {"label": "เรียบง่าย"}}, "is_show_date": {"label": "แสดงวันที่"}, "is_show_author": {"label": "แสดงผู้เขียน"}, "is_show_cover": {"label": "แสดงภาพหน้าปก"}, "image_cover_ratio": {"label": "มาตราส่วนของภาพหน้าปก", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "1:1"}, "options__2": {"label": "4:3"}, "options__3": {"label": "3:2"}, "options__4": {"label": "16:9"}}, "is_show_desc": {"label": "แสดงสรุป"}, "btn_text": {"label": "ข้อความปุ่ม"}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "presets": {"presets__0": {"category": "บล็อก", "name": "บล็อก"}}}, "collapsible-content": {"name": "ปัญหาที่พบบ่อย", "settings": {"heading": {"label": "หัวข้อ"}, "heading_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}, "heading_alignment": {"label": "รูปแบบการจัดตำแหน่ง", "options__0": {"label": "ซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ขวา"}}, "layout_style": {"label": "รูปแบบเค้าโครง", "options__0": {"label": "ไม่มีภาชนะ"}, "options__1": {"label": "คอนเทนเนอร์สําหรับแต่ละแถว"}, "options__2": {"label": "คอนเทนเนอร์พาร์ติชัน"}}, "background_color": {"label": "สีพื้นหลังของคอนเทนเนอร์"}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "open_first_collapsible_row": {"label": "ขยายแท็บแรกตามค่าเริ่มต้น"}, "group_header__0": {"label": "รูปภาพ"}, "image": {"label": "รูปภาพ"}, "image_ratio": {"label": "ความสูงของรูปภาพ", "options__0": {"label": "ปรับให้เข้ากับภาพ"}, "options__1": {"label": "สูง"}, "options__2": {"label": "ต่ำ"}}, "desktop_layout": {"label": "ตำแหน่งภาพคอมพิวเตอร์", "info": "เทอร์มินัลมือถือมีค่าเริ่มต้นตามข้อความอยู่ภายใต้รูปภาพ", "options__0": {"label": "ภาพซ้ายและข้อความขวา"}, "options__1": {"label": "ข้อความซ้ายและภาพขวา"}}, "group_header__1": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"collapsible_row": {"name": "แถวที่พับได้", "settings": {"heading": {"label": "หัวข้อ"}, "icon": {"label": "ไอคอน", "options__0": {"label": "เปล่า"}, "options__1": {"label": "แอปเปิ้ล"}, "options__2": {"label": "กล้วย"}, "options__3": {"label": "ขวด"}, "options__4": {"label": "กล่อง"}, "options__5": {"label": "แครอท"}, "options__6": {"label": "ฟองแชท"}, "options__7": {"label": "เครื่องหมายถูก"}, "options__8": {"label": "คลิปบอร์ด"}, "options__9": {"label": "ผลิตภัณฑ์นม"}, "options__10": {"label": "ไม่มีนม"}, "options__11": {"label": "เครื่องเป่าผม"}, "options__12": {"label": "ดวงตา"}, "options__13": {"label": "ไฟ"}, "options__14": {"label": "ตังฟรี"}, "options__15": {"label": "รูปหัวใจ"}, "options__16": {"label": "เหล็ก"}, "options__17": {"label": "ใบไม้"}, "options__18": {"label": "หนัง"}, "options__19": {"label": "สายฟ้า"}, "options__20": {"label": "ลิปสติก"}, "options__21": {"label": "ล็อค"}, "options__22": {"label": "เป๊ก"}, "options__23": {"label": "ไม่มีส่วนผสมของถั่ว"}, "options__24": {"label": "กางเกง"}, "options__25": {"label": "พิมพ์อุ้งเท้า"}, "options__26": {"label": "พริกไทย"}, "options__27": {"label": "กางเกง"}, "options__28": {"label": "เครื่องบิน"}, "options__29": {"label": "พืชสีเขียว"}, "options__30": {"label": "ป้ายราคา"}, "options__31": {"label": "เครื่องหมายคำถาม"}, "options__32": {"label": "รีไซเคิลและนำกลับมาใช้ใหม่"}, "options__33": {"label": "คืนเงิน"}, "options__34": {"label": "ไม้บรรทัด"}, "options__35": {"label": "จานอาหารค่ำ"}, "options__36": {"label": "เสื้อ"}, "options__37": {"label": "รองเท้า"}, "options__38": {"label": "ภาพเงา"}, "options__39": {"label": "เกล็ดหิมะ"}, "options__40": {"label": "ดาว"}, "options__41": {"label": "นาฬิกาจับเวลา"}, "options__42": {"label": "รถบรรทุก"}, "options__43": {"label": "ผงซักฟอก"}}, "upload_icon": {"label": "ไอคอนอัปโหลด"}, "icon_width": {"label": "ความกว้างของไอคอนบนพีซี"}, "row_content": {"label": "คำอธิบาย"}, "page": {"label": "เนื้อหาที่กำหนดเอง"}}}}, "presets": {"presets__0": {"category": "ส่วนประกอบที่ไว้วางใจ"}}}, "collection-list": {"name": "หมวดหมู่", "settings": {"title": {"label": "หัวข้อ"}, "collection_image_ratio": {"label": "อัตราส่วนภาพหมวดหมู่", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "1:1"}, "options__2": {"label": "4:3"}, "options__3": {"label": "2:3"}}, "collection_image_shape": {"label": "รูปร่างรูปภาพหมวดหมู่", "options__0": {"label": "สี่เหลี่ยม"}, "options__1": {"label": "ทรงกลม"}}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "desktop_grid_cols": {"label": "จำนวนคอลัมน์บนคอมพิวเตอร์"}, "m_cols": {"label": "จำนวนคอลัมน์บนมือถือ", "options__0": {"label": "1 คอลัมน์"}, "options__1": {"label": "2 คอลัมน์"}, "options__2": {"label": "3 คอลัมน์"}, "options__3": {"label": "4 คอลัมน์"}}, "m_rows": {"label": "เลื่อนซ้ายและขวาเพื่อเลือกจำนวนแถวบนโทรศัพท์มือถือ", "info": "เมื่อเปิดใช้งานการเลื่อนซ้ายและขวา จำนวนแถวเลื่อนซ้ายและขวาจะเป็นไปตาม \"จำนวนแถวบนโทรศัพท์มือถือ\" และจำนวนแถวเลื่อนซ้ายและขวาจะเป็นไปตามตัวเลือกนี้", "options__0": {"label": "1 บรรทัด"}, "options__1": {"label": "2 บรรทัด"}, "options__2": {"label": "3 บรรทัด"}}, "slice_in_mobile": {"label": "ปัดไปทางซ้ายและขวาบนมือถือเพื่อดู"}, "slice_in_pc": {"label": "ปัดไปทางซ้ายและขวาบนคอมพิวเตอร์เพื่อดู"}, "max_in_mobile": {"label": "จำนวนหมวดหมู่สูงสุดที่แสดงบนเทอร์มินัลมือถือ"}, "button_text": {"label": "ข้อความปุ่ม"}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"collection": {"name": "หมวดหมู่", "settings": {"category": {"label": "หมวดหมู่"}, "title": {"label": "หัวข้อ"}, "image_display_area": {"label": "พื้นที่แสดงรูปภาพ", "info": "ปรับพื้นที่แสดงรูปภาพสินค้า", "options__0": {"label": "บนซ้าย"}, "options__1": {"label": "ด้านบนโดยตรง"}, "options__2": {"label": "บนขวา"}, "options__3": {"label": "ด้านซ้าย"}, "options__4": {"label": "กลาง"}, "options__5": {"label": "ด้านขวา"}, "options__6": {"label": "ล่างซ้าย"}, "options__7": {"label": "ด้านล่างโดยตรง"}, "options__8": {"label": "ล่างขวา"}}}}}, "presets": {"presets__0": {"category": "โชว์สินค้า", "name": "หมวดหมู่"}}}, "combine-shoppable-image": {"name": "ภาพการซื้อแบบแพคเกจ", "settings": {"text_title": {"label": "หัวข้อ"}, "description": {"label": "อธิบาย"}, "button_text": {"label": "ข้อความปุ่ม"}, "jump_link": {"label": "ลิงก์กระโดดข้าม"}, "show_columns": {"label": "แสดงคอลัมน์", "options__0": {"label": "1 คอลัมน์"}, "options__1": {"label": "2 คอลัมน์"}, "options__2": {"label": "3 คอลัมน์"}}, "image_ratio": {"label": "อัตราส่วนของรูปภาพ", "options__0": {"label": "2:3"}, "options__1": {"label": "1:1"}, "options__3": {"label": "2:1"}}, "group_header__0": {"label": "จุดยึด"}, "anchor_quick_view": {"label": "ชี้ไปที่ปุ่มซื้อสินค้าด่วน"}, "anchor_show_type": {"label": "โหมดการแสดงจุดยึด", "options__0": {"label": "แสดงชื่อและราคาหลังจากคลิกจุดยึด"}, "options__1": {"label": "แก้ไขการแสดงชื่อและราคา"}}, "group_header__1": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"image": {"name": "รูปภาพ", "settings": {"image": {"label": "รูปภาพ"}, "product_butotn_text": {"label": "ข้อความปุ่มกดสินค้า"}, "product1": {"label": "สินค้า 1"}, "horizontal_axis_position1": {"label": "ตำแหน่งแกนนอน"}, "vertical_axis_position1": {"label": "ตำแหน่งแกนแนวตั้ง"}, "product2": {"label": "สินค้า 2"}, "horizontal_axis_position2": {"label": "ตำแหน่งแกนนอน"}, "vertical_axis_position2": {"label": "ตำแหน่งแกนแนวตั้ง"}, "product3": {"label": "สินค้า 3"}, "horizontal_axis_position3": {"label": "ตำแหน่งแกนนอน"}, "vertical_axis_position3": {"label": "ตำแหน่งแกนแนวตั้ง"}, "product4": {"label": "สินค้า 4"}, "horizontal_axis_position4": {"label": "ตำแหน่งแกนนอน"}, "vertical_axis_position4": {"label": "ตำแหน่งแกนแนวตั้ง"}, "product5": {"label": "สินค้า 5"}, "horizontal_axis_position5": {"label": "ตำแหน่งแกนนอน"}, "vertical_axis_position5": {"label": "ตำแหน่งแกนแนวตั้ง"}}}}, "presets": {"presets__0": {"category": "โชว์สินค้า", "name": "ภาพการซื้อแบบแพคเกจ"}}}, "contact-form": {"name": "แบบฟอร์มการติดต่อ", "settings": {"heading": {"label": "หัวข้อ"}, "heading_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "presets": {"presets__0": {"category": "การดำเนินงานของลูกค้า", "name": "แบบฟอร์มการติดต่อ"}}}, "custom-html": {"name": "HTML ที่กำหนดเอง", "settings": {"html": {"label": "HTML"}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "presets": {"presets__0": {"category": "กำหนดเอง", "name": "HTML ที่กำหนดเอง"}}}, "custom-page": {"name": "หน้าเพจที่กำหนดเอง", "settings": {"page": {"label": "หน้าเพจที่กำหนดเอง"}, "heading_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "presets": {"presets__0": {"category": "กำหนดเอง", "name": "หน้าเพจที่กำหนดเอง"}}}, "featured-collection-with-banner": {"name": "สินค้าแนะนำพร้อมภาพปก", "settings": {"title": {"label": "หัวข้อ"}, "heading_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}, "product_categories": {"label": "หมวดหมู่"}, "show_collections_desc": {"label": "แสดงคำอธิบายหมวดหมู่"}, "group_header__0": {"label": "ภาพหน้าปก"}, "show_collection_image": {"label": "ภาพหน้าปกแสดงภาพหมวดหมู่"}, "image": {"label": "รูปภาพ"}, "image_opacticy": {"label": "ความทึบของเลเยอร์รูปภาพ"}, "text_align": {"label": "การจัดตำแหน่งข้อความ", "options__0": {"label": "ด้านซ้าย"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ด้านขวา"}}, "collection_title": {"label": "หัวข้อ"}, "collection_description": {"label": "ข้อความ"}, "collection_text_color": {"label": "สีข้อความ"}, "collection_button_text": {"label": "การเขียนคำโฆษณาปุ่มภาพการจัดหมวดหมู่"}, "group_header__1": {"label": "รายการ"}, "products_num": {"label": "จำนวนสินค้าสูงสุด"}, "pc_cols": {"label": "จำนวนคอลัมน์บนคอมพิวเตอร์"}, "mobile_cols": {"label": "จำนวนคอลัมน์บนมือถือ", "options__0": {"label": "1 คอลัมน์"}, "options__1": {"label": "2 คอลัมน์"}}, "slice_in_pc": {"label": "ปัดไปทางซ้ายและขวาบนคอมพิวเตอร์เพื่อดู"}, "slice_in_mobile": {"label": "ปัดไปทางซ้ายและขวาบนมือถือเพื่อดู"}, "button_text": {"label": "ข้อความปุ่ม", "info": "ปุ่มจะแสดงก็ต่อเมื่อ \"จำนวนสินค้าสูงสุด\" น้อยกว่าหรือเท่ากับจำนวนสินค้าในหมวดนี้"}, "group_header__2": {"label": "ภาพสินค้า"}, "product_image_ratio": {"label": "อัตราส่วนภาพสินค้า", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "image_fill_type": {"label": "วิธีการเติมภาพสินค้า", "options__0": {"label": "ปรับ"}, "options__1": {"label": "การกรอก"}}, "show_secondary_image": {"label": "แสดงรูปภาพสินค้ารูปถัดไปเมื่อวางเมาส์ไว้"}, "group_header__3": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"image": {"name": "รูปภาพ"}, "title": {"name": "หัวข้อ"}, "price": {"name": "ราคา"}, "highlight": {"name": "การอธิบายจุดแด่น", "settings": {"group_header__0": {"label": "ในการใช้คุณลักษณะนี้ สร้างชื่อเนมสเปซ \"highlights\" และคีย์ที่ชื่อว่า \"list\" สำหรับเมตาดาต้าสินค้า โดยเลือกประเภทข้อมูลเป็น \"ข้อความหลายบรรทัด\" หลังจากสร้างแล้ว คุณสามารถเพิ่มค่าให้กับฟิลด์เมตาดาต้าในสินค้าที่เฉพาะเจาะจง และจะแสดงบนหน้าเว็บไซต์"}}}, "text": {"name": "ข้อความ", "settings": {"text": {"label": "ข้อความ"}}}, "divider": {"name": "เส้นแบ่ง"}, "brand": {"name": "ยี่ห้อ"}, "sku": {"name": "sku"}, "quick_add_button": {"name": "ปุ่มซื้อสินค้าด่วน"}}, "presets": {"presets__0": {"category": "โชว์สินค้า", "name": "สินค้าแนะนำพร้อมภาพปก"}}}, "featured-collection": {"name": "สินค้าแนะนำ", "settings": {"title": {"label": "หัวข้อ"}, "heading_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}, "collection_1": {"label": "หมวดสินค้า1"}, "label_1": {"label": "หัวข้อแท็บ 1"}, "collection_2": {"label": "หมวดสินค้า2"}, "label_2": {"label": "หัวข้อแท็บ 2"}, "collection_3": {"label": "หมวดสินค้า3"}, "label_3": {"label": "หัวข้อแท็บ 3"}, "collection_4": {"label": "หมวดสินค้า4"}, "label_4": {"label": "หัวข้อแท็บ 4"}, "collection_5": {"label": "หมวดสินค้า5"}, "label_5": {"label": "หัวข้อแท็บ 5"}, "collection_6": {"label": "หมวดสินค้า6"}, "label_6": {"label": "หัวข้อแท็บ 6"}, "group_header__0": {"label": "รายการ"}, "products_to_show": {"label": "จำนวนสินค้าสูงสุด"}, "columns_desktop": {"label": "จำนวนคอลัมน์บนคอมพิวเตอร์"}, "full_width": {"label": "ความกว้างเต็มหน้าจอบนพีซี"}, "columns_mobile": {"label": "จำนวนคอลัมน์บนมือถือ", "options__0": {"label": "1 คอลัมน์"}, "options__1": {"label": "2 คอลัมน์"}}, "enable_desktop_slider": {"label": "ปัดไปทางซ้ายและขวาบนคอมพิวเตอร์เพื่อดู"}, "enable_mobile_slider": {"label": "ปัดไปทางซ้ายและขวาบนมือถือเพื่อดู"}, "button_text": {"label": "ข้อความปุ่ม", "info": "ปุ่มจะแสดงก็ต่อเมื่อ \"จำนวนสินค้าสูงสุด\" น้อยกว่าหรือเท่ากับจำนวนสินค้าในหมวดนี้"}, "full_in_mobile": {"label": "การแสดงผลแบบเต็มหน้าจอบนเทอร์มินัลมือถือ"}, "group_header__1": {"label": "ภาพสินค้า"}, "product_image_ratio": {"label": "อัตราส่วนภาพสินค้า", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "image_fill_type": {"label": "วิธีการเติมภาพสินค้า", "options__0": {"label": "ปรับ"}, "options__1": {"label": "การกรอก"}}, "image_grid_shape": {"label": "รูปร่างรูปภาพหมวดหมู่", "options__0": {"label": "ทรงกลม"}, "options__1": {"label": "สี่เหลี่ยม"}}, "show_secondary_image": {"label": "แสดงรูปภาพสินค้ารูปถัดไปเมื่อวางเมาส์ไว้"}, "group_header__2": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"image": {"name": "รูปภาพ"}, "title": {"name": "หัวข้อ"}, "price": {"name": "ราคา"}, "highlight": {"name": "การอธิบายจุดแด่น", "settings": {"group_header__0": {"label": "ในการใช้คุณลักษณะนี้ สร้างชื่อเนมสเปซ \"highlights\" และคีย์ที่ชื่อว่า \"list\" สำหรับเมตาดาต้าสินค้า โดยเลือกประเภทข้อมูลเป็น \"ข้อความหลายบรรทัด\" หลังจากสร้างแล้ว คุณสามารถเพิ่มค่าให้กับฟิลด์เมตาดาต้าในสินค้าที่เฉพาะเจาะจง และจะแสดงบนหน้าเว็บไซต์"}}}, "text": {"name": "ข้อความ", "settings": {"text": {"label": "ข้อความ"}}}, "divider": {"name": "เส้นแบ่ง"}, "brand": {"name": "ยี่ห้อ"}, "sku": {"name": "sku"}, "quick_add_button": {"name": "ปุ่มซื้อสินค้าด่วน"}}, "presets": {"presets__0": {"category": "โชว์สินค้า", "name": "สินค้าแนะนำ"}}}, "featured-product": {"name": "สินค้าชิ้นเดียว", "settings": {"product": {"label": "สินค้า"}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "secondary_background": {"label": "แสดงบล็อกสีพื้นหลัง"}, "group_header__0": {"label": "สื่อ"}, "product_image_pc_position": {"label": "ตำแหน่งสื่อด้านคอมพิวเตอร์", "options__0": {"label": "ซ้าย"}, "options__1": {"label": "ขวา"}}, "magnifier_interactive_type": {"label": "โหมดขยายภาพหลัก", "options__0": {"label": "โหมด 1"}, "options__1": {"label": "โหมด 2"}}, "video_loop": {"label": "เปิดใช้งานวิดีโอวนซ้ำ"}, "group_header__1": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"title": {"name": "หัวข้อ", "settings": {"heading_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}}}, "price": {"name": "ราคา"}, "variant_picker": {"name": "ตัวเลือกคุณลักษณะหลายรายการ", "settings": {"picker_type": {"label": "จอแสดงผลหลายคุณลักษณะ", "options__0": {"label": "เรียงต่อกัน"}, "options__1": {"label": "หล่นลง"}}}}, "quantity_selector": {"name": "ตัวเลือกจำนวน"}, "buy_buttons": {"name": "ปุ่มซื้อ"}, "share": {"name": "แบ่งปัน"}, "html": {"name": "HTML ที่กำหนดเอง", "settings": {"html": {"label": "HTML"}}}, "text": {"name": "ข้อความ", "settings": {"text": {"label": "ข้อความ"}, "text_style": {"label": "สไตล์ข้อความ", "options__0": {"label": "ข้อความ"}, "options__1": {"label": "หัวข้อรอง"}, "options__2": {"label": "ตัวพิมพ์ใหญ่"}}}}, "highlight": {"name": "การอธิบายจุดแด่น"}}, "presets": {"presets__0": {"category": "โชว์สินค้า", "name": "สินค้าชิ้นเดียว"}}}, "featured-slideshow": {"name": "ภาพหมุนที่โดดเด่น", "settings": {"section_height": {"label": "ความสูงบนคอมพิวเตอร์", "options__0": {"label": "ปรับให้เข้ากับขนาดภาพแรก"}, "options__1": {"label": "450px"}, "options__2": {"label": "550px"}, "options__3": {"label": "650px"}, "options__4": {"label": "750px"}}, "autoplay": {"label": "เปิดใช้งานการสลับอัตโนมัติ"}, "autoplay_speed": {"label": "เปลี่ยนสไลด์ทุกๆ", "unit": "วินาที"}, "group_header__0": {"label": "ภาพนิ่ง1"}, "pc_static_image1": {"label": "รูปภาพบนคอมพิวเตอร์"}, "mb_static_image1": {"label": "รูปภาพมือถือ"}, "overlay_opacity1": {"label": "หน้ากากภาพ"}, "pc_static_text_position1": {"label": "ตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "ด้านซ้าย"}, "options__1": {"label": "ตรงกลาง"}, "options__2": {"label": "ด้านขวา"}}, "static_text_align1": {"label": "การจัดตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "mb_static_text_align1": {"label": "การจัดตำแหน่งเนื้อหาบนมือถือ", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "title1": {"label": "หัวข้อหลัก"}, "title_size1": {"label": "ขนาดตัวอักษรของหัวข้อหลัก"}, "subheading1": {"label": "ข้อความ"}, "jump_link1": {"label": "ลิงก์กระโดดข้าม"}, "text_color1": {"label": "สีของข้อความ"}, "group_header__1": {"label": "ภาพนิ่ง2"}, "pc_static_image2": {"label": "รูปภาพบนคอมพิวเตอร์"}, "mb_static_image2": {"label": "รูปภาพมือถือ"}, "overlay_opacity2": {"label": "หน้ากากภาพ"}, "pc_static_text_position2": {"label": "ตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "ด้านซ้าย"}, "options__1": {"label": "ตรงกลาง"}, "options__2": {"label": "ด้านขวา"}}, "static_text_align2": {"label": "การจัดตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "mb_static_text_align2": {"label": "การจัดตำแหน่งเนื้อหาบนมือถือ", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "title2": {"label": "หัวข้อหลัก"}, "title_size2": {"label": "ขนาดตัวอักษรของหัวข้อหลัก"}, "subheading2": {"label": "ข้อความ"}, "jump_link2": {"label": "ลิงก์กระโดดข้าม"}, "text_color2": {"label": "สีของข้อความ"}}, "blocks": {"image": {"name": "ภาพหมุน", "settings": {"image": {"label": "รูปภาพบนคอมพิวเตอร์"}, "image_mobile": {"label": "รูปภาพมือถือ"}, "overlay_opacity": {"label": "หน้ากากภาพ"}, "text_mask": {"label": "เลเยอร์ข้อความ"}, "text_mask_color": {"label": "สีของเลเยอร์ข้อความ", "options__0": {"label": "สีเข้ม"}, "options__1": {"label": "สีอ่อน"}}, "pc_text_position": {"label": "ตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "ด้านซ้าย"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ด้านขวา"}, "options__3": {"label": "บนซ้าย"}, "options__4": {"label": "ด้านบนโดยตรง"}, "options__5": {"label": "บนขวา"}, "options__6": {"label": "ล่างซ้าย"}, "options__7": {"label": "ด้านล่างโดยตรง"}, "options__8": {"label": "ล่างขวา"}}, "pc_text_align": {"label": "การจัดตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "mobile_text_align": {"label": "การจัดตำแหน่งเนื้อหาบนมือถือ", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "group_header__0": {"label": "เนื้อหาข้อความ"}, "sub_title": {"label": "หัวข้อรอง"}, "title": {"label": "หัวข้อหลัก"}, "title_size": {"label": "ขนาดตัวอักษรของหัวข้อหลัก"}, "subheading": {"label": "ข้อความ"}, "group_header__1": {"label": "เนื้อหาปุ่ม"}, "link_text": {"label": "ข้อความปุ่ม"}, "link": {"label": "ลิงก์กระโดดข้าม"}, "is_profile_link": {"label": "ใช้รูปแบบปุ่มเค้าร่าง"}, "link_text_2": {"label": "เนื้อหาปุ่ม2"}, "link_2": {"label": "ข้ามลิงก์2"}, "is_profile_link2": {"label": "ใช้รูปแบบปุ่มเค้าร่าง2"}, "text_color": {"label": "สีของข้อความ"}}}}, "presets": {"presets__0": {"category": "การแสดงกราฟิก", "name": "ภาพหมุน"}}}, "footer": {"name": "ท้ายของหน้า", "settings": {"color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "show_pay_channel": {"label": "แสดงไอคอนการชำระเงิน"}, "show_country_selector": {"label": "แสดงตัวเลือกประเทศ/ภูมิภาค", "info": "ต้องเพิ่มตลาดสองแห่งขึ้นไปเพื่อให้ตัวเลือกประเทศที่แสดงทำงานได้ หากต้องการเพิ่มตลาด ให้ไปที่ [การตั้งค่าตลาด](/admin/settings/markets)"}, "show_locale_selector": {"label": "แสดงตัวเลือกภาษา", "info": "ต้องเพิ่มสองภาษาขึ้นไปในตลาด-การจัดการภาษา เพื่อให้ตัวเลือกมีผล หากคุณต้องการเพิ่มภาษา คุณสามารถไปที่ [การตั้งค่าภาษา] (/admin/settings/lang) หลังจากเพิ่มแล้ว คุณสามารถไปที่ [ตลาด] (/admin/settings/markets) - หน้าการจัดการภาษาเพื่อเพิ่มตลาดหลายภาษาในปัจจุบัน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"menu": {"name": "การนำทางอย่างรวดเร็ว", "settings": {"title": {"label": "หัวข้อ"}, "menu": {"label": "เมนูนำทาง"}, "span": {"label": "ความกว้างของคอลัมน์"}, "default_fold": {"label": "ขยายตามค่าเริ่มต้น"}}}, "custom": {"name": "เนื้อหาที่กำหนดเอง", "settings": {"title": {"label": "หัวข้อ"}, "content": {"label": "ข้อความ"}, "span": {"label": "ความกว้างของคอลัมน์"}}}, "image": {"name": "รูปภาพ", "settings": {"image": {"label": "รูปภาพ"}, "image_width": {"label": "ความกว้างของภาพ"}, "span": {"label": "ความกว้างของคอลัมน์"}}}, "newsletter": {"name": "การสมัครอีเมล", "settings": {"title": {"label": "หัวข้อ"}, "desc": {"label": "อธิบาย"}, "subscribe_letter_placeholder": {"label": "ข้อความแจ้งเตือนสำหรับกล่องใส่ข้อความ"}, "span": {"label": "ความกว้างของคอลัมน์"}}}, "social_media": {"name": "ไอคอนโซเชียลมีเดีย", "settings": {"span": {"label": "ความกว้างของคอลัมน์"}}}}}, "header": {"name": "หัวของหน้า", "settings": {"full_width": {"label": "เปิดใช้งานความกว้างเต็มหน้าจอ"}, "header_division_bottom": {"label": "แสดงเส้นแบ่งด้านล่างส่วนหัว"}, "group_header__0": {"label": "ค้นหา"}, "show_search_mobile": {"label": "แสดงแถบค้นหาบนมือถือ"}, "search_menu": {"label": "เมนูแนะนำ", "info": "หลังจากตั้งค่าเมนูแล้ว แถบเมนูจะถูกนำมาใช้เพื่อให้คำแนะนำเมื่อเปิดการค้นหาครั้งแรก"}, "group_header__1": {"label": "ประกาศไอคอน"}, "show_icon": {"label": "แสดงคำสั่งไอคอน"}, "icon": {"label": "ไอคอน", "options__0": {"label": "ไม่แสดง"}, "options__1": {"label": "การชำระเงินที่ปลอดภัย"}, "options__2": {"label": "พัสดุ"}, "options__3": {"label": "อีเมล"}, "options__4": {"label": "ตำแหน่ง"}, "options__5": {"label": "ลูกค้า"}, "options__6": {"label": "สนทนา"}, "options__7": {"label": "ของขวัญ"}, "options__8": {"label": "โทรศัพท์มือถือ"}, "options__9": {"label": "เครื่องหมายคำถาม"}, "options__10": {"label": "โลจิสติกส์"}, "options__11": {"label": "ป้ายส่วนลด"}, "options__12": {"label": "เครื่องอิสริยาภรณ์"}, "options__13": {"label": "การคุ้มครองสิ่งแวดล้อม"}}, "icon_image": {"label": "รูปภาพ", "info": "ขนาดที่แนะนำ 30 x 30px"}, "icon_title": {"label": "หัวข้อ"}, "icon_sub_title": {"label": "หัวข้อรอง"}, "icon_link": {"label": "ลิงก์กระโดดข้าม"}, "group_header__2": {"label": "แถบเครื่องมือ", "info": "แถบเครื่องมือจะแสดงที่ด้านบนส่วนหัว"}, "show_tool": {"label": "แสดงแถบเครื่องมือ"}, "show_tool_full": {"label": "เปิดใช้งานการแสดงผลแบบจอกว้าง"}, "show_locale_selector": {"label": "แสดงตัวเลือกภาษา", "info": "ต้องเพิ่มสองภาษาขึ้นไปในตลาด-การจัดการภาษา เพื่อให้ตัวเลือกมีผล หากคุณต้องการเพิ่มภาษา คุณสามารถไปที่ [การตั้งค่าภาษา] (/admin/settings/lang) หลังจากเพิ่มแล้ว คุณสามารถไปที่ [ตลาด] (/admin/settings/markets) - หน้าการจัดการภาษาเพื่อเพิ่มตลาดหลายภาษาในปัจจุบัน"}, "show_country_selector": {"label": "แสดงตัวเลือกประเทศ", "info": "ต้องเพิ่มตลาดสองแห่งขึ้นไปเพื่อให้ตัวเลือกประเทศที่แสดงทำงานได้ หากต้องการเพิ่มตลาด ให้ไปที่ [การตั้งค่าตลาด](/admin/settings/markets)"}, "toolbar_social": {"label": "แสดงไอคอนโซเชียลมีเดีย", "info": "สามารถไปที่ [การตั้งค่าธีม-โซเชียลมีเดีย](/editor?locator=settings&category=media_social) เพื่อเพิ่มที่อยู่ลิงก์ที่เกี่ยวข้อง"}, "toolbar_bacground_color": {"label": "สีพื้นหลังของแถบเครื่องมือ"}, "toolbar_link_color": {"label": "สีลิงค์แถบเครื่องมือ"}, "toolbar_link_hover_color": {"label": "สีโฮเวอร์ลิงก์ของแถบเครื่องมือ"}, "toolbar_menu": {"label": "เมนูนำทาง (แถบเครื่องมือ)", "info": "แถบเครื่องมือไม่แสดงเมนูแบบเลื่อนลง แสดงเฉพาะเมนูระดับแรก"}, "toolbar_menu_mobile": {"label": "แสดงเมนูบนโทรศัพท์มือถือ"}, "toolbar_menu_position": {"label": "ตำแหน่งแสดงเมนูบนโทรศัพท์มือถือ", "options__0": {"label": "ด้านบนเมนูหลัก"}, "options__1": {"label": "ด้านล่างเมนูหลัก"}}, "group_header__3": {"label": "เมนู"}, "main_menu_link_list": {"label": "การนำทางเมนูหลัก"}, "second_menu_link_list": {"label": "การนำทางเมนูรอง", "info": "การนำทางเมนูรองไม่แสดงเมนูแบบเลื่อนลง แสดงเฉพาะเมนูระดับแรก"}, "body_pc_second_font_size": {"label": "ขนาดตัวอักษรการนำทางรองบนพีซี", "options__0": {"label": "ใหญ่"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "เล็ก"}}, "body_pc_second_font_bold": {"label": "การแสดงการนำทางรองอย่างหนาบนคอมพิวเตอร์"}, "body_pc_thirdly_font_size": {"label": "ขนาดตัวอักษรของการนำทางระดับที่สามบนพีซี", "options__0": {"label": "ใหญ่"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "เล็ก"}}, "mobile_top_menu": {"label": "เมนูด้านบนบนโทรศัพท์มือถือ", "info": "หลังจากกำหนดค่า เมนูจะได้รับการแก้ไขที่ด้านล่างของส่วนหัวของโทรศัพท์มือถือ สามารถเลื่อนไปทางซ้ายหรือขวาเพื่อสลับการแสดงผลได้"}, "mobile_top_menu_show_home": {"label": "เมนูที่ปักหมุดไว้จะปรากฏเฉพาะในหน้าแรกเท่านั้น", "info": "หลังจากตั้งค่าแล้ว เมนูด้านบนจะแสดงเฉพาะในหน้าแรกเท่านั้น"}, "group_header__4": {"label": "ไฮไลท์", "info": "สามารถไฮไลต์รายการเมนูเฉพาะได้"}, "enable_highlight": {"label": "เปิดใช้งานการไฮไลต์"}, "highlight_menus": {"label": "ไฮไลท์เมนู", "info": "กรุณากรอกชื่อเมนูระดับแรกที่ต้องการแสดงเด่น หากป้อนหลายเมนู ตรงกลางโปรดดั่นด้วยเครื่องหมายจุลภาคภาษาอังกฤษ \",\""}, "highlight_text_color": {"label": "ไฮไลท์สีข้อความ"}, "highlight_bg_color": {"label": "ไฮไลท์สีพื้นหลัง"}, "group_header__5": {"label": "สี"}, "header_background_color": {"label": "สีพื้นหลังของส่วนหัว"}, "header_text_color": {"label": "สีข้อความส่วนหัว"}, "menu_background_color": {"label": "สีพื้นหลังของเมนู"}, "menu_text_color": {"label": "สีข้อความเมนู"}, "search_color": {"label": "สีข้อความของช่องค้นหา"}, "search_bacground_color": {"label": "สีพื้นหลังของช่องค้นหา"}, "user_mobile_layout": {"label": "ตำแหน่งศูนย์กลางส่วนบุคคลบนมือถือ", "options__0": {"label": "แสดงในหน้าแรก"}, "options__1": {"label": "แสดงในแถบเมนู"}}, "sticky_header_type": {"label": "ส่วนหัวของหน้าลอยอยู่ด้านบน", "options__0": {"label": "ไม่ปักหมุด"}, "options__1": {"label": "ปักหมุดเสมอ"}, "options__2": {"label": "ปักหมุดเฉพาะเมื่อปัดขึ้น"}}, "show_user_entry": {"label": "แสดงทางเข้าศูนย์ส่วนบุคคล"}, "show_cart_entry": {"label": "แสดงทางเข้าตะกร้าสินค้า"}, "cart_icon": {"label": "ไอคอนตะกร้าสินค้า", "options__0": {"label": "ตะกร้าสินค้า"}, "options__1": {"label": "กระเป๋าช้อปปิ้ง"}}}, "blocks": {"menuImage": {"name": "รูปภาพเมนู", "settings": {"menu_title": {"label": "เมนูทริกเกอร์", "info": "กรุณากรอกชื่อเมนูระดับแรกในส่วนหัวและสามารถแสดงภาพได้เมื่อเมนูมีเมนูระดับล่าง"}, "group_header__0": {"label": "รูปที่ 1"}, "image_1": {"label": "รูปภาพ"}, "image_1_title": {"label": "หัวข้อ"}, "image_1_link_text": {"label": "ข้อความปุ่ม"}, "image_1_link": {"label": "ลิงก์กระโดดข้าม"}, "image_1_position": {"label": "ตำแหน่งภาพ", "options__0": {"label": "ด้านหน้า"}, "options__1": {"label": "ด้านหลัง"}}, "group_header__1": {"label": "รูปที่ 2"}, "image_2": {"label": "รูปภาพ"}, "image_2_title": {"label": "หัวข้อ"}, "image_2_link_text": {"label": "ข้อความปุ่ม"}, "image_2_link": {"label": "ลิงก์กระโดดข้าม"}, "image_2_position": {"label": "ตำแหน่งภาพ", "options__0": {"label": "ด้านหน้า"}, "options__1": {"label": "ด้านหลัง"}}, "group_header__2": {"label": "ภาพ 3"}, "image_3": {"label": "รูปภาพ"}, "image_3_title": {"label": "หัวข้อ"}, "image_3_link_text": {"label": "ข้อความปุ่ม"}, "image_3_link": {"label": "ลิงก์กระโดดข้าม"}, "image_3_position": {"label": "ตำแหน่งภาพ", "options__0": {"label": "ด้านหน้า"}, "options__1": {"label": "ด้านหลัง"}}}}}}, "icon-list": {"name": "รายการเครื่องหมายการค้า", "settings": {"title": {"label": "หัวข้อ"}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"icon": {"name": "เครื่องหมายการค้า", "settings": {"image": {"label": "เพิ่มรูปภาพ", "info": "อัตราส่วนที่แนะนำ: 2-1 / ขนาด: 220-220px อัตราส่วนที่แนะนำ: 1-2 / ขนาด: 157-157px"}, "link": {"label": "ลิงก์กระโดดข้าม"}}}}, "presets": {"presets__0": {"category": "ส่วนประกอบที่ไว้วางใจ", "name": "รายการเครื่องหมายการค้า"}}}, "image-banner": {"name": "แบนเนอร์รูปภาพ", "settings": {"banner1": {"label": "ภาพแรก"}, "banner2": {"label": "ภาพที่สอง"}, "banner_height_size": {"label": "ความสูงของรูปภาพ", "info": "อัตราส่วนภาพที่แนะนำ 2:3", "options__0": {"label": "ต่ำ"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "สูง"}}, "override_banner_height": {"label": "ส่วนสูงเหมาะพอดีกับรูปแรก", "info": "แทนที่การตั้งค่าความสูงของภาพเมื่อเปิดแบบเรียงต่อกัน"}, "pc_content_position": {"label": "ตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "บนซ้าย"}, "options__1": {"label": "ด้านบนโดยตรง"}, "options__2": {"label": "บนขวา"}, "options__3": {"label": "ด้านซ้าย"}, "options__4": {"label": "กลาง"}, "options__5": {"label": "ด้านขวา"}, "options__6": {"label": "ล่างซ้าย"}, "options__7": {"label": "ด้านล่างโดยตรง"}, "options__8": {"label": "ล่างขวา"}}, "pc_text_position": {"label": "การจัดตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "จัดแนวข้อความชิดซ้าย"}, "options__1": {"label": "ตรงกลาง"}, "options__2": {"label": "จัดแนวข้อความชิดขวา"}}, "pc_show_textarea": {"label": "แสดงกล่องข้อความบนคอมพิวเตอร์"}, "alpha_range": {"label": "ความทึบแสงของโอเวอร์เลย์"}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__0": {"label": "เลย์เอาต์บนมือถือ"}, "mobile_text_position": {"label": "การจัดตำแหน่งเนื้อหาบนมือถือ", "options__0": {"label": "จัดแนวข้อความชิดซ้าย"}, "options__1": {"label": "ตรงกลาง"}, "options__2": {"label": "จัดแนวข้อความชิดขวา"}}, "mobile_banner_flatten": {"label": "แสดงรูปภาพของอุปกรณ์มือถือ"}, "m_show_textarea": {"label": "แสดงข้อความใต้ภาพบนมือถือ"}}, "blocks": {"title": {"name": "หัวข้อ", "settings": {"title": {"label": "หัวข้อหลัก"}, "title_size": {"label": "ขนาดตัวอักษรของหัวข้อหลัก", "options__0": {"label": "ใหญ่"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "เล็ก"}}}}, "desc": {"name": "อธิบาย", "settings": {"description": {"label": "อธิบาย"}}}, "button": {"name": "ปุ่ม", "settings": {"button_text": {"label": "ข้อความปุ่ม"}, "link": {"label": "ลิงก์กระโดดข้าม"}, "outline_button": {"label": "ใช้รูปแบบเส้นขอบปุ่ม"}, "link_text_2": {"label": "เนื้อหาปุ่ม2"}, "link_2": {"label": "ข้ามลิงก์2"}, "outline_button_2": {"label": "ใช้รูปแบบเส้นขอบปุ่ม"}}}}, "presets": {"presets__0": {"category": "การแสดงกราฟิก", "name": "แบนเนอร์รูปภาพ"}}}, "image-with-text": {"name": "โมดูลกราฟิก", "settings": {"image": {"label": "เพิ่มรูปภาพ"}, "image_height": {"label": "ความสูงของรูปภาพ", "options__0": {"label": "ปรับให้เข้ากับภาพ"}, "options__1": {"label": "สูง"}, "options__2": {"label": "ต่ำ"}}, "pc_image_width": {"label": "ความกว้างของภาพคอมพิวเตอร์", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}, "pc_image_position": {"label": "ตำแหน่งภาพคอมพิวเตอร์", "info": "เทอร์มินัลมือถือมีค่าเริ่มต้นตามข้อความอยู่ภายใต้รูปภาพ", "options__0": {"label": "ภาพซ้ายและข้อความขวา"}, "options__1": {"label": "ข้อความซ้ายและภาพขวา"}}, "pc_box_align": {"label": "ตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "บนสุด"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ล่างสุด"}}, "pc_text_align": {"label": "การจัดตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "จัดแนวข้อความชิดซ้าย"}, "options__1": {"label": "ตรงกลาง"}, "options__2": {"label": "จัดแนวข้อความชิดขวา"}}, "image_overlap_display": {"label": "แสดงเนื้อหาภาพแบบซ้อนทับ"}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "mobile_text_align": {"label": "การจัดตำแหน่งเนื้อหาบนมือถือ", "options__0": {"label": "จัดแนวข้อความชิดซ้าย"}, "options__1": {"label": "ตรงกลาง"}, "options__2": {"label": "จัดแนวข้อความชิดขวา"}}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"sub_title": {"name": "หัวข้อรอง", "settings": {"text": {"label": "ข้อความ"}, "text_size": {"label": "ขนาดตัวอักษร", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}}}, "title": {"name": "หัวข้อ", "settings": {"title": {"label": "หัวข้อ"}, "title_size": {"label": "ขนาดข้อความชื่อเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}}}, "content": {"name": "ข้อความ", "settings": {"content": {"label": "เนื้อหา"}}}, "button": {"name": "ปุ่ม", "settings": {"button_text": {"label": "ข้อความปุ่ม"}, "link": {"label": "ลิงก์กระโดดข้าม"}}}}, "presets": {"presets__0": {"category": "การแสดงกราฟิก", "name": "โมดูลกราฟิก"}}}, "logo-list": {"name": "ประกาศไอคอน", "settings": {"width": {"label": "ความกว้างของส่วนประกอบ", "options__0": {"label": "เต็มจอ"}, "options__1": {"label": "มาตรฐาน"}}, "layout": {"label": "เลย์เอาต์", "options__0": {"label": "เลย์เอาต์ด้านบนและด้านล่าง"}, "options__1": {"label": "เลย์เอาต์ซ้ายและขวา"}}, "font_color": {"label": "สีตัวอักษร"}, "icon_color": {"label": "สี ICON"}, "background_color": {"label": "สีพื้นหลัง"}, "mobile_display": {"label": "รูปแบบบนมือถือ", "options__0": {"label": "ปัดไปทางซ้ายและขวา"}, "options__1": {"label": "ตะแกรง"}}, "style_card": {"label": "รูปแบบการ์ด"}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"icon": {"name": "ประกาศ", "settings": {"icon": {"label": "ไอคอน", "options__0": {"label": "ไม่แสดง"}, "options__1": {"label": "การชำระเงินที่ปลอดภัย"}, "options__2": {"label": "พัสดุ"}, "options__3": {"label": "อีเมล"}, "options__4": {"label": "ตำแหน่ง"}, "options__5": {"label": "ลูกค้า"}, "options__6": {"label": "สนทนา"}, "options__7": {"label": "ของขวัญ"}, "options__8": {"label": "โทรศัพท์มือถือ"}, "options__9": {"label": "เครื่องหมายคำถาม"}, "options__10": {"label": "โลจิสติกส์"}, "options__11": {"label": "ป้ายส่วนลด"}}, "image": {"label": "รูปภาพ", "info": "อัตราส่วนที่แนะนำ: 1-1 / ขนาด: 48-48px"}, "title": {"label": "หัวข้อ"}, "subtitle": {"label": "หัวข้อรอง"}, "link": {"label": "ลิงก์กระโดดข้าม"}}}}, "presets": {"presets__0": {"category": "ส่วนประกอบที่ไว้วางใจ", "name": "ประกาศไอคอน"}}}, "main-404": {"name": "หน้า 404"}, "main-account": {"name": "บัญชี", "settings": {"group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-activate-account": {"name": "รายละเอียดสินค้า", "settings": {"group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-addresses": {"name": "ที่อยู่", "settings": {"group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-article": {"name": "โพสต์บล็อก", "blocks": {"select": {"name": "รูปภาพประกอบ", "settings": {"image_height": {"label": "ความสูงของภาพปก", "options__0": {"label": "ปรับให้เข้ากับภาพ"}, "options__1": {"label": "เล็ก"}, "options__2": {"label": "กลาง"}, "options__3": {"label": "ใหญ่"}}}}, "title": {"name": "หัวข้อ", "settings": {"blog_show_date": {"label": "แสดงวันที่"}, "blog_show_author": {"label": "แสดงผู้เขียน"}}}, "share": {"name": "แบ่งปัน"}, "content": {"name": "เนื้อหา"}}}, "main-blog-list": {"name": "คอลเลกชั่นบล็อก", "settings": {"layout": {"label": "เลย์เอาต์", "options__0": {"label": "ตะแกรง"}, "options__1": {"label": "รายการ"}}, "page_number": {"label": "จำนวนบล็อกต่อหน้า"}, "columns": {"label": "จำนวนคอลัมน์บนคอมพิวเตอร์", "info": "มีผลเฉพาะในรูปแบบกริดเท่านั้น", "options__0": {"label": "2 คอลัมน์"}, "options__1": {"label": "3 คอลัมน์"}}, "is_show_cover": {"label": "แสดงภาพหน้าปก"}, "cover_img_ratio": {"label": "ความสูงของภาพปก", "options__0": {"label": "ปรับให้เข้ากับภาพ"}, "options__1": {"label": "เล็ก"}, "options__2": {"label": "กลาง"}, "options__3": {"label": "ใหญ่"}}, "is_show_date": {"label": "แสดงวันที่"}, "is_show_author": {"label": "แสดงผู้เขียน"}, "is_show_desc": {"label": "แสดงสรุป"}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-cart-footer": {"name": "ผลรวมย่อย", "blocks": {"buttons": {"name": "ปุ่มชำระเงิน"}, "subtotal": {"name": "ราคารวมย่อย"}}}, "main-cart-items": {"name": "สินค้า", "settings": {"group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-collection-banner": {"name": "หน้าปกหมวดหมู่", "settings": {"show_collection_description": {"label": "แสดงคำอธิบายหมวดหมู่"}, "show_collection_name": {"label": "ชื่อหมวดหมู่ที่แสดง"}, "show_collection_cover": {"label": "แสดงภาพการจัดหมวดหมู่", "info": "หากคุณต้องการตั้งค่า Banner การจัดประเภท คุณสามารถไปที่การจัดการการจัดประเภทสินค้าเพื่อกำหนดค่า [ไปเดี๋ยวนี้](/admin/categories)"}, "banner_image_mobile": {"label": "บนมือถือ banner"}, "pc_collection_img_height": {"label": "ความสูงของภาพหมวดหมู่บนคอมพิวเตอร์"}, "md_collection_img_height": {"label": "ความสูงของภาพหมวดหมู่บนโทรศัพท์มือถือ"}, "collection_cover_area": {"label": "พื้นที่แสดงภาพ", "options__0": {"label": "บนสุด"}, "options__1": {"label": "ส่วนตรงกลาง"}, "options__2": {"label": "ล่างสุด"}}, "mask_color": {"label": "สีหน้ากาก"}, "mask_color_opacity": {"label": "ความทึบของ Mask"}, "parallax_scroll": {"label": "กลิ้งพารัลแลกซ์"}}}, "main-collection-product-list": {"name": "รายการสินค้า", "settings": {"products_per_page": {"label": "จำนวนรายการสินค้าต่อหน้า"}, "columns_desktop": {"label": "จำนวนคอลัมน์บนคอมพิวเตอร์"}, "columns_mobile": {"label": "จำนวนคอลัมน์บนมือถือ", "options__0": {"label": "1 คอลัมน์"}, "options__1": {"label": "2 คอลัมน์"}}, "pagination_style": {"label": "ตัวเปลี่ยนหน้า", "options__0": {"label": "พลิกหน้าไปกลับมา"}, "options__1": {"label": "กระโดดหมายเลขหน้า"}}, "enable_infinite_scroll": {"label": "เปิดใช้งานการเลื่อนไม่สิ้นสุด"}, "enable_infinite_scroll_button": {"label": "แสดงปุ่ม \"ดูเพิ่มเติม\" เมื่อเลื่อนอย่างไม่สิ้นสุด"}, "group_header__0": {"label": "ข้อมูลผลิตภัณฑ์", "info": "คุณสามารถไปที่[การตั้งค่าธีม - การตั้งค่าผลิตภัณฑ์](/editor?locator=settings&category=product) เช่น ฉลากส่วนลด มุมมองด่วน/การซื้อเพิ่มเติม ฯลฯ"}, "price_show_type": {"label": "วิธีการแสดงราคา", "info": "หากสินค้าเป็นสินค้าขนาดเดียว สินค้าจะแสดงในรูปแบบราคาเดียว", "options__0": {"label": "ราคาต่ำสุด"}, "options__1": {"label": "ช่วงราคา"}, "options__2": {"label": "ราคาต่ำสุด"}}, "show_origin_price": {"label": "แสดงราคาเดิมหรือไม่"}, "group_header__1": {"label": "ภาพสินค้า"}, "product_image_ratio": {"label": "อัตราส่วนภาพสินค้า", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "image_fill_type": {"label": "วิธีการเติมภาพสินค้า", "options__0": {"label": "ปรับ"}, "options__1": {"label": "การกรอก"}}, "image_display_area": {"label": "พื้นที่แสดงรูปภาพ", "info": "ปรับพื้นที่แสดงรูปภาพสินค้า", "options__0": {"label": "บนซ้าย"}, "options__1": {"label": "ด้านบนโดยตรง"}, "options__2": {"label": "บนขวา"}, "options__3": {"label": "ด้านซ้าย"}, "options__4": {"label": "กลาง"}, "options__5": {"label": "ด้านขวา"}, "options__6": {"label": "ล่างซ้าย"}, "options__7": {"label": "ด้านล่างโดยตรง"}, "options__8": {"label": "ล่างขวา"}}, "show_secondary_image": {"label": "แสดงรูปภาพสินค้ารูปถัดไปเมื่อวางเมาส์ไว้"}, "mobile_show_secondary_image": {"label": "แสดงภาพผลิตภัณฑ์ถัดไปเมื่อเทอร์มินัลมือถือวางเมาส์เหนือ"}, "sticky_filtering": {"label": "แถบเครื่องมือได้รับการแก้ไขเมื่อเลื่อนหน้า"}, "group_header__2": {"label": "การเรียงลำดับและการกรอง"}, "enable_filtering": {"label": "เปิดใช้งานการกรองแถบด้านข้าง"}, "filter_type": {"label": "เค้าโครงตัวกรอง", "options__0": {"label": "แนวนอน"}, "options__1": {"label": "แนวตั้ง"}, "options__2": {"label": "ลิ้นชัก"}}, "enable_sorting": {"label": "เปิดใช้งานการจัดเรียงสินค้า"}, "group_header__3": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"image": {"name": "รูปภาพ"}, "title": {"name": "หัวข้อ"}, "price": {"name": "ราคา"}, "highlight": {"name": "การอธิบายจุดแด่น", "settings": {"group_header__0": {"label": "ในการใช้คุณลักษณะนี้ สร้างชื่อเนมสเปซ \"highlights\" และคีย์ที่ชื่อว่า \"list\" สำหรับเมตาดาต้าสินค้า โดยเลือกประเภทข้อมูลเป็น \"ข้อความหลายบรรทัด\" หลังจากสร้างแล้ว คุณสามารถเพิ่มค่าให้กับฟิลด์เมตาดาต้าในสินค้าที่เฉพาะเจาะจง และจะแสดงบนหน้าเว็บไซต์"}}}, "text": {"name": "ข้อความ", "settings": {"text": {"label": "ข้อความ"}}}, "divider": {"name": "เส้นแบ่ง"}, "brand": {"name": "ยี่ห้อ"}, "sku": {"name": "sku"}, "quick_add_button": {"name": "ปุ่มซื้อสินค้าด่วน"}}}, "main-company": {"name": "การลงทะเบียนบัญชีบริษัท", "settings": {"padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-forgot-password": {"name": "ลืมรหัสผ่าน", "settings": {"padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-list-collections": {"name": "รายการหมวดหมู่", "settings": {"title": {"label": "หัวข้อ"}, "sort": {"label": "กฎการเรียงลำดับหมวดหมู่:", "options__0": {"label": "เรียงตามตัวอักษร A-Z"}, "options__1": {"label": "เรียงตามตัวอักษร Z-A"}, "options__2": {"label": "วันที่จากใกล้ถึงไกล"}, "options__3": {"label": "วันที่จากไกลถึงใกล้"}, "options__4": {"label": "ปริมาณสินค้าจากมากไปน้อย"}, "options__5": {"label": "ปริมาณสินค้าจากน้อยไปมาก"}}, "collection_image_ratio": {"label": "อัตราส่วนภาพหมวดหมู่", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "collection_fill_type": {"label": "วิธีการเติมภาพ", "options__0": {"label": "ปรับ"}, "options__1": {"label": "การกรอก"}}, "pc_cols": {"label": "จำนวนคอลัมน์บนคอมพิวเตอร์"}, "m_cols": {"label": "จำนวนคอลัมน์บนมือถือ", "options__0": {"label": "1 คอลัมน์"}, "options__1": {"label": "2 คอลัมน์"}}}}, "main-login": {"name": "เข้าสู่ระบบลูกค้า", "settings": {"padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-order-detail": {"name": "รายละเอียดคำสั่งซื้อ", "settings": {"group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-order-list": {"name": "รายการคำสั่งซื้อ", "settings": {"group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-order-tracking": {"name": "ติดตามการสั่งซื้อ", "settings": {"title": {"label": "หัวข้อ"}, "btn_text": {"label": "ข้อความปุ่ม"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-page": {"name": "หน้าเพจที่กำหนดเอง", "settings": {"group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-password-footer": {"name": "ส่วนท้ายของรหัสผ่าน"}, "main-password-header": {"name": "ส่วนหัวของรหัสผ่าน"}, "main-password": {"name": "รหัสผ่าน", "settings": {"group_header__0": {"label": "หัวข้อ"}, "image": {"label": "ภาพพื้นหลัง"}, "image_overlay_opacity": {"label": "ความทึบของภาพซ้อนทับ"}, "show_background_image": {"label": "แสดงภาพพื้นหลัง"}, "image_height": {"label": "ความสูงของแบนเนอร์", "info": "เพื่อผลลัพธ์ที่ดีที่สุด ให้ใช้รูปภาพที่มีอัตราส่วนกว้างยาว 16:9", "options__0": {"label": "ปรับให้เข้ากับภาพ"}, "options__1": {"label": "เล็ก"}, "options__2": {"label": "กลาง"}, "options__3": {"label": "ใหญ่"}}, "desktop_content_position": {"label": "ตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "ด้านซ้าย"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ด้านขวา"}, "options__3": {"label": "บนซ้าย"}, "options__4": {"label": "ล่างซ้าย"}, "options__5": {"label": "ด้านล่างโดยตรง"}, "options__6": {"label": "บนขวา"}, "options__7": {"label": "ล่างขวา"}}, "show_text_box": {"label": "แสดงบล็อกพื้นหลังข้อความบนพีซี"}, "desktop_content_alignment": {"label": "การจัดตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__1": {"label": "เลย์เอาต์บนมือถือ"}, "mobile_content_alignment": {"label": "การจัดตำแหน่งเนื้อหาบนมือถือ", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "show_text_below": {"label": "แสดงเนื้อหาด้านล่างของรูปภาพบนมือถือ"}}, "blocks": {"heading": {"name": "หัวข้อ", "settings": {"heading": {"label": "หัวข้อ"}, "heading_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}}}, "paragraph": {"name": "อธิบาย", "settings": {"text": {"label": "อธิบาย"}}}, "email_form": {"name": "สมัครสมาชิกอีเมล"}}}, "main-product": {"name": "รายละเอียดสินค้า", "settings": {"product_info_sticky": {"label": "การแสดงข้อมูลสินค้าบนคอมพิวเตอร์"}, "product_image_pc_show_style": {"label": "เลย์เอาต์บนคอมพิวเตอร์", "options__0": {"label": "เรียงต่อกัน"}, "options__1": {"label": "สองคอลัมน์"}, "options__2": {"label": "ภาพขนาดย่อเรียงต่อกัน"}, "options__3": {"label": "การหมุนภาพขนาดย่อ"}}, "product_image_size": {"label": "ขนาดสื่อพีซี", "info": "ไฟล์มีเดียจะปรับให้เหมาะกับอุปกรณ์มือถือโดยอัตโนมัติ", "options__0": {"label": "ใหญ่"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "เล็ก"}}, "product_image_fill_type": {"label": "วิธีการเติมรูปภาพบน PC", "options__0": {"label": "ปรับ"}, "options__1": {"label": "การกรอก"}}, "product_image_ratio": {"label": "อัตราส่วนภาพบน PC", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "ตามมาภาพแรก"}, "options__2": {"label": "1:1"}, "options__4": {"label": "4:3"}}, "image_quality": {"label": "อัตราการบีบอัดภาพ", "info": "กำหนดค่าอัตราส่วนของการบีบอัดของรูปภาพหลักและรูปภาพขนาดย่อ แนะนำให้เลือกอัตราส่วนการบีบอัดที่ 80% หรือต่ำกว่า เพื่อให้มั่นใจถึงประสิทธิภาพของร้านค้าและประสบการณ์ผู้ใช้", "options__0": {"label": "อัตราส่วนของภาพต้นฉบับ"}, "options__1": {"label": "90%"}, "options__2": {"label": "80%"}, "options__3": {"label": "70%"}}, "product_image_pc_thumbnail_postion": {"label": "ตำแหน่งภาพขนาดย่อบนคอมพิวเตอร์", "options__0": {"label": "ข้างภาพสินค้า"}, "options__1": {"label": "ใต้ภาพสินค้า"}}, "product_thumbnail_image_size": {"label": "ขนาดภาพขนาดย่อของคอมพิวเตอร์", "options__0": {"label": "ใหญ่"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "เล็ก"}}, "video_loop": {"label": "เปิดใช้งานวิดีโอวนซ้ำ"}, "video_autoplay": {"label": "เล่นวิดีโออัตโนมัติ", "info": "เนื่องจากข้อจำกัดด้านฟังก์ชันบางอย่างของเบราว์เซอร์ วิดีโออาจเล่นไม่ได้โดยอัตโนมัติ"}, "youtube_simple_style": {"label": "เครื่องเล่น Youtube สไตล์มินิมอล "}, "pc_magnifier_type": {"label": "แว่นขยายบนคอมพิวเตอร์", "options__0": {"label": "คลิกเพื่อดูภาพขยาย"}, "options__1": {"label": "ซูมแบบลอยตัว"}}, "magnifier_interactive_type": {"label": "โหมดขยายภาพหลัก", "options__0": {"label": "โหมด 1"}, "options__1": {"label": "โหมด 2"}}, "default_selected_variant": {"label": "SKU ถูกเลือกโดยค่าเริ่มต้น"}, "hide_variants": {"label": "ซ่อนรูปภาพของ SKU อื่นๆ หลังจากเลือก SKU"}, "group_header__0": {"label": "บนมือถือ"}, "product_mobile_thumbnail_image_hide": {"label": "เลย์เอาต์บนมือถือ", "options__0": {"label": "สองคอลัมน์"}, "options__1": {"label": "ซ่อนภาพขนาดย่อ"}, "options__2": {"label": "แสดงภาพขนาดย่อ"}}, "product_mobile_image_fill_type": {"label": "วิธีเติมภาพบนมือถือ", "options__0": {"label": "ปรับ"}, "options__1": {"label": "การกรอก"}}, "product_mobile_image_ratio": {"label": "อัตราส่วนภาพบนมือถือ", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "ตามมาภาพแรก"}, "options__2": {"label": "1:1"}, "options__4": {"label": "4:3"}}, "product_mobile_thumbnail_image_size": {"label": "ขนาดภาพขนาดย่อบนมือถือ", "options__0": {"label": "ใหญ่"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "เล็ก"}}, "group_header__1": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"variant_sku": {"name": "หมายเลขสินค้า SKU"}, "title": {"name": "หัวข้อ"}, "dividing_line": {"name": "เส้นแบ่ง", "settings": {"show_pc_line": {"label": "แสดงผลบนคอมพิวเตอร์"}, "show_mobile_line": {"label": "แสดงผลบนมือถือ"}, "dividing_line_color": {"label": "สีของเส้นแบ่ง"}, "desktop_dividing_line_height": {"label": "ความหนาของเส้นแบ่งด้านคอมพิวเตอร์"}, "dividing_line_style": {"label": "รูปแบบเส้นแบ่งบนมือถือ", "options__0": {"label": "การแบ่งด้วยพื้นที่ว่างเปล่า"}, "options__1": {"label": "การแบ่งด้วยทั้งคอลัมน์"}}, "dividing_line_height": {"label": "ความหนาของเส้นแบ่งบนมือถือ"}}}, "price": {"name": "ราคา", "settings": {"show_order": {"label": "วิธีการแสดงราคา/ส่วนลด", "options__0": {"label": "ราคาขาย ราคาเดิม"}, "options__1": {"label": "ราคาเดิม ราคาขาย"}, "options__2": {"label": "ฉลากส่วนลด ราคาขาย ราคาเดิม"}, "options__3": {"label": "ราคาขาย ราคาเดิม ฉลากส่วนลด"}, "options__4": {"label": "<PERSON><PERSON><PERSON>, nh<PERSON>n <PERSON>u đãi"}, "options__5": {"label": "<PERSON><PERSON><PERSON><PERSON> đãi, g<PERSON><PERSON> b<PERSON>"}}, "sale_font_size": {"label": "ขนาดตัวอักษรราคาขาย", "options__0": {"label": "เล็ก"}, "options__1": {"label": "ปานกลาง"}, "options__2": {"label": "ใหญ่"}, "options__3": {"label": "ใหญ่มาก"}}, "regular_font_size": {"label": "ราคาเดิม (ราคาที่ขีดเส้นใต้) ขนาดตัวอักษร", "options__0": {"label": "เล็ก"}, "options__1": {"label": "ปานกลาง"}, "options__2": {"label": "ใหญ่"}, "options__3": {"label": "ใหญ่มาก"}}, "save_font_size": {"label": "<PERSON><PERSON><PERSON> th<PERSON><PERSON><PERSON> phông chữ nhãn ưu đãi", "options__0": {"label": "เล็ก"}, "options__1": {"label": "ปานกลาง"}, "options__2": {"label": "ใหญ่"}, "options__3": {"label": "ใหญ่มาก"}}, "discount_style": {"label": "รูปแบบส่วนลด", "options__0": {"label": "อัตราส่วนส่วนลด"}, "options__1": {"label": "ราคาที่ลด"}}, "save_style": {"label": "เสนอรูปแบบฉลาก", "info": "สามารถไปที่ [การตั้งค่าธีม-สี](/editor?locator=settings&category=color) เพื่อปรับค่าสีของป้ายกำกับส่วนลด", "options__0": {"label": "สไตล์ปุ่ม"}, "options__1": {"label": "สไตล์ข้อความ"}}, "font_size_flexible": {"label": "การกำหนดค่าขนาดตัวอักษรที่ยืดหยุ่น", "info": "หลังจากเปิดใช้งานแล้ว คุณสามารถระบุขนาดตัวอักษรได้ด้วยตัวเอง"}, "sale_price_pc_font_size": {"label": "ขนาดตัวอักษรราคาขายบนคอมพิวเตอร์"}, "sale_price_mobile_font_size": {"label": "ขนาดตัวอักษรราคาขายบนบนโทรศัพท์มือถือ"}, "regular_price_pc_font_size": {"label": "ขนาดตัวอักษรของราคาเดิม (ราคาที่ขีดเส้นกลาง) ทางด้านคอมพิวเตอร์"}, "regular_price_mobile_font_size": {"label": "ขนาดตัวอักษรของราคาเดิม (ราคาที่ขีดเส้นกลาง)) บนเทรศัพท์มือถือ"}, "save_price_pc_font_size": {"label": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> phông chữ nhãn ưu đãi thiết bị đầu cuối máy tính"}, "save_price_mobile_font_size": {"label": "<PERSON><PERSON><PERSON> thư<PERSON><PERSON> phông chữ nhãn ưu đãi thiết bị đầu cuối di động"}, "sale_font_bold": {"label": "ราคาขายเป็นตัวหนา"}}}, "variant_picker": {"name": "ตัวเลือกคุณลักษณะหลายรายการ", "settings": {"picker_type": {"label": "จอแสดงผลหลายคุณลักษณะ", "options__0": {"label": "เรียงต่อกัน"}, "options__1": {"label": "กล่องลากลง"}}, "sizes": {"label": "แสดงขนาด", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}, "layout_direction": {"label": "แสดงเลเอาท์", "options__0": {"label": "เลย์เอาต์ด้านบนและด้านล่าง"}, "options__1": {"label": "เลย์เอาต์ซ้ายและขวา"}}, "enabled_color_swatch": {"label": "เปิดใช้งานแถบสี", "info": "เมื่อเปิดใช้งานตัวอย่างสี ตัวเลือกจะแสดงในไทล์ตามค่าเริ่มต้น [คลิกเพื่อเรียนรู้](https://help.shopline.com/hc/en-001/articles/18291247740825-Quick-Add-to-Cart-of-Palette-Feature#h_01J0X1YZHS09MJ94XAYHX515ZV) ตารางเปรียบเทียบตัวอย่างสี "}, "color_swatch_type": {"label": "รูปแบบแถบสี", "options__0": {"label": "สี่เหลี่ยม"}, "options__1": {"label": "ทรงกลม"}, "options__2": {"label": "สี่เหลี่ยมผืนผ้าโค้งมน"}}}}, "quantity_selector": {"name": "ตัวเลือกจำนวน", "settings": {"width": {"label": "แสดงความกว้าง", "options__0": {"label": "ความกว้างคอลัมน์ 1/2"}, "options__1": {"label": "ความกว้างเต็มคอลัมน์"}}, "layout_direction": {"label": "แสดงเลเอาท์", "options__0": {"label": "เลย์เอาต์ด้านบนและด้านล่าง"}, "options__1": {"label": "เลย์เอาต์ซ้ายและขวา"}}, "border_style": {"label": "แสดงรูปแบบ", "options__0": {"label": "รูปแบบเส้น"}, "options__1": {"label": "รูปแบบเส้นขอบ"}, "options__2": {"label": "รูปแบบ์ไร้กรอบ"}}}}, "inventory": {"name": "สถานะสต็อค", "settings": {"inventory_threshold": {"label": "เกณฑ์สินค้าสต็อคต่ำ", "info": "เลือก 0 เพื่อแสดง \"มีของ\" เสมอ"}, "show_inventory_quantity": {"label": "แสดงจำนวนสต็อค"}}}, "buy_buttons": {"name": "ปุ่มซื้อ", "settings": {"button_layout": {"label": "Lokasi butang beli", "options__0": {"label": "Halaman terbina dalam"}, "options__1": {"label": "<PERSON><PERSON><PERSON> hisap bawah"}, "options__2": {"label": "Halaman terbina dalam ditambah terapung"}}}}, "description": {"name": "รายละเอียดสินค้า", "settings": {"location": {"label": "ตำแหน่งแสดงรายละเอียดสินค้า", "options__0": {"label": "ด้านขวาของภาพสินค้า"}, "options__1": {"label": "ใต้ภาพสินค้า"}}, "is_fold": {"label": "เก็บรายละเอียดสินค้า"}}}, "description_accordion": {"name": "รายละเอียดสินค้า(หีบเพลง)", "settings": {"title": {"label": "หัวข้อ"}, "fold": {"label": "แสดงแบบเก็บ"}}}, "share": {"name": "แบ่งปัน", "settings": {"group_header__0": {"label": "สามารถไปที่ [การตั้งค่าธีม-โซเชียลมีเดีย](/editor?locator=settings&category=media_social) เพื่อเพิ่มที่อยู่ลิงก์ที่เกี่ยวข้อง"}}}, "product_additional": {"name": "เนื้อหาที่กำหนดเอง", "settings": {"title": {"label": "หัวข้อ"}, "icon": {"label": "ไอคอน", "options__0": {"label": "ไม่แสดง"}, "options__1": {"label": "การชำระเงินที่ปลอดภัย"}, "options__2": {"label": "พัสดุ"}, "options__3": {"label": "อีเมล"}, "options__4": {"label": "ตำแหน่ง"}, "options__5": {"label": "ลูกค้า"}, "options__6": {"label": "สนทนา"}, "options__7": {"label": "ของขวัญ"}, "options__8": {"label": "โทรศัพท์มือถือ"}, "options__9": {"label": "ข้อมูลช่วยเหลือ"}, "options__10": {"label": "โลจิสติกส์"}, "options__11": {"label": "ป้ายส่วนลด"}}, "description": {"label": "คำอธิบาย"}, "custom_page": {"label": "หน้าเพจที่กำหนดเอง"}}}, "html": {"name": "HTML ที่กำหนดเอง", "settings": {"html": {"label": "HTML"}}}, "icon": {"name": "ประกาศไอคอน", "settings": {"icon1": {"label": "ไอคอน 1", "options__0": {"label": "ไม่แสดง"}, "options__1": {"label": "การชำระเงินที่ปลอดภัย"}, "options__2": {"label": "พัสดุ"}, "options__3": {"label": "อีเมล"}, "options__4": {"label": "ตำแหน่ง"}, "options__5": {"label": "ลูกค้า"}, "options__6": {"label": "สนทนา"}, "options__7": {"label": "ของขวัญ"}, "options__8": {"label": "โทรศัพท์มือถือ"}, "options__9": {"label": "เครื่องหมายคำถาม"}, "options__10": {"label": "โลจิสติกส์"}, "options__11": {"label": "ป้ายส่วนลด"}, "options__12": {"label": "เครื่องอิสริยาภรณ์"}, "options__13": {"label": "การคุ้มครองสิ่งแวดล้อม"}}, "image1": {"label": "รูปที่ 1"}, "title1": {"label": "หัวข้อ 1"}, "sub_title1": {"label": "หัวข้อรอง 1"}, "icon2": {"label": "ไอคอน 2", "options__0": {"label": "ไม่แสดง"}, "options__1": {"label": "การชำระเงินที่ปลอดภัย"}, "options__2": {"label": "พัสดุ"}, "options__3": {"label": "อีเมล"}, "options__4": {"label": "ตำแหน่ง"}, "options__5": {"label": "ลูกค้า"}, "options__6": {"label": "สนทนา"}, "options__7": {"label": "ของขวัญ"}, "options__8": {"label": "โทรศัพท์มือถือ"}, "options__9": {"label": "เครื่องหมายคำถาม"}, "options__10": {"label": "โลจิสติกส์"}, "options__11": {"label": "ป้ายส่วนลด"}, "options__12": {"label": "เครื่องอิสริยาภรณ์"}, "options__13": {"label": "การคุ้มครองสิ่งแวดล้อม"}}, "image2": {"label": "รูปที่ 2"}, "title2": {"label": "หัวข้อ 2"}, "sub_title2": {"label": "หัวข้อรอง 2"}, "icon3": {"label": "ไอคอน 3", "options__0": {"label": "ไม่แสดง"}, "options__1": {"label": "การชำระเงินที่ปลอดภัย"}, "options__2": {"label": "พัสดุ"}, "options__3": {"label": "อีเมล"}, "options__4": {"label": "ตำแหน่ง"}, "options__5": {"label": "ลูกค้า"}, "options__6": {"label": "สนทนา"}, "options__7": {"label": "ของขวัญ"}, "options__8": {"label": "โทรศัพท์มือถือ"}, "options__9": {"label": "เครื่องหมายคำถาม"}, "options__10": {"label": "โลจิสติกส์"}, "options__11": {"label": "ป้ายส่วนลด"}, "options__12": {"label": "เครื่องอิสริยาภรณ์"}, "options__13": {"label": "การคุ้มครองสิ่งแวดล้อม"}}, "image3": {"label": "ภาพ 3"}, "title3": {"label": "หัวข้อ 3"}, "sub_title3": {"label": "หัวข้อรอง 3"}}}, "highlight": {"name": "การอธิบายจุดแด่น", "settings": {"group_header__0": {"label": "ในการใช้คุณลักษณะนี้ สร้างชื่อเนมสเปซ \"highlights\" และคีย์ที่ชื่อว่า \"list\" สำหรับเมตาดาต้าสินค้า โดยเลือกประเภทข้อมูลเป็น \"ข้อความหลายบรรทัด\" หลังจากสร้างแล้ว คุณสามารถเพิ่มค่าให้กับฟิลด์เมตาดาต้าในสินค้าที่เฉพาะเจาะจง และจะแสดงบนหน้าเว็บไซต์"}}}, "text": {"name": "ข้อความ", "settings": {"text": {"label": "ข้อความ"}, "text_style": {"label": "สไตล์ข้อความ", "options__0": {"label": "ข้อความ"}, "options__1": {"label": "หัวข้อรอง"}, "options__2": {"label": "ตัวพิมพ์ใหญ่"}}}}}}, "main-register": {"name": "การลงทะเบียนลูกค้า", "settings": {"padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}}, "main-search": {"name": "ผลการค้นหา", "settings": {"products_per_page": {"label": "จำนวนรายการสินค้าต่อหน้า"}, "columns_desktop": {"label": "จำนวนคอลัมน์บนคอมพิวเตอร์"}, "columns_mobile": {"label": "จำนวนคอลัมน์บนมือถือ", "options__0": {"label": "1 คอลัมน์"}, "options__1": {"label": "2 คอลัมน์"}}, "group_header__0": {"label": "ภาพสินค้า"}, "product_image_ratio": {"label": "อัตราส่วนภาพสินค้า", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "image_fill_type": {"label": "วิธีการเติมภาพสินค้า", "options__0": {"label": "ปรับ"}, "options__1": {"label": "การกรอก"}}, "show_secondary_image": {"label": "แสดงรูปภาพสินค้ารูปถัดไปเมื่อวางเมาส์ไว้"}, "group_header__1": {"label": "การเรียงลำดับและการกรอง"}, "enable_filtering": {"label": "เปิดใช้งานการกรองแถบด้านข้าง"}, "filter_type": {"label": "เค้าโครงตัวกรอง", "options__0": {"label": "แนวนอน"}, "options__1": {"label": "แนวตั้ง"}, "options__2": {"label": "ลิ้นชัก"}}, "enable_sorting": {"label": "เปิดใช้งานการจัดเรียงสินค้า"}, "group_header__2": {"label": "โพสต์บล็อก"}, "show_article_author": {"label": "แสดงผู้เขียน"}, "show_article_date": {"label": "แสดงวันที่"}, "group_header__3": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"image": {"name": "รูปภาพ"}, "title": {"name": "หัวข้อ"}, "price": {"name": "ราคา"}, "highlight": {"name": "การอธิบายจุดแด่น"}, "text": {"name": "ข้อความ", "settings": {"text": {"label": "ข้อความ"}}}, "divider": {"name": "เส้นแบ่ง"}, "brand": {"name": "ยี่ห้อ"}, "sku": {"name": "sku"}, "quick_add_button": {"name": "ปุ่มซื้อสินค้าด่วน"}}}, "map": {"name": "แผนที่", "settings": {"title": {"label": "หัวข้อ"}, "store_info": {"label": "ที่อยู่และเวลาทำการ"}, "address": {"label": "ที่อยู่แผนที่", "info": "คลิกปุ่มเพื่อข้ามไปยังที่อยู่ Google Map ที่เกี่ยวข้อง"}, "btn_style": {"label": "สไตล์ปุ่ม", "options__0": {"label": "ปุ่มหลัก"}, "options__1": {"label": "ปุ่มรอง"}}, "btn_text": {"label": "ข้อความปุ่ม"}, "pushpin": {"label": "แสดงหมุด"}, "group_header__0": {"label": "พื้นหลัง"}, "bg_color": {"label": "สีพื้นหลัง"}, "google_api_secret_key": {"label": "คีย์ API ของ Google Maps", "info": "หลังจากป้อนคีย์ API ของ Google Maps แล้ว สามารถแสดงแผนที่ได้ [ดูวิธีรับ](https://shoplineapphelp.zendesk.com/hc/articles/4411546876313-%E5%A6%82%E4%BD%95%E6%B3%A8%E5%86%8C-Google-Maps-API-%E5%AF%86%E9%92%A5)"}, "image": {"label": "รูปภาพ", "info": "รูปภาพจะปรากฏขึ้นเมื่อไม่ได้กำหนดค่า Google Maps"}, "image_position": {"label": "พื้นที่แสดงรูปภาพ", "options__0": {"label": "บนซ้าย"}, "options__1": {"label": "ด้านบนโดยตรง"}, "options__2": {"label": "บนขวา"}, "options__3": {"label": "ด้านซ้าย"}, "options__4": {"label": "กลาง"}, "options__5": {"label": "ด้านขวา"}, "options__6": {"label": "ล่างซ้าย"}, "options__7": {"label": "ด้านล่างโดยตรง"}, "options__8": {"label": "ล่างขวา"}}}, "presets": {"presets__0": {"category": "ส่วนประกอบที่ไว้วางใจ", "name": "แผนที่"}}}, "multi-media-splicing": {"name": "ตัดต่อสื่อ", "settings": {"title": {"label": "หัวข้อ"}, "title_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "ใหญ่"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "เล็ก"}}, "desktop_layout": {"label": "เลย์เอาต์บนคอมพิวเตอร์", "options__0": {"label": "ซ้ายใหญ่ขวาเล็ก"}, "options__1": {"label": "ซ้ายเล็กขวาใหญ่"}}, "mobile_layout": {"label": "เลย์เอาต์บนมือถือ", "options__0": {"label": "ตัดต่อ"}, "options__1": {"label": "รายการ"}}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"video": {"name": "วิดีโอ", "settings": {"cover_image": {"label": "ภาพหน้าปก"}, "internal_video": {"label": "วิดีโอในเครื่อง"}, "external_video": {"label": "ลิงค์วิดีโอภายนอก", "info": "ป้อนลิงก์ YouTube หรือ Vimeo แบบฝัง"}, "video_alt": {"label": "วิดีโอAltข้อความ"}, "image_padding": {"label": "วิธีการเติมภาพ", "info": "รูปภาพที่กำหนดให้ \"พอดี\" จะแสดงเมื่ออยู่ในคอนเทนเนอร์ขนาดใหญ่", "options__0": {"label": "การกรอก"}, "options__1": {"label": "ปรับ"}}}}, "product": {"name": "สินค้า", "settings": {"product": {"label": "สินค้า"}, "product_hover_show_next": {"label": "แสดงรูปภาพสินค้ารูปถัดไปเมื่อวางเมาส์ไว้"}, "image_padding": {"label": "วิธีการเติมภาพ", "info": "รูปภาพที่กำหนดให้ \"พอดี\" จะแสดงเมื่ออยู่ในคอนเทนเนอร์ขนาดใหญ่", "options__0": {"label": "การกรอก"}, "options__1": {"label": "ปรับ"}}}}, "collection": {"name": "หมวดหมู่", "settings": {"category": {"label": "หมวดหมู่"}, "image_padding": {"label": "วิธีการเติมภาพ", "info": "รูปภาพที่กำหนดให้ \"พอดี\" จะแสดงเมื่ออยู่ในคอนเทนเนอร์ขนาดใหญ่", "options__0": {"label": "การกรอก"}, "options__1": {"label": "ปรับ"}}}}, "image": {"name": "รูปภาพ", "settings": {"image": {"label": "รูปภาพ"}, "image_padding": {"label": "วิธีการเติมภาพ", "info": "รูปภาพที่กำหนดให้ \"พอดี\" จะแสดงเมื่ออยู่ในคอนเทนเนอร์ขนาดใหญ่", "options__0": {"label": "การกรอก"}, "options__1": {"label": "ปรับ"}}, "jump_link": {"label": "ลิงก์กระโดดข้าม", "info": "หลังจากตั้งค่าลิงค์แล้วคลิกที่ภาพเพื่อข้ามไป"}}}}, "presets": {"presets__0": {"category": "การแสดงกราฟิก", "name": "ตัดต่อสื่อ"}}}, "picture-floating": {"name": "เปลี่ยนรูปภาพแบบลอยตัว", "settings": {"title": {"label": "หัวข้อ"}, "title_font": {"label": "แบบอักษรชื่อเรื่อง"}, "title_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}, "title_split": {"label": "การแสดงโมเสกหัวข้อเรื่อง"}, "description": {"label": "อธิบาย"}, "image_height": {"label": "ความสูงของรูปภาพ"}, "mobile_slide_duration": {"label": "เปลี่ยนเวลาอัตโนมัติบนมือถือ", "unit": "วินาที"}, "is_fullscreen": {"label": "การแสดงผลแบบเต็มหน้าจอ"}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"image": {"name": "รูปภาพ", "settings": {"image": {"label": "รูปภาพ", "info": "ความกว้างขั้นต่ำที่แนะนำคือ 820px และสามารถปรับความสูงได้"}, "link": {"label": "ลิงก์กระโดดข้าม"}}}}, "presets": {"presets__0": {"category": "การแสดงกราฟิก", "name": "เปลี่ยนรูปภาพแบบลอยตัว"}}}, "product-recommendations": {"name": "แนะนำผลิตภัณฑ์", "settings": {"title": {"label": "หัวข้อ"}, "title_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "ใหญ่"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "เล็ก"}}, "products_to_show": {"label": "จำนวนสินค้าที่แสดง"}, "pc_cols": {"label": "จำนวนคอลัมน์บนคอมพิวเตอร์"}, "mobile_cols": {"label": "จำนวนคอลัมน์บนมือถือ", "options__0": {"label": "1 คอลัมน์"}, "options__1": {"label": "2 คอลัมน์"}}, "enable_horizontal_slider": {"label": "เปิดใช้งานการปัดไปทางซ้ายและขวา", "info": "มีผลเมื่อจำนวนสินค้าที่แสดงเกินจำนวนคอลัมน์"}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__0": {"label": "ภาพสินค้า"}, "product_image_ratio": {"label": "อัตราส่วนภาพสินค้า", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "product_fill_type": {"label": "วิธีการเติมภาพสินค้า", "options__0": {"label": "ปรับ"}, "options__1": {"label": "การกรอก"}}, "show_secondary_image": {"label": "แสดงรูปภาพสินค้ารูปถัดไปเมื่อวางเมาส์ไว้"}, "group_header__1": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"image": {"name": "รูปภาพ"}, "title": {"name": "หัวข้อ"}, "price": {"name": "ราคา"}, "highlight": {"name": "การอธิบายจุดแด่น"}, "text": {"name": "ข้อความ", "settings": {"text": {"label": "ข้อความ"}}}, "divider": {"name": "เส้นแบ่ง"}, "brand": {"name": "ยี่ห้อ"}, "sku": {"name": "sku"}, "quick_add_button": {"name": "ปุ่มซื้อสินค้าด่วน"}}}, "promotional-banner": {"name": "แบนเนอร์โปรโมท", "settings": {"background_image": {"label": "ภาพพื้นหลัง"}, "background_image_width": {"label": "ความกว้างของภาพพื้นหลัง", "options__0": {"label": "1/1"}, "options__1": {"label": "1/2"}, "options__2": {"label": "2/3"}}, "background_color": {"label": "สีพื้นหลัง"}, "front_image": {"label": "ภาพโปรโมท"}, "front_image_width": {"label": "ความกว้างของภาพที่ส่งเสริม"}, "front_layout": {"label": "ตำแหน่งภาพ", "options__0": {"label": "ข้อความซ้ายและภาพขวา"}, "options__1": {"label": "ภาพซ้ายและข้อความขวา"}}, "front_card_mode": {"label": "แสดงบล็อกสีข้อความ"}, "front_card_border_radius": {"label": "รัศมีมุมการ์ด"}, "front_card_background_color": {"label": "สีพื้นหลังการ์ด"}, "front_card_text_color": {"label": "สีข้อความของการ์ด"}, "group_header__0": {"label": "ข้อความ"}, "sub_title": {"label": "หัวข้อรอง"}, "title": {"label": "หัวข้อ"}, "description": {"label": "อธิบาย"}, "btn_text": {"label": "ข้อความปุ่ม"}, "btn_link": {"label": "ลิงก์กระโดดข้าม"}, "button_style": {"label": "สไตล์ปุ่ม", "options__0": {"label": "ปุ่มหลัก"}, "options__1": {"label": "ปุ่มเค้าร่าง"}, "options__2": {"label": "ขีดเส้นใต้"}}, "text_align": {"label": "จัดแนวข้อความให้ตรงกัน", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "text_color": {"label": "สีของข้อความ"}}, "presets": {"presets__0": {"category": "การแสดงกราฟิก", "name": "แบนเนอร์โปรโมท"}}}, "promotional-image": {"name": "ภาพโปรโมท", "settings": {"title": {"label": "หัวข้อ"}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"image": {"name": "รูปภาพ", "settings": {"image": {"label": "ภาพโปรโมท"}, "link": {"label": "ลิงก์กระโดดข้าม"}, "text": {"label": "ข้อความส่งเสริมการขาย"}, "show_text_background": {"label": "แสดงบล็อกสีข้อความ"}, "text_border_radius": {"label": "รัศมีมุมของบล็อกสีข้อความ"}, "text_background_color": {"label": "สีบล็อกสีข้อความ"}, "text_color": {"label": "สีตัวอักษรของข้อความ"}, "text_position": {"label": "ตำแหน่งข้อความ", "options__0": {"label": "ข้อความชิดซ้าย"}, "options__1": {"label": "ข้อความชิดขวา"}}, "icon": {"label": "ไอคอน", "options__0": {"label": "ไม่แสดง"}, "options__1": {"label": "การชำระเงินที่ปลอดภัย"}, "options__2": {"label": "พัสดุ"}, "options__3": {"label": "อีเมล"}, "options__4": {"label": "ตำแหน่ง"}, "options__5": {"label": "ลูกค้า"}, "options__6": {"label": "สนทนา"}, "options__7": {"label": "ของขวัญ"}, "options__8": {"label": "โทรศัพท์มือถือ"}, "options__9": {"label": "เครื่องหมายคำถาม"}, "options__10": {"label": "โลจิสติกส์"}, "options__11": {"label": "ป้ายส่วนลด"}}, "custom_icon": {"label": "ไอคอนที่กำหนดเอง"}}}}, "presets": {"presets__0": {"category": "การแสดงกราฟิก", "name": "ภาพโปรโมท"}}}, "rich-text": {"name": "เต็มไปด้วยข้อความ", "settings": {"desktop_content_position": {"label": "ตำแหน่งเนื้อหาบนคอมพิวเตอร์", "info": "ตำแหน่งได้รับการปรับให้เหมาะกับมือถือโดยอัตโนมัติ", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "normal_width": {"label": "ใช้ความกว้างคอลัมน์มาตรฐาน"}, "show_decoration": {"label": "แสดงเส้นทริม"}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"heading": {"name": "หัวข้อ", "settings": {"heading": {"label": "หัวข้อ"}, "heading_size": {"label": "ขนาดข้อความชื่อเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}}}, "text": {"name": "อธิบาย", "settings": {"text": {"label": "ข้อความ"}}}, "button": {"name": "ปุ่ม", "settings": {"button_text": {"label": "ข้อความปุ่ม"}, "button_link": {"label": "ลิงก์กระโดดข้าม"}, "button_style_secondary": {"label": "ใช้รูปแบบเส้นขอบปุ่ม"}, "button_text_2": {"label": "เนื้อหาปุ่ม2"}, "button_link_2": {"label": "ข้ามลิงก์2"}, "button_style_secondary_2": {"label": "ใช้รูปแบบเส้นขอบปุ่ม"}}}}, "presets": {"presets__0": {"category": "การแสดงกราฟิก", "name": "เต็มไปด้วยข้อความ"}}}, "shoppable-image": {"name": "รูปภาพช้อปปิ้ง", "settings": {"image_pc": {"label": "รูปภาพบนคอมพิวเตอร์", "info": "ขนาดที่แนะนำ 1680 x 700 px"}, "image_mobile": {"label": "รูปภาพมือถือ", "info": "ขนาดที่แนะนำคือ 750 x 800 px หากไม่ได้อัปโหลดรูปภาพจากมือถือ ระบบจะใช้รูปภาพจาก PC เป็นค่าเริ่มต้น"}, "image_full": {"label": "แสดงภาพแบบเต็มหน้าจอ"}, "group_header__0": {"label": "ข้อความ"}, "text_title": {"label": "หัวข้อ"}, "description": {"label": "อธิบาย"}, "button_text": {"label": "ข้อความปุ่ม"}, "jump_link": {"label": "ลิงก์กระโดดข้าม"}, "text_align": {"label": "จัดแนวข้อความให้ตรงกัน", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "text_position": {"label": "ตำแหน่งข้อความ", "options__0": {"label": "บนซ้าย"}, "options__1": {"label": "ด้านบนโดยตรง"}, "options__2": {"label": "บนขวา"}, "options__3": {"label": "ด้านซ้าย"}, "options__4": {"label": "กลาง"}, "options__5": {"label": "ด้านขวา"}, "options__6": {"label": "ล่างซ้าย"}, "options__7": {"label": "ด้านล่างโดยตรง"}, "options__8": {"label": "ล่างขวา"}}, "text_color": {"label": "สีของข้อความ"}, "button_text_color": {"label": "สีข้อความของปุ่ม"}}, "blocks": {"product": {"name": "สินค้า", "settings": {"product": {"label": "สินค้า"}, "horizontal_axis_position_pc": {"label": "ตำแหน่งแกนแนวนอนของส่วนท้ายบน PC"}, "vertical_axis_position_pc": {"label": "ตำแหน่งแกนแนวตั้งของส่วนท้ายบน PC"}, "horizontal_axis_position_mobile": {"label": "ตำแหน่งแกนแนวนอนของส่วนท้ายบนมือถือ"}, "vertical_axis_position_mobile": {"label": "ตำแหน่งแกนแนวตั้งของส่วนท้ายบนมือถือ"}}}, "text": {"name": "การแสดงข้อความ", "settings": {"title": {"label": "หัวข้อ"}, "desc": {"label": "อธิบาย"}, "button_text": {"label": "ข้อความปุ่ม"}, "button_href": {"label": "ลิงก์กระโดดข้าม"}, "horizontal_axis_position_pc": {"label": "ตำแหน่งแกนแนวนอนของส่วนท้ายบน PC"}, "vertical_axis_position_pc": {"label": "ตำแหน่งแกนแนวตั้งของส่วนท้ายบน PC"}, "horizontal_axis_position_mobile": {"label": "ตำแหน่งแกนแนวนอนของส่วนท้ายบนมือถือ"}, "vertical_axis_position_mobile": {"label": "ตำแหน่งแกนแนวตั้งของส่วนท้ายบนมือถือ"}}}}, "presets": {"presets__0": {"category": "โชว์สินค้า", "name": "รูปภาพช้อปปิ้ง"}}}, "sign-up-and-save": {"name": "การสมัครอีเมล", "settings": {"color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "full_screen_width": {"label": "ความกว้างเต็มหน้าจอ"}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"title": {"name": "หัวข้อการสมัครสมาชิก", "settings": {"title": {"label": "หัวข้อการสมัครสมาชิก"}, "fontSize": {"label": "ขนาดของตัวอักษร", "options__0": {"label": "ใหญ่"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "เล็ก"}}}}, "desc": {"name": "คำอธิบายการสมัครสมาชิก", "settings": {"description": {"label": "คำอธิบายการสมัครสมาชิก"}}}, "button": {"name": "กล่องใส่ข้อมูลการสมัครสมาชิก", "settings": {"button_text": {"label": "ข้อความปุ่ม"}, "placeholder": {"label": "ข้อความแจ้งเตือนสำหรับกล่องใส่ข้อความ"}}}}, "presets": {"presets__0": {"category": "การดำเนินงานของลูกค้า", "name": "สมัครสมาชิกอีเมล"}}}, "slideshow": {"name": "ภาพหมุน", "block_info": "[ดูขนาดภาพเทมเพลตที่แนะนำ](https://shoplineapphelp.zendesk.com/hc/articles/4406422263577)", "settings": {"section_height": {"label": "ความสูงบนคอมพิวเตอร์", "options__0": {"label": "ปรับให้เข้ากับขนาดภาพแรก"}, "options__1": {"label": "450px"}, "options__2": {"label": "550px"}, "options__3": {"label": "650px"}, "options__4": {"label": "750px"}, "options__5": {"label": "เต็มจอ"}}, "mobile_height": {"label": "ความสูงบนมือถือ", "options__0": {"label": "ปรับให้เข้ากับขนาดภาพแรก"}, "options__1": {"label": "250px"}, "options__2": {"label": "300px"}, "options__3": {"label": "400px"}, "options__4": {"label": "500px"}, "options__5": {"label": "เต็มจอ"}}, "full_screen": {"label": "แสดงแบบเต็มหน้าจอ"}, "style": {"label": "รูปแบบการเปลี่ยนหน้า", "options__0": {"label": "ลูกศร"}, "options__1": {"label": "แถบความคืบหน้า"}, "options__2": {"label": "จุดเล็ก"}}, "autoplay": {"label": "เปิดใช้งานการสลับอัตโนมัติ"}, "autoplay_speed": {"label": "เปลี่ยนสไลด์ทุกๆ", "unit": "วินาที"}, "mobile_content_layout": {"label": "กราฟิกบนมือถือและรูปแบบข้อความ", "options__0": {"label": "ข้อความภายในภาพ"}, "options__1": {"label": "ข้อความใต้ภาพ"}, "options__2": {"label": "ข้อความซ้อนทับรูปภาพ"}}}, "blocks": {"image": {"name": "รูปภาพ", "settings": {"image": {"label": "รูปภาพบนคอมพิวเตอร์"}, "image_mobile": {"label": "รูปภาพมือถือ"}, "image_show_position": {"label": "พื้นที่แสดงรูปภาพ", "options__0": {"label": "บนซ้าย"}, "options__1": {"label": "ด้านบนโดยตรง"}, "options__2": {"label": "บนขวา"}, "options__3": {"label": "ด้านซ้าย"}, "options__4": {"label": "กลาง"}, "options__5": {"label": "ด้านขวา"}, "options__6": {"label": "ล่างซ้าย"}, "options__7": {"label": "ด้านล่างโดยตรง"}, "options__8": {"label": "ล่างขวา"}}, "overlay_opacity": {"label": "หน้ากากภาพ"}, "text_mask": {"label": "เลเยอร์ข้อความ"}, "text_mask_color": {"label": "สีของเลเยอร์ข้อความ", "options__0": {"label": "สีเข้ม"}, "options__1": {"label": "สีอ่อน"}}, "pc_text_position": {"label": "ตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "บนซ้าย"}, "options__1": {"label": "ด้านบนโดยตรง"}, "options__2": {"label": "บนขวา"}, "options__3": {"label": "ด้านซ้าย"}, "options__4": {"label": "กลาง"}, "options__5": {"label": "ด้านขวา"}, "options__6": {"label": "ล่างซ้าย"}, "options__7": {"label": "ด้านล่างโดยตรง"}, "options__8": {"label": "ล่างขวา"}}, "mb_text_position": {"label": "ตำแหน่งเนื้อหาบนโทรศัพท์มือถือ", "options__0": {"label": "บนซ้าย"}, "options__1": {"label": "ด้านบนโดยตรง"}, "options__2": {"label": "บนขวา"}, "options__3": {"label": "ด้านซ้าย"}, "options__4": {"label": "กลาง"}, "options__5": {"label": "ด้านขวา"}, "options__6": {"label": "ล่างซ้าย"}, "options__7": {"label": "ด้านล่างโดยตรง"}, "options__8": {"label": "ล่างขวา"}}, "pc_text_color": {"label": "สีของข้อความ"}, "btn_bg_color": {"label": "สีพื้นหลังของปุ่ม"}, "btn_text_color": {"label": "สีข้อความของปุ่ม"}, "btn_border_color": {"label": "สีเส้นขอบของปุ่ม"}, "pc_text_align": {"label": "การจัดตำแหน่งเนื้อหาบนคอมพิวเตอร์", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "mobile_text_align": {"label": "การจัดตำแหน่งเนื้อหาบนมือถือ", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}, "options__2": {"label": "ชิดขวา"}}, "group_header__0": {"label": "เนื้อหาข้อความ"}, "sub_title": {"label": "หัวข้อรอง"}, "title": {"label": "หัวข้อหลัก"}, "title_size": {"label": "ขนาดตัวอักษรของหัวข้อหลัก"}, "subheading": {"label": "ข้อความ"}, "pc_content_width": {"label": "ความกว้างของเนื้อหาเทอร์มินัลคอมพิวเตอร์", "options__0": {"label": "ปรับตัวได้"}}, "text_color": {"label": "สีข้อความของพื้นที่ข้อความบนโทรศัพท์มือถือ"}, "text_area_background_color": {"label": "สีพื้นหลังพื้นที่ข้อความบนมือถือ"}, "group_header__1": {"label": "ปุ่มหรือเปลี่ยนรูปภาพ", "info": "หากไม่มีการดำหนดข้อความปุ่ม ให้คลิกรูปภาพเพื่อข้ามไป"}, "link_text": {"label": "ข้อความปุ่ม"}, "link": {"label": "ลิงก์กระโดดข้าม"}, "is_profile_link": {"label": "ใช้รูปแบบปุ่มเค้าร่าง"}, "link_text_2": {"label": "เนื้อหาปุ่ม2"}, "link_2": {"label": "ข้ามลิงก์2"}, "is_profile_link2": {"label": "ใช้รูปแบบปุ่มเค้าร่าง2"}}}}, "presets": {"presets__0": {"category": "การแสดงกราฟิก", "name": "ภาพหมุน"}}}, "spacing": {"name": "ปรับระยะห่าง", "settings": {"pc_height": {"label": "ความสูงบนคอมพิวเตอร์"}, "m_height": {"label": "ความสูงบนมือถือ"}, "background_color": {"label": "สี"}}, "presets": {"presets__0": {"category": "อื่น ๆ", "name": "ปรับระยะห่าง"}}}, "text-columns-with-image": {"name": "รายการกราฟิก", "settings": {"title": {"label": "หัวข้อ"}, "title_font_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "ใหญ่"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "เล็ก"}}, "image_width": {"label": "ความกว้างของภาพ", "options__0": {"label": "เท่ากับความกว้างของคอลัมน์"}, "options__1": {"label": "ความกว้างครึ่งคอลัมน์"}, "options__2": {"label": "ความกว้างหนึ่งในสามของคอลัมน์"}}, "image_ratio": {"label": "อัตราส่วนของรูปภาพ", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "1:1"}, "options__3": {"label": "ทรงกลม"}}, "pc_cols": {"label": "จำนวนคอลัมน์บนคอมพิวเตอร์"}, "text_align": {"label": "รูปแบบการจัดตำแหน่งเนื้อหา"}, "show_block_bg": {"label": "พื้นหลัง", "options__0": {"label": "ไม่มีพื้นหลัง"}, "options__1": {"label": "แสดงพื้นหลังของรายการ"}}, "button_text": {"label": "ข้อความปุ่ม"}, "jump_link": {"label": "ลิงก์กระโดดข้าม"}, "show_touch": {"label": "ปัดไปทางซ้ายและขวาบนมือถือ"}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "blocks": {"item": {"name": "รูปภาพ", "settings": {"image": {"label": "รูปภาพ"}, "title": {"label": "หัวข้อ"}, "description": {"label": "อธิบาย"}, "button_text": {"label": "ข้อความปุ่ม"}, "jump_link": {"label": "ลิงก์กระโดดข้าม"}}}}, "presets": {"presets__0": {"category": "การแสดงกราฟิก", "name": "รายการกราฟิก"}}}, "text-with-image": {"name": "โมดูลการประกบกราฟิก", "settings": {"layout": {"label": "เลย์เอาต์กราฟิก", "options__0": {"label": "ชื่อเรื่องชิดซ้าย"}, "options__1": {"label": "ชื่อเรื่องชิดขวา"}}, "group_header__0": {"label": "เนื้อหาข้อความ"}, "title": {"label": "หัวข้อ"}, "title_font_size": {"label": "ขนาดข้อความชื่อเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}, "content": {"label": "เนื้อหา"}, "button_text": {"label": "ข้อความปุ่ม"}, "button_link": {"label": "ลิงก์กระโดดข้าม"}, "group_header__1": {"label": "รูปภาพ"}, "image_1": {"label": "รูปที่ 1", "info": "ขนาดที่แนะนำ: 1200x1370px"}, "url_1": {"label": "ลิงค์ 1"}, "product_1": {"label": "สินค้า 1"}, "image_2": {"label": "รูปที่ 2", "info": "ขนาดที่แนะนำ: 1200x1370px"}, "url_2": {"label": "ข้ามลิงก์2"}, "product_2": {"label": "สินค้า 2"}, "show_image_line": {"label": "แสดงเส้นตกแต่งรูปภาพ"}, "image_height": {"label": "ความสูงของรูปภาพ", "options__0": {"label": "ปรับให้เข้ากับภาพ"}, "options__1": {"label": "สูง"}, "options__2": {"label": "ต่ำ"}}, "group_header__2": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "presets": {"presets__0": {"category": "การแสดงกราฟิก", "name": "โมดูลกราฟิก"}}}, "video": {"name": "วิดีโอ", "settings": {"title": {"label": "หัวข้อ"}, "title_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}, "cover": {"label": "ภาพหน้าปก"}, "internal_video": {"label": "วิดีโอในเครื่อง"}, "external_video": {"label": "ลิงค์วิดีโอภายนอก", "info": "ป้อนลิงก์ YouTube หรือ Vimeo แบบฝัง"}, "video_auto_play": {"label": "เล่นวิดีโออัตโนมัติ", "info": "ปิดเสียงเมื่อเล่นอัตโนมัติ"}, "full_width": {"label": "ความกว้างเต็มหน้าจอ"}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__0": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "presets": {"presets__0": {"category": "การแสดงกราฟิก", "name": "วิดีโอ"}}}}, "settings_schema": {"logo": {"name": "Logo", "settings": {"logo": {"label": "Logo"}, "desktop_logo_width": {"label": "ความกว้างสูงสุดของโลโก้บนพีซี", "info": "การตั้งค่านี้เป็นความกว้างสูงสุดที่แสดงผลได้ ซึ่งจริง ๆ แล้วปรับให้เข้ากับการแสดงผลตามขนาดหน้าจอ"}, "desktop_logo_height": {"label": "ความสูงสูงสุดของโลโก้บนพีซี"}, "mobile_logo_width": {"label": "ความกว้างสูงสุดของโลโก้บนมือถือ", "info": "การตั้งค่านี้เป็นความกว้างสูงสุดที่แสดงผลได้ ซึ่งจริง ๆ แล้วปรับให้เข้ากับการแสดงผลตามขนาดหน้าจอ"}, "mobile_logo_height": {"label": "ความสูงสูงสุดของโลโก้บนมือถือ"}}}, "color": {"name": "สี", "settings": {"group_header__0": {"label": "พื้นฐาน"}, "color_page_background": {"label": "พื้นหลัง"}, "color_text": {"label": "ข้อความ"}, "color_light_text": {"label": "ข้อความสีอ่อน"}, "color_entry_line": {"label": "เส้นและเส้นขอบ"}, "color_card_background": {"label": "พื้นหลังการ์ด", "info": "ใช้สําหรับสินค้า หมวดหมู่ บล็อกการ์ดที่มีสไตล์เป็นการ์ด"}, "color_card_text": {"label": "ข้อความการ์ด", "info": "ใช้สําหรับสินค้า หมวดหมู่ บล็อกการ์ดที่มีสไตล์เป็นการ์ด"}, "group_header__1": {"label": "ปุ่ม"}, "color_button_background": {"label": "พื้นหลังปุ่ม"}, "color_button_text": {"label": "ข้อความปุ่ม"}, "color_button_secondary_background": {"label": "พื้นหลังปุ่มรอง", "info": "ปุ่มรองต้องอยู่ในสถานะว่าง ซึ่งประกอบไปด้วยปุ่มเพิ่มลงรถเข็นสินค้า ปุ่มซื้อสินค้าด่วน ฯลฯ"}, "color_button_secondary_text": {"label": "ข้อความปุ่มรอง"}, "color_button_secondary_border": {"label": "เส้นขอบปุ่มรอง"}, "group_header__2": {"label": "สินค้า"}, "color_sale": {"label": "ราคาขาย"}, "color_discount": {"label": "ราคาที่ลด"}, "color_discount_tag_background": {"label": "ป้ายส่วนลด"}, "color_discount_tag_text": {"label": "ข้อความบนแท็กส่วนลด"}, "group_header__3": {"label": "อื่น ๆ"}, "color_cart_dot": {"label": "จุดเตือนตะกร้าสินค้า"}, "color_cart_dot_text": {"label": "ข้อความเตือนตะกร้าสินค้า"}, "color_image_background": {"label": "พื้นหลังรูปภาพ"}, "color_image_loading_background": {"label": "โหลดพื้นหลังสำหรับรูปภาพ"}, "color_mask": {"label": "พื้นหลัง mask layer ของหน้าต่างป๊อปอัป"}, "color_shadow": {"label": "เงา"}, "group_header__4": {"label": "จับคู่สี1"}, "color_scheme_1_bg": {"label": "สีพื้นหลัง"}, "color_scheme_1_text": {"label": "สีข้อความ"}, "group_header__5": {"label": "จับคู่สี2"}, "color_scheme_2_bg": {"label": "สีพื้นหลัง"}, "color_scheme_2_text": {"label": "สีข้อความ"}, "group_header__6": {"label": "จับคู่สี3"}, "color_scheme_3_bg": {"label": "สีพื้นหลัง"}, "color_scheme_3_text": {"label": "สีข้อความ"}}}, "font": {"name": "ฟอนต์", "settings": {"group_header__0": {"label": "หัวข้อ"}, "title_font_family": {"label": "ฟอนต์"}, "title_letter_spacing": {"label": "ระยะห่างตัวอักษร"}, "title_font_size": {"label": "ขนาดของตัวอักษร"}, "title_line_height": {"label": "ความสูงของบรรทัด"}, "title_uppercase": {"label": "ตัวพิมพ์ใหญ่ทั้งหมด"}, "group_header__1": {"label": "ข้อความ"}, "body_font_family": {"label": "ฟอนต์"}, "body_letter_spacing": {"label": "ระยะห่างตัวอักษร"}, "body_font_size": {"label": "ขนาดของตัวอักษร"}, "body_line_height": {"label": "ความสูงของบรรทัด"}}}, "layout": {"name": "เลย์เอาต์", "settings": {"page_width": {"label": "ความกว้างของหน้า"}, "section_vertical_gap": {"label": "ระยะห่างแนวตั้งระหว่างส่วนประกอบ"}, "group_header__0": {"label": "ตะแกรง", "info": "ส่วนประกอบและสินค้า/เค้าโครงหมวดหมที่ส่งผลต่อรูปแบบกริด"}, "grid_horizontal_space": {"label": "ระยะห่างแนวนอน"}, "grid_vertical_space": {"label": "ระยะห่างแนวตั้ง"}}}, "button": {"name": "ปุ่ม", "settings": {"btn_hover_animation": {"label": "เอฟเฟกต์โฮเวอร์", "options__0": {"label": "กวาดไปทางซ้ายและขวา"}, "options__1": {"label": "การฉายภาพซ้อนทับ"}, "options__2": {"label": "ซูมแบบลอยตัว"}, "options__3": {"label": "ช่องว่างภายในด้านขวา"}}, "group_header__0": {"label": "กรอบ"}, "btn_border_thickness": {"label": "ความหนา"}, "btn_border_opacity": {"label": "ความทึบ"}, "btn_border_radius": {"label": "รัศมีมุม"}, "group_header__1": {"label": "เงา"}, "btn_shadow_opacity": {"label": "ความทึบ"}, "btn_shadow_offset_x": {"label": "ออฟเซตแนวนอน"}, "btn_shadow_offset_y": {"label": "ออฟเซตแนวตั้ง"}, "btn_shadow_blur": {"label": "เบลอ"}}}, "sku": {"name": "ตัวเลือกคุณลักษณะหลายรายการ", "settings": {"group_header__0": {"label": "กรอบ"}, "sku_selector_border_thickness": {"label": "ความหนา"}, "sku_selector_border_opacity": {"label": "ความทึบ"}, "sku_selector_border_radius": {"label": "รัศมีมุม"}, "group_header__1": {"label": "เงา"}, "sku_selector_shadow_opacity": {"label": "ความทึบ"}, "sku_selector_shadow_offset_x": {"label": "ออฟเซตแนวนอน"}, "sku_selector_shadow_offset_y": {"label": "ออฟเซตแนวตั้ง"}, "sku_selector_shadow_blur": {"label": "เบลอ"}}}, "input": {"name": "กล่องใส่ข้อมูล", "settings": {"group_header__0": {"label": "กรอบ"}, "input_border_thickness": {"label": "ความหนา"}, "input_border_opacity": {"label": "ความทึบ"}, "input_border_radius": {"label": "รัศมีมุม"}, "group_header__1": {"label": "เงา"}, "input_shadow_opacity": {"label": "ความทึบ"}, "input_shadow_offset_x": {"label": "ออฟเซตแนวนอน"}, "input_shadow_offset_y": {"label": "ออฟเซตแนวตั้ง"}, "input_shadow_blur": {"label": "เบลอ"}}}, "product": {"name": "สินค้า", "settings": {"group_header__0": {"label": "รายการสินค้า"}, "enable_quick_view": {"label": "ซื้อด่วน"}, "quick_view_button_pc_style": {"label": "รูปแบบการซื้อด่วนในฝั่งคอมพิวเตอร์", "info": "หากตั้งค่าเป็นรูปแบบปุ่ม สามารถใช้รายการสินค้า - ปุ่มซื้อสินค้าด่วนเพื่อปรับตำแหน่งปุ่ม block", "options__0": {"label": "ปุ่ม"}, "options__1": {"label": "ไอคอน"}}, "quick_view_button_mobile_style": {"label": "รูปแบบการซื้อด่วนบนมือถือ", "info": "หากตั้งค่าเป็นรูปแบบปุ่ม สามารถใช้รายการสินค้า - ปุ่มซื้อสินค้าด่วนเพื่อปรับตำแหน่งปุ่ม block", "options__0": {"label": "ไอคอน"}, "options__1": {"label": "ปุ่ม"}}, "product_title_show_type": {"label": "กฎการแสดงหัวข้อสินค้า", "options__0": {"label": "แสดงทั้วหมด"}, "options__1": {"label": "แสดงไม่เกิน 1 แถว"}, "options__2": {"label": "แสดงได้ถึง 2 แถว"}}, "product_pc_title_show": {"label": "ชื่อผลิตภัณฑ์จอแสดงผลคอมพิวเตอร์"}, "product_mobile_title_show": {"label": "แสดงหัวข้อสินค้าบนโทรศัพท์มือถือ"}, "group_header__1": {"label": "ป้ายส่วนลดบัตรสินค้า", "info": "มีผลกับฉลากส่วนลดบัตรสินค้าเท่านั้น ฉลากที่แสดง ในหน้ารายละเอียดสินค้าสามารถตั้งค่าได้ใน \"รายละเอียดสินค้า-ราคา\""}, "product_discount": {"label": "แสดงส่วนลด"}, "product_discount_tag_style": {"label": "ฉลากส่วนลด", "options__0": {"label": "ป้ายขาย"}, "options__1": {"label": "บันทึกแท็ก"}}, "product_discount_style": {"label": "รูปแบบส่วนลด", "info": "จะมีผลเมื่อมีการเลือกแท็กบันทึกสำหรับแท็กข้อเสนอ", "options__0": {"label": "แสดงยอดเงินส่วนลด"}, "options__1": {"label": "แสดงเปอร์เซ็นต์ส่วนลด"}}, "product_discount_size": {"label": "เสนอขนาดฉลาก", "options__0": {"label": "กลาง"}, "options__1": {"label": "เล็ก"}}, "product_discount_position": {"label": "ตำแหน่งของแท็กส่วนลด", "options__0": {"label": "บนซ้าย"}, "options__1": {"label": "บนขวา"}, "options__2": {"label": "ล่างซ้าย"}, "options__3": {"label": "ล่างขวา"}}, "product_discount_radius": {"label": "รัศมีมุมของแท็กส่วนลด"}}}, "product_card": {"name": "การ์ดสินค้า", "settings": {"product_card_style": {"label": "สไตล์", "options__0": {"label": "มาตรฐาน"}, "options__1": {"label": "การ์ด"}}, "product_card_image_padding": {"label": "ระยะขอบภาพ", "info": "กำหนดระยะห่างระหว่างรูปภาพและพื้นหลัง"}, "product_card_content_align": {"label": "ทิศทางการจัดตำแหน่งเนื้อหาการ์ดสินค้า", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}}, "group_header__0": {"label": "กรอบ"}, "product_card_border_thickness": {"label": "ความหนา"}, "product_card_border_opacity": {"label": "ความทึบ"}, "product_card_border_radius": {"label": "รัศมีมุม"}, "group_header__1": {"label": "เงา"}, "product_card_shadow_opacity": {"label": "ความทึบ"}, "product_card_shadow_offset_x": {"label": "ออฟเซตแนวนอน"}, "product_card_shadow_offset_y": {"label": "ออฟเซตแนวตั้ง"}, "product_card_shadow_blur": {"label": "เบลอ"}}}, "collection_card": {"name": "การ์ดหมวดหมู่สินค้า", "settings": {"collection_card_style": {"label": "สไตล์", "options__0": {"label": "มาตรฐาน"}, "options__1": {"label": "การ์ด"}}, "collection_card_image_padding": {"label": "ระยะขอบภาพ", "info": "กำหนดระยะห่างระหว่างรูปภาพและพื้นหลัง"}, "collection_card_content_align": {"label": "รูปแบบการจัดตำแหน่งเนื้อหา", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}}, "group_header__0": {"label": "กรอบ"}, "collection_card_border_thickness": {"label": "ความหนา"}, "collection_card_border_opacity": {"label": "ความทึบ"}, "collection_card_border_radius": {"label": "รัศมีมุม"}, "group_header__1": {"label": "เงา"}, "collection_card_shadow_opacity": {"label": "ความทึบ"}, "collection_card_shadow_offset_x": {"label": "ออฟเซตแนวนอน"}, "collection_card_shadow_offset_y": {"label": "ออฟเซตแนวตั้ง"}, "collection_card_shadow_blur": {"label": "เบลอ"}}}, "blog_card": {"name": "การ์ดบล็อก", "settings": {"blog_card_style": {"label": "สไตล์", "options__0": {"label": "มาตรฐาน"}, "options__1": {"label": "การ์ด"}}, "blog_card_image_padding": {"label": "ระยะขอบภาพ", "info": "กำหนดระยะห่างระหว่างรูปภาพและพื้นหลัง"}, "blog_card_content_align": {"label": "รูปแบบการจัดตำแหน่งเนื้อหา", "options__0": {"label": "ชิดซ้าย"}, "options__1": {"label": "ศูนย์กลาง"}}, "group_header__0": {"label": "กรอบ"}, "blog_card_border_thickness": {"label": "ความหนา"}, "blog_card_border_opacity": {"label": "ความทึบ"}, "blog_card_border_radius": {"label": "รัศมีมุม"}, "group_header__1": {"label": "เงา"}, "blog_card_shadow_opacity": {"label": "ความทึบ"}, "blog_card_shadow_offset_x": {"label": "ออฟเซตแนวนอน"}, "blog_card_shadow_offset_y": {"label": "ออฟเซตแนวตั้ง"}, "blog_card_shadow_blur": {"label": "เบลอ"}}}, "other_card": {"name": "การ์ดอื่นๆ", "settings": {"group_header__0": {"label": "กรอบ"}, "card_border_thickness": {"label": "ความหนา"}, "card_border_opacity": {"label": "ความทึบ"}, "card_border_radius": {"label": "รัศมีมุม"}, "group_header__1": {"label": "เงา"}, "card_shadow_opacity": {"label": "ความทึบ"}, "card_shadow_offset_x": {"label": "ออฟเซตแนวนอน"}, "card_shadow_offset_y": {"label": "ออฟเซตแนวตั้ง"}, "card_shadow_blur": {"label": "เบลอ"}}}, "content": {"name": "ที่บรรจุเนื้อหา", "settings": {"group_header__0": {"label": "กรอบ"}, "content_border_thickness": {"label": "ความหนา"}, "content_border_opacity": {"label": "ความทึบ"}, "content_border_radius": {"label": "รัศมีมุม"}, "group_header__1": {"label": "เงา"}, "content_shadow_opacity": {"label": "ความทึบ"}, "content_shadow_offset_x": {"label": "ออฟเซตแนวนอน"}, "content_shadow_offset_y": {"label": "ออฟเซตแนวตั้ง"}, "content_shadow_blur": {"label": "เบลอ"}}}, "media_files": {"name": "ไฟล์สื่อ", "settings": {"group_header__0": {"label": "กรอบ"}, "media_border_thickness": {"label": "ความหนา"}, "media_border_opacity": {"label": "ความทึบ"}, "media_border_radius": {"label": "รัศมีมุม"}, "group_header__1": {"label": "เงา"}, "media_shadow_opacity": {"label": "ความทึบ"}, "media_shadow_offset_x": {"label": "ออฟเซตแนวนอน"}, "media_shadow_offset_y": {"label": "ออฟเซตแนวตั้ง"}, "media_shadow_blur": {"label": "เบลอ"}}}, "dropdown_menu": {"name": "เมนูแบบเลื่อนลงและป๊อปอัป", "settings": {"group_header__0": {"label": "กรอบ"}, "menu_modal_border_thickness": {"label": "ความหนา"}, "menu_modal_border_opacity": {"label": "ความทึบ"}, "menu_modal_border_radius": {"label": "รัศมีมุม"}, "group_header__1": {"label": "เงา"}, "menu_modal_shadow_opacity": {"label": "ความทึบ"}, "menu_modal_shadow_offset_x": {"label": "ออฟเซตแนวนอน"}, "menu_modal_shadow_offset_y": {"label": "ออฟเซตแนวตั้ง"}, "menu_modal_shadow_blur": {"label": "เบลอ"}}}, "drawer": {"name": "ลิ้นชัก", "settings": {"group_header__0": {"label": "กรอบ"}, "drawer_border_thickness": {"label": "ความหนา"}, "drawer_border_opacity": {"label": "ความทึบ"}, "group_header__1": {"label": "เงา"}, "drawer_shadow_opacity": {"label": "ความทึบ"}, "drawer_shadow_offset_x": {"label": "ออฟเซตแนวนอน"}, "drawer_shadow_offset_y": {"label": "ออฟเซตแนวตั้ง"}, "drawer_shadow_blur": {"label": "เบลอ"}}}, "cart": {"name": "ตะกร้าสินค้า", "settings": {"cart_type": {"label": "วิธีการซื้อเพิ่ม", "options__0": {"label": "ตะกร้าช้อปปิ้งแบบลิ้นชัก"}, "options__1": {"label": "เข้าสู่หน้าตะกร้าสินค้า"}, "options__2": {"label": "การแจ้งเตือนป๊อปอัป"}}, "group_header__0": {"label": "คำแนะนำตะกร้าสินค้าเปล่า"}, "cart_empty_recommend_title": {"label": "หัวข้อ"}, "cart_empty_recommend_collection": {"label": "หมวดหมู่สินค้าที่แนะนำ"}, "cart_empty_recommend_product_to_show": {"label": "จำนวนสินค้าสูงสุด"}, "cart_empty_recommend_product_image_ratio": {"label": "อัตราส่วนภาพสินค้า", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "cart_empty_recommend_product_image_fill_type": {"label": "วิธีการเติมภาพสินค้า", "options__0": {"label": "ปรับ"}, "options__1": {"label": "การกรอก"}}}}, "product-recently-viewed": {"name": "สินค้าที่ดูล่าสุด", "settings": {"title": {"label": "หัวข้อ"}, "title_size": {"label": "ขนาดหัวเรื่อง", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}, "show_product_number": {"label": "จำนวนสินค้าที่แสดง"}, "pc_cols": {"label": "จำนวนคอลัมน์บนคอมพิวเตอร์"}, "mobile_cols": {"label": "จำนวนคอลัมน์บนมือถือ", "options__0": {"label": "1 คอลัมน์"}, "options__1": {"label": "2 คอลัมน์"}}, "enable_horizontal_slider": {"label": "เปิดใช้งานการปัดไปทางซ้ายและขวา", "info": "มีผลเมื่อจำนวนสินค้าที่แสดงเกินจำนวนคอลัมน์"}, "color_scheme": {"label": "จับคู่สี", "options__0": {"label": "เปล่า"}, "options__1": {"label": "จับคู่สี1"}, "options__2": {"label": "จับคู่สี2"}, "options__3": {"label": "จับคู่สี3"}}, "group_header__0": {"label": "ภาพสินค้า"}, "product_image_ratio": {"label": "อัตราส่วนภาพสินค้า", "options__0": {"label": "อัตราส่วนเดิม"}, "options__1": {"label": "1:1"}, "options__3": {"label": "4:3"}, "options__4": {"label": "2:3"}}, "product_fill_type": {"label": "วิธีการเติมภาพสินค้า", "options__0": {"label": "ปรับ"}, "options__1": {"label": "การกรอก"}}, "show_secondary_image": {"label": "แสดงรูปภาพสินค้ารูปถัดไปเมื่อวางเมาส์ไว้"}, "group_header__1": {"label": "แพคดิงในโซน"}, "padding_top": {"label": "แพคดิงด้านบน"}, "padding_bottom": {"label": "แพคดิงด้านล่าง"}}, "presets": {"presets__0": {"category": "โชว์สินค้า", "name": "สินค้าที่ดูล่าสุด"}}}, "checkout": {"name": "ชำระเงิน", "settings": {"group_header__0": {"label": "Banner"}, "co_banner_pc_img": {"label": "รูปภาพบนคอมพิวเตอร์"}, "co_banner_phone_img": {"label": "รูปภาพมือถือ", "info": "หากไม่ได้อัปโหลดภาพเทอร์มินัลมือถือ เทอร์มินัลมือถือจะแสดงรูปภาพเทอร์มินัลคอมพิวเตอร์"}, "co_banner_pc_height": {"label": "ความสูงของภาพบนคอมพิวเตอร์", "options__0": {"label": "ปรับให้เข้ากับภาพ"}, "options__1": {"label": "สูง"}, "options__2": {"label": "ต่ำ"}}, "co_banner_phone_height": {"label": "ความสูงของภาพบนมือถือ", "options__0": {"label": "ปรับให้เข้ากับภาพ"}, "options__1": {"label": "สูง"}, "options__2": {"label": "ต่ำ"}}, "co_banner_img_show": {"label": "พื้นที่แสดงรูปภาพ", "options__0": {"label": "บน"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ล่าง"}}, "co_full_screen": {"label": "ความกว้างเต็มหน้าจอบนคอมพิวเตอร์"}, "group_header__1": {"label": "Logo"}, "co_checkout_image": {"label": "รูปภาพที่กำหนดเอง"}, "co_logo_size": {"label": "ขนาดLogo", "options__0": {"label": "เล็ก"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ใหญ่"}}, "co_logo_position": {"label": "ตำแหน่งLogo", "options__0": {"label": "ซ้าย"}, "options__1": {"label": "กลาง"}, "options__2": {"label": "ขวา"}}, "group_header__2": {"label": "ข้อมูลเนื้อหา"}, "co_bg_image": {"label": "ภาพพื้นหลัง"}, "co_background_color": {"label": "สีพื้นหลัง"}, "co_form_bg_color": {"label": "สีพื้นหลังแบบฟอร์ม", "options__0": {"label": "สีขาว"}, "options__1": {"label": "โปร่งใส"}}, "group_header__3": {"label": "สรุปคำสั่งซื้อ"}, "co_order_bg_image": {"label": "ภาพพื้นหลัง"}, "co_order_background_color": {"label": "สีพื้นหลัง"}, "group_header__4": {"label": "ฟอนต์"}, "co_type_title_font": {"label": "หัวข้อ"}, "co_type_body_font": {"label": "ข้อความ"}, "group_header__5": {"label": "สี"}, "co_color_btn_bg": {"label": "พื้นหลังปุ่ม"}, "co_color_err_color": {"label": "ข้อความแจ้งข้อผิดพลาด", "info": "ข้อความแจ้งข้อผิดพลาดและไฮไลท์เส้นขอบ"}, "co_color_msg_color": {"label": "ข้อมูลเน้นให้เด่น", "info": "ลิงก์ กล่องเลือก เส้นขอบของช่องป้อนข้อมูล และเมนูแบบเลื่อนลง"}}}, "media_sosial": {"name": "สื่อสังคม", "settings": {"group_header__0": {"label": "บัญชีผู้ใช้", "info": "ใช้สำหรับลูกค้าเข้าถึงบัญชีโซเชียลมีเดียของคุณ"}, "show_official_icon": {"label": "แสดงไอคอนอย่างเป็นทางการของโซเชียลมีเดีย"}, "social_facebook_link": {"label": "Facebook"}, "social_twitter_link": {"label": "X（Twitter）"}, "social_pinterest_link": {"label": "Pinterest"}, "social_instagram_link": {"label": "Instagram"}, "social_snapchat_link": {"label": "Snapchat"}, "social_tiktok_link": {"label": "TikTok"}, "social_youtube_link": {"label": "Youtube"}, "social_vimeo_link": {"label": "Vimeo"}, "social_tumblr_link": {"label": "Tumblr"}, "social_linkedin_link": {"label": "Linkedin"}, "social_whatsapp_link": {"label": "WhatsApp"}, "social_line_link": {"label": "Line"}, "social_kakao_link": {"label": "<PERSON><PERSON><PERSON>"}, "group_header__1": {"label": "แบ่งปัน", "info": "จัดให้มีการดำเนินการแบ่งปันในหน้ารายละเอียดสินค้าและหน้าบล็อก"}, "show_official_share_icon": {"label": "แสดงไอคอนอย่างเป็นทางการของโซเชียลมีเดีย"}, "show_social_name": {"label": "แสดงชื่อโซเชียลมีเดีย"}, "share_to_facebook": {"label": "แชร์ไปที่ Facebook"}, "share_to_twitter": {"label": "แบ่งปันไปที่ X（Twitter）"}, "share_to_pinterest": {"label": "แบ่งปันไปที่ Pinterest"}, "share_to_line": {"label": "แบ่งปันไปที่ LINE"}, "share_to_whatsapp": {"label": "แบ่งปันไปที่ Whatsapp"}, "share_to_tumblr": {"label": "แบ่งปันไปที่ Tumblr"}}}, "search_behavior": {"name": "พฤติกรรมการค้นหา", "settings": {"show_search_goods_price": {"label": "สินค้าแนะนําแสดงราคา"}}}, "breadcrumb": {"name": "เกล็ดขนมปัง", "settings": {"show_pc_breadcrumb": {"label": "แสดงเบรดครัมบ์บนคอมพิวเตอร์"}, "show_mobile_breadcrumb": {"label": "แสดงเบรดครัมบ์บนมือถือ"}}}, "favicon": {"name": "ไอคอนเว็บไซต์", "settings": {"favicon_image": {"label": "iconป้ายแท็บเบราว์เซอร์", "info": "ขนาด: 32x32px"}}}}}