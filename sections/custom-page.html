{{snippet "stylesheet" href=(asset_url "section-main-page.css")}}
{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}
<div class="color-scheme-{{section.settings.color_scheme}}">
  <div class="custom-page page-width page-width--narrow section-padding">
    <h2 class="page-title {{section.settings.heading_size}}">
      {{#if section.settings.page.title}}
        {{section.settings.page.title}}
      {{else}}
        {{t "customer.general.title"}}
      {{/if}}
    </h2>
    <div class="rte">
      {{#if section.settings.page.content}}
        {{{section.settings.page.content}}}
      {{else}}
        {{placeholder_svg_tag "image" "placeholder"}}
      {{/if}}
    </div>
  </div>
</div>
{{#schema}}
{
  "name": "t:sections.custom-page.name",
  "class": "section",
  "presets": [
    {
      "category_index": 7,
      "category": "t:sections.custom-page.presets.presets__0.category",
      "name": "t:sections.custom-page.presets.presets__0.name",
      "settings": {
        "page": "",
        "heading_size": "title3",
        "color_scheme": "none",
        "padding_top": 80,
        "padding_bottom": 80
      }
    }
  ],
  "settings": [
    {
      "type": "page_picker",
      "id": "page",
      "label": "t:sections.custom-page.settings.page.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "title5",
          "label": "t:sections.custom-page.settings.heading_size.options__0.label"
        },
        {
          "value": "title3",
          "label": "t:sections.custom-page.settings.heading_size.options__1.label"
        },
        {
          "value": "title2",
          "label": "t:sections.custom-page.settings.heading_size.options__2.label"
        }
      ],
      "default": "title3",
      "label": "t:sections.custom-page.settings.heading_size.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "t:sections.custom-page.settings.color_scheme.label",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "t:sections.custom-page.settings.color_scheme.options__0.label"
        },
        {
          "value": "1",
          "label": "t:sections.custom-page.settings.color_scheme.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.custom-page.settings.color_scheme.options__2.label"
        },
        {
          "value": "3",
          "label": "t:sections.custom-page.settings.color_scheme.options__3.label"
        }
      ]
    },
    {
      "type": "group_header",
      "label": "t:sections.custom-page.settings.group_header__0.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.custom-page.settings.padding_top.label",
      "default": 80
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.custom-page.settings.padding_bottom.label",
      "default": 80
    }
  ]
}
{{/schema}}