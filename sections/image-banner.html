{{snippet "stylesheet" href=(asset_url "section-image-banner.css")}}

{{assign "banner1" section.settings.banner1}}
{{assign "banner2" section.settings.banner2}}
{{assign "banner_height_size" section.settings.banner_height_size}}
{{assign "override_banner_height" section.settings.override_banner_height}}
{{assign "pc_position" section.settings.pc_content_position}}
{{assign "show_textarea" section.settings.pc_show_textarea}}
{{assign "alpha_range" section.settings.alpha_range}}
{{assign "color_scheme" section.settings.color_scheme}}
{{assign "mobile_flatten" section.settings.mobile_banner_flatten}}
{{assign "show_mobile_textarea" section.settings.m_show_textarea}}
{{assign "blocks_length" (default (length section.blocks) 0)}}
{{assign "image_pc_size" (ternary (if banner1 and banner2) "1/2" "100vw")}}

{{assign "banner_ratio" "0"}}
{{#if (isTruthy override_banner_height)}}
  {{#if banner1}}
    {{assign "banner_ratio" (append (default (divide 100 banner1.aspect_ratio) 100) "%")}}
  {{/if}}
  {{#if banner2}}
    {{#unless banner_ratio}}
      {{assign "banner_ratio" (append (default (divide 100 banner2.aspect_ratio) 100) "%")}}
    {{/unless}}
  {{/if}}
{{/if}}

<div class="section section-padding section-image-banner">
  
  <div
    class="section-image-banner-pc {{#if banner1 or banner2}}image-banner-loaded{{/if}} display-none display-block-desktop"
  >
    <div class="image-banner--container">
      {{#if banner1}}
        <div
          class="banner-item-wrapper
            banner-item-wrapper--size-{{#if (isFalsey override_banner_height)}}{{banner_height_size}}{{else}}none{{/if}}"
        >
          <div style="padding-bottom: {{banner_ratio}};">
            {{snippet "image" data=banner1 pc_size=image_pc_size class="banner-item banner-pc-item"}}
          </div>
        </div>
      {{/if}}
      {{#if banner2}}
        <div
          class="banner-item-wrapper
            banner-item-wrapper--size-{{#if (isFalsey override_banner_height)}}{{banner_height_size}}{{else}}none{{/if}}"
        >
          <div style="padding-bottom: {{banner_ratio}};">
            {{snippet "image" data=banner2 pc_size=image_pc_size class="banner-item banner-pc-item"}}
          </div>
        </div>
      {{/if}}
      {{#if (isFalsey banner1) and (isFalsey banner2)}}
        {{placeholder_svg_tag "lifestyle-1" "default-image"}}
      {{/if}}
      <div class="mask" style="opacity: {{alpha_range}}%"></div>
    </div>
    
    {{#if blocks_length > 0}}
      <div class="content content--pos-{{pc_position}}">
        <div class="page-width">
          <div
            class="wrapper global-content-border-shadow
              pc-content-text-{{section.settings.pc_text_position}}
              {{#if (isFalsey show_textarea) and (isFalsey banner1) and (isFalsey banner2)}}wrapper--default--shadow{{/if}}
              {{#if show_textarea}}
                color-scheme-{{color_scheme}}
              {{else}}
                wrapper--default-scheme
              {{/if}}"
          >
            {{#for section.blocks as |block|}}
              {{#if (trim block.type) == "title"}}
                <h2 {{{block.shopline_attributes}}} class="title {{block.settings.title_size}}">
                  {{newline_to_br block.settings.title}}
                </h2>
              {{/if}}

              {{#if (trim block.type) == "desc"}}
                <p {{{block.shopline_attributes}}} class="desc body1">
                  {{block.settings.description}}
                </p>
              {{/if}}
              {{#if (trim block.type) == "button"}}
                {{assign "outline_button" block.settings.outline_button}}
                {{assign "outline_button_2" block.settings.outline_button_2}}
                <div {{{block.shopline_attributes}}} class="control-wrap">
                  <div class="control">
                    {{~#if block.settings.button_text}}
                      <a
                        {{#if block.settings.link}}href="{{block.settings.link}}"{{else}}href="javascript:;"{{/if}}
                        class="button {{#if (isTruthy outline_button)}}button--secondary{{else}}btn-primary{{/if}}"
                      >
                        {{block.settings.button_text}}
                      </a>
                    {{~/if}}
                    {{~#if block.settings.link_text_2}}
                      <a
                        {{#if block.settings.link_2}}href="{{block.settings.link_2}}"{{else}}href="javascript:;"{{/if}}
                        class="button {{#if (isTruthy outline_button_2)}}button--secondary{{else}}btn-primary{{/if}}"
                      >
                        {{block.settings.link_text_2}}
                      </a>
                    {{~/if}}
                  </div>
                </div>
              {{/if}}
            {{/for}}
          </div>
        </div>
      </div>
    {{/if}}
  </div>
  
  <div
    class="section-image-banner-mobile
      {{#if banner1 or banner2}}image-banner-loaded{{/if}}
      display-block display-none-desktop
      {{#if (isTruthy mobile_flatten)}}section-image-banner--m-flatten{{else}}section-image-banner--m-normal {{/if}}"
    data-section-type="image-banner"
    data-section-id="{{section.id}}"
  >
    <div class="image-banner--container">
      {{#if (isFalsey banner1) and (isFalsey banner2)}}
        {{placeholder_svg_tag "lifestyle-1" "default-image"}}
      {{/if}}
      {{#if banner1}}
        <div
          class="banner-item-wrapper first-banner
            banner-item-wrapper--size-{{#if override_banner_height}}none{{else}}{{banner_height_size}}{{/if}}"
        >
          <div style="padding-bottom: {{banner_ratio}};">
            {{snippet "image" data=banner1 mobile_size="100%" class="banner-item banner-mobile-item"}}
          </div>
        </div>
      {{/if}}

      {{#if blocks_length > 0}}
        <div
          class="content content--pos-center
            {{#if show_mobile_textarea}}content-m--normal-flow{{else}}content-m-no-bg{{/if}}
            "
        >
          <div
            class="wrapper page-width
              mobile-content-text-{{section.settings.mobile_text_position}}
              {{#if (isFalsey show_mobile_textarea) and (isFalsey banner1) and (isFalsey banner2)}}wrapper--default--shadow{{/if}}
              {{#if show_mobile_textarea}} color-scheme-{{color_scheme}}
              {{else}} wrapper--default-scheme {{/if}}
              "
          >
            {{#for section.blocks as |block|}}
              {{#if (trim block.type) == "title"}}
                <h2 {{{block.shopline_attributes}}} class="title2 {{block.settings.title_size}}">
                  {{newline_to_br block.settings.title}}
                </h2>
              {{/if}}

              {{#if (trim block.type) == "desc"}}
                <p {{{block.shopline_attributes}}} class="body1">
                  {{block.settings.description}}
                </p>
              {{/if}}
              {{#if (trim block.type) == "button"}}
                {{assign "outline_button" block.settings.outline_button}}
                {{assign "outline_button_2" block.settings.outline_button_2}}
                <div class="control-wrap" {{{block.shopline_attributes}}}>
                  <div class="control">
                    {{~#if block.settings.button_text}}
                      <a
                        href="{{block.settings.link}}"
                        class="button {{#if (isTruthy outline_button)}}button--secondary{{/if}}"
                      >
                        {{block.settings.button_text}}
                      </a>
                    {{~/if}}
                    {{~#if block.settings.link_text_2}}
                      <a
                        href="{{block.settings.link_2}}"
                        class="button {{#if (isTruthy outline_button_2)}}button--secondary{{/if}}"
                      >
                        {{block.settings.link_text_2}}
                      </a>
                    {{~/if}}
                  </div>
                </div>

              {{/if}}
            {{/for}}
          </div>
        </div>
      {{/if}}

      {{#if banner2}}
        <div
          class="banner-item-wrapper second-banner
            banner-item-wrapper--size-{{#if (isFalsey override_banner_height)}}{{banner_height_size}}{{else}}none{{/if}}
            "
        >
          <div style="padding-bottom: {{banner_ratio}};">
            {{snippet "image" data=banner2 mobile_size="100%" class="banner-item banner-mobile-item"}}
          </div>
        </div>
      {{/if}}
      
      {{#if (isFalsey show_mobile_textarea) or (if (isFalsey image_url banner1) and (isFalsey image_url banner2))}}
        <div class="mask" style="opacity: {{alpha_range}}%"></div>
      {{/if}}
    </div>
  </div>
</div>
{{#schema}}
{
  "name": "t:sections.image-banner.name",
  "class": "section",
  "settings": [
    {
      "id": "banner1",
      "type": "image_picker",
      "label": "t:sections.image-banner.settings.banner1.label"
    },
    {
      "id": "banner2",
      "type": "image_picker",
      "label": "t:sections.image-banner.settings.banner2.label"
    },
    {
      "id": "banner_height_size",
      "type": "select",
      "label": "t:sections.image-banner.settings.banner_height_size.label",
      "info": "t:sections.image-banner.settings.banner_height_size.info",
      "options": [
        {
          "value": "low",
          "label": "t:sections.image-banner.settings.banner_height_size.options__0.label"
        },
        {
          "label": "t:sections.image-banner.settings.banner_height_size.options__1.label",
          "value": "middle"
        },
        {
          "value": "high",
          "label": "t:sections.image-banner.settings.banner_height_size.options__2.label"
        }
      ],
      "default": "middle"
    },
    {
      "id": "override_banner_height",
      "type": "switch",
      "label": "t:sections.image-banner.settings.override_banner_height.label",
      "info": "t:sections.image-banner.settings.override_banner_height.info",
      "default": false
    },
    {
      "id": "pc_content_position",
      "type": "select",
      "label": "t:sections.image-banner.settings.pc_content_position.label",
      "options": [
        {
          "label": "t:sections.image-banner.settings.pc_content_position.options__0.label",
          "value": "left-top"
        },
        {
          "label": "t:sections.image-banner.settings.pc_content_position.options__1.label",
          "value": "top"
        },
        {
          "label": "t:sections.image-banner.settings.pc_content_position.options__2.label",
          "value": "right-top"
        },
        {
          "label": "t:sections.image-banner.settings.pc_content_position.options__3.label",
          "value": "left"
        },
        {
          "label": "t:sections.image-banner.settings.pc_content_position.options__4.label",
          "value": "center"
        },
        {
          "label": "t:sections.image-banner.settings.pc_content_position.options__5.label",
          "value": "right"
        },
        {
          "label": "t:sections.image-banner.settings.pc_content_position.options__6.label",
          "value": "left-bottom"
        },
        {
          "label": "t:sections.image-banner.settings.pc_content_position.options__7.label",
          "value": "bottom"
        },
        {
          "label": "t:sections.image-banner.settings.pc_content_position.options__8.label",
          "value": "right-bottom"
        }
      ],
      "default": "center"
    },
    {
      "id": "pc_text_position",
      "type": "select",
      "label": "t:sections.image-banner.settings.pc_text_position.label",
      "options": [
        {
          "label": "t:sections.image-banner.settings.pc_text_position.options__0.label",
          "value": "left"
        },
        {
          "label": "t:sections.image-banner.settings.pc_text_position.options__1.label",
          "value": "center"
        },
        {
          "label": "t:sections.image-banner.settings.pc_text_position.options__2.label",
          "value": "right"
        }
      ],
      "default": "center"
    },
    {
      "id": "pc_show_textarea",
      "type": "switch",
      "label": "t:sections.image-banner.settings.pc_show_textarea.label",
      "default": true
    },
    {
      "id": "alpha_range",
      "label": "t:sections.image-banner.settings.alpha_range.label",
      "type": "range",
      "default": 30,
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "default": "none",
      "label": "t:sections.image-banner.settings.color_scheme.label",
      "options": [
        {
          "value": "none",
          "label": "t:sections.image-banner.settings.color_scheme.options__0.label"
        },
        {
          "label": "t:sections.image-banner.settings.color_scheme.options__1.label",
          "value": "1"
        },
        {
          "label": "t:sections.image-banner.settings.color_scheme.options__2.label",
          "value": "2"
        },
        {
          "label": "t:sections.image-banner.settings.color_scheme.options__3.label",
          "value": "3"
        }
      ]
    },
    {
      "type": "group_header",
      "label": "t:sections.image-banner.settings.group_header__0.label"
    },
    {
      "id": "mobile_text_position",
      "type": "select",
      "label": "t:sections.image-banner.settings.mobile_text_position.label",
      "options": [
        {
          "label": "t:sections.image-banner.settings.mobile_text_position.options__0.label",
          "value": "left"
        },
        {
          "label": "t:sections.image-banner.settings.mobile_text_position.options__1.label",
          "value": "center"
        },
        {
          "label": "t:sections.image-banner.settings.mobile_text_position.options__2.label",
          "value": "right"
        }
      ],
      "default": "center"
    },
    {
      "id": "mobile_banner_flatten",
      "type": "switch",
      "label": "t:sections.image-banner.settings.mobile_banner_flatten.label",
      "default": true
    },
    {
      "id": "m_show_textarea",
      "type": "switch",
      "label": "t:sections.image-banner.settings.m_show_textarea.label",
      "default": true
    }
  ],
  "max_blocks": 3,
  "blocks": [
    {
      "limit": 1,
      "icon": "title",
      "type": "title",
      "name": "t:sections.image-banner.blocks.title.name",
      "settings": [
        {
          "type": "textarea",
          "limit": 500,
          "id": "title",
          "label": "t:sections.image-banner.blocks.title.settings.title.label",
          "default": "Two Line\nTitle Slide"
        },
        {
          "type": "select",
          "id": "title_size",
          "label": "t:sections.image-banner.blocks.title.settings.title_size.label",
          "default": "title3",
          "options": [
            {
              "value": "title1",
              "label": "t:sections.image-banner.blocks.title.settings.title_size.options__0.label"
            },
            {
              "value": "title3",
              "label": "t:sections.image-banner.blocks.title.settings.title_size.options__1.label"
            },
            {
              "value": "title5",
              "label": "t:sections.image-banner.blocks.title.settings.title_size.options__2.label"
            }
          ]
        }
      ]
    },
    {
      "limit": 1,
      "icon": "paragraph",
      "type": "desc",
      "name": "t:sections.image-banner.blocks.desc.name",
      "settings": [
        {
          "id": "description",
          "type": "text",
          "default": "And optional subtext",
          "label": "t:sections.image-banner.blocks.desc.settings.description.label"
        }
      ]
    },
    {
      "limit": 1,
      "icon": "button",
      "type": "button",
      "name": "t:sections.image-banner.blocks.button.name",
      "settings": [
        {
          "type": "text",
          "id": "button_text",
          "label": "t:sections.image-banner.blocks.button.settings.button_text.label",
          "default": "Shop this"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.image-banner.blocks.button.settings.link.label"
        },
        {
          "id": "outline_button",
          "type": "switch",
          "default": true,
          "label": "t:sections.image-banner.blocks.button.settings.outline_button.label"
        },
        {
          "type": "text",
          "id": "link_text_2",
          "label": "t:sections.image-banner.blocks.button.settings.link_text_2.label",
          "default": "Shop all"
        },
        {
          "type": "url",
          "id": "link_2",
          "label": "t:sections.image-banner.blocks.button.settings.link_2.label"
        },
        {
          "id": "outline_button_2",
          "type": "switch",
          "default": true,
          "label": "t:sections.image-banner.blocks.button.settings.outline_button_2.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "category_index": 1,
      "category": "t:sections.image-banner.presets.presets__0.category",
      "name": "t:sections.image-banner.presets.presets__0.name",
      "settings": {
        "banner1": "",
        "banner2": "",
        "banner_height_size": "middle",
        "override_banner_height": false,
        "pc_text_position": "center",
        "pc_content_position": "center",
        "mobile_text_position": "center",
        "pc_show_textarea": true,
        "alpha_range": "30",
        "color_scheme": "none",
        "mobile_banner_flatten": true,
        "m_show_textarea": true
      },
      "blocks": [
        {
          "type": "title",
          "settings": {
            "title": "Two Line\nTitle Slide",
            "title_size": "title3"
          }
        },
        {
          "type": "desc",
          "settings": {
            "description": "And optional subtext"
          }
        },
        {
          "type": "button",
          "settings": {
            "button_text": "Shop this",
            "link": "",
            "outline_button": true,
            "link_text_2": "Shop all",
            "link_2": "",
            "outline_button_2": true
          }
        }
      ]
    }
  ]
}
{{/schema}}