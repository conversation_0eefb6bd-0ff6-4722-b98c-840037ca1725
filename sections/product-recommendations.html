<script src="{{asset_url 'section-product-recommendations.js'}}" defer></script>
<script src="{{asset_url 'component-slider.js'}}" defer></script>
{{snippet "stylesheet" href=(asset_url "component-price.css")}}
{{snippet "stylesheet" href=(asset_url "component-card.css")}}
{{snippet "stylesheet" href=(asset_url "section-product-recommendations.css")}}

{{#if settings.enable_quick_view}}
  {{snippet "stylesheet" href=(asset_url "component-quick-add.css") lazy=true}}
  <script src="{{asset_url 'component-quick-add.js'}}" defer="defer"></script>
{{/if}}

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}

{{assign "title" section.settings.title}}
{{assign "mobile_cols" section.settings.mobile_cols}}
{{assign "pc_cols" section.settings.pc_cols}}
{{assign "color_scheme" section.settings.color_scheme}}
{{assign "enable_horizontal_slider" section.settings.enable_horizontal_slider}}

<div class="page-width section-padding color-scheme-{{color_scheme}}">
  <product-recommendations
    data-url="{{routes.product_recommendations_url}}?section_id={{section.id}}&product_id={{product.id}}&limit={{section.settings.products_to_show}}"
  >
    {{assign "pc_enable_horizontal_slider" false}}
    {{assign "mobile_enable_horizontal_slider" false}}

    {{#if enable_horizontal_slider and recommendations.products_count > pc_cols}}
      {{assign "pc_enable_horizontal_slider" true}}
    {{/if}}
    {{#if enable_horizontal_slider and recommendations.products_count > mobile_cols}}
      {{assign "mobile_enable_horizontal_slider" true}}
    {{/if}}

    <h2 class="title4 title-wrapper text-center product-recommendations-title {{section.settings.title_size}}">
      {{title}}
    </h2>
    {{#if (isTruthy recommendations.performed) and recommendations.products_count > 0}}

      <slider-component
        class="slider-product-recommendations
          {{~#unless pc_enable_horizontal_slider}} no-slider-pc{{/unless~}}
          {{~#unless mobile_enable_horizontal_slider}} no-slider-mobile{{/unless~}}
          "
      >
        <ul
          id="Slider-{{section.id}}"
          class="grid
            grid-cols-{{pc_cols}}-desktop
            grid-cols-{{mobile_cols}}
            slider
            {{#if mobile_enable_horizontal_slider}}slider--mobile{{/if}}
            "
          style="--mobile-cols: {{mobile_cols}};"
        >
          {{#for recommendations.products as |product_item|}}
            <li id="Slide-{{product_item.id}}" class="slider__slide">
              {{snippet
                "product-card"
                product_data=product_item
                show_secondary_image=section.settings.show_secondary_image
                image_ratio=section.settings.product_image_ratio
                section_id=section.id
                desktop_grid_cols=section.settings.pc_cols
                mobile_grid_cols=section.settings.mobile_cols
                image_fill_type=section.settings.product_fill_type
                theme_settings=settings
                blocks=section.blocks
              }}
            </li>
          {{/for}}
        </ul>

        <button name="previous" class="slider-product-recommendations__button previous display-none-tablet">
          {{snippet "icon-arrow"}}
        </button>
        <button name="next" class="slider-product-recommendations__button next display-none-tablet">
          {{snippet "icon-arrow"}}
        </button>

      </slider-component>

    {{/if}}
  </product-recommendations>
</div>
{{#schema}}
{
  "name": "t:sections.product-recommendations.name",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.product-recommendations.settings.title.label",
      "default": "You may also like"
    },
    {
      "id": "title_size",
      "type": "select",
      "label": "t:sections.product-recommendations.settings.title_size.label",
      "options": [
        {
          "value": "large",
          "label": "t:sections.product-recommendations.settings.title_size.options__0.label"
        },
        {
          "value": "medium",
          "label": "t:sections.product-recommendations.settings.title_size.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.product-recommendations.settings.title_size.options__2.label"
        }
      ],
      "default": "medium"
    },
    {
      "type": "range",
      "id": "products_to_show",
      "min": 2,
      "max": 12,
      "step": 2,
      "default": 4,
      "label": "t:sections.product-recommendations.settings.products_to_show.label"
    },
    {
      "id": "pc_cols",
      "type": "range",
      "label": "t:sections.product-recommendations.settings.pc_cols.label",
      "min": 1,
      "max": 5,
      "step": 1,
      "default": 4
    },
    {
      "id": "mobile_cols",
      "type": "select",
      "label": "t:sections.product-recommendations.settings.mobile_cols.label",
      "options": [
        {
          "value": 1,
          "label": "t:sections.product-recommendations.settings.mobile_cols.options__0.label"
        },
        {
          "value": 2,
          "label": "t:sections.product-recommendations.settings.mobile_cols.options__1.label"
        }
      ],
      "default": 2
    },
    {
      "id": "enable_horizontal_slider",
      "type": "switch",
      "label": "t:sections.product-recommendations.settings.enable_horizontal_slider.label",
      "info": "t:sections.product-recommendations.settings.enable_horizontal_slider.info",
      "default": false
    },
    {
      "id": "color_scheme",
      "type": "select",
      "label": "t:sections.product-recommendations.settings.color_scheme.label",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "t:sections.product-recommendations.settings.color_scheme.options__0.label"
        },
        {
          "value": "1",
          "label": "t:sections.product-recommendations.settings.color_scheme.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.product-recommendations.settings.color_scheme.options__2.label"
        },
        {
          "value": "3",
          "label": "t:sections.product-recommendations.settings.color_scheme.options__3.label"
        }
      ]
    },
    {
      "type": "group_header",
      "label": "t:sections.product-recommendations.settings.group_header__0.label"
    },
    {
      "id": "product_image_ratio",
      "type": "select",
      "label": "t:sections.product-recommendations.settings.product_image_ratio.label",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.product-recommendations.settings.product_image_ratio.options__0.label"
        },
        {
          "value": "100",
          "label": "t:sections.product-recommendations.settings.product_image_ratio.options__1.label"
        },
        {
          "value": "133.33",
          "label": "3:4"
        },
        {
          "value": "75",
          "label": "t:sections.product-recommendations.settings.product_image_ratio.options__3.label"
        },
        {
          "value": "150",
          "label": "t:sections.product-recommendations.settings.product_image_ratio.options__4.label"
        }
      ],
      "default": "150"
    },
    {
      "id": "product_fill_type",
      "type": "select",
      "label": "t:sections.product-recommendations.settings.product_fill_type.label",
      "options": [
        {
          "value": "contain",
          "label": "t:sections.product-recommendations.settings.product_fill_type.options__0.label"
        },
        {
          "value": "cover",
          "label": "t:sections.product-recommendations.settings.product_fill_type.options__1.label"
        }
      ],
      "default": "contain"
    },
    {
      "id": "show_secondary_image",
      "type": "switch",
      "label": "t:sections.product-recommendations.settings.show_secondary_image.label",
      "default": true
    },
    {
      "type": "group_header",
      "label": "t:sections.product-recommendations.settings.group_header__1.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.product-recommendations.settings.padding_top.label",
      "default": 80,
      "max": 100,
      "min": 0,
      "step": 2,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.product-recommendations.settings.padding_bottom.label",
      "default": 80,
      "max": 100,
      "min": 0,
      "step": 2,
      "unit": "px"
    }
  ],
  "max_blocks": 15,
  "blocks": [
    {
      "type": "image",
      "name": "t:sections.product-recommendations.blocks.image.name",
      "limit": 1,
      "settings": []
    },
    {
      "type": "title",
      "name": "t:sections.product-recommendations.blocks.title.name",
      "limit": 1,
      "settings": []
    },
    {
      "type": "price",
      "name": "t:sections.product-recommendations.blocks.price.name",
      "limit": 1,
      "settings": []
    },
    {
      "type": "highlight",
      "name": "t:sections.product-recommendations.blocks.highlight.name",
      "limit": 1,
      "settings": []
    },
    {
      "type": "text",
      "name": "t:sections.product-recommendations.blocks.text.name",
      "settings": [
        {
          "type": "text",
          "id": "text",
          "default": "Add some text information",
          "label": "t:sections.product-recommendations.blocks.text.settings.text.label"
        }
      ]
    },
    {
      "type": "divider",
      "name": "t:sections.product-recommendations.blocks.divider.name",
      "settings": []
    },
    {
      "type": "brand",
      "name": "t:sections.product-recommendations.blocks.brand.name",
      "limit": 1,
      "settings": []
    },
    {
      "type": "sku",
      "name": "t:sections.product-recommendations.blocks.sku.name",
      "limit": 1,
      "settings": []
    },
    {
      "type": "quick_add_button",
      "name": "t:sections.product-recommendations.blocks.quick_add_button.name",
      "limit": 1,
      "settings": []
    }
  ],
  "default": {
    "settings": {
      "pc_cols": 4,
      "mobile_cols": 2,
      "padding_top": 80,
      "padding_bottom": 80,
      "product_image_ratio": "150",
      "product_fill_type": "contain",
      "show_secondary_image": true,
      "color_scheme": "none",
      "products_to_show": 4,
      "title_size": "medium",
      "enable_horizontal_slider": false
    }
  }
}
{{/schema}}