{{snippet "stylesheet" href=(asset_url "component-tool-tip.css")}}
{{snippet "stylesheet" href=(asset_url "section-customer.css")}}
<script src="{{asset_url 'section-customer-lib-jquery.js'}}" defer></script>


<script src="{{asset_url 'section-customer-lib-air-datepicker.js'}}" defer></script>
<script src="{{asset_url 'section-customer-lib-rolldate.js'}}" defer></script>
{{snippet "stylesheet" href=(asset_url "section-customer-datepicker.css")}}
<script src="{{asset_url 'section-customer-datepicker.js'}}" defer></script>
<script src="{{asset_url 'component-tool-tip.js'}}" defer></script>
<script src="{{asset_url 'section-main-register.js'}}" defer></script>
<script src="{{asset_url 'component-phone-input.js'}}" defer></script>

<script>
  window.__I18N__ = window.__I18N__ || {};
  window.__I18N__['customer'] = window.__I18N__['customer'] || {};
  window.__I18N__['customer']['general'] = window.__I18N__['customer']['general'] || {{{ json (t 'customer.general') }}};
</script>

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}

<div class="customer register section-padding">
  {{assign "has_privacy_policy" shop.privacy_policy.body}}
  {{assign "has_terms_of_service" shop.terms_of_service.body}}

  <h1 class="title5 text-center customer-form-title">
    {{t "customer.general.sign_up"}}
  </h1>
  <div style="display: flex;flex-direction: column;align-items: center;">
    <div style="background-color: #FDF6EC;padding: 5px 20px;border-radius: 50px;margin: 0 auto 20px auto">
      <p style="margin: 0;">新規登録で<span style="color: #FF9400;font-weight: bold;">5,000ポイント</span>GET！</p>
    </div>
  </div>
  {{#form "create_customer" id="create-customer-form"}}
    {{assign "support_type_list" (split form.register_config.types ",")}}
    {{assign "has_switch" (if (inArray support_type_list "email") and (inArray support_type_list "mobile"))}}

    {{#if has_switch}}
      <div class="tab" id="create-customer-tab">
        <a class="body3 active" data-type="email">
          {{t "customer.account.email"}}
        </a>
        <a class="body3" data-type="mobile">
          {{t "customer.account.phone"}}
        </a>
      </div>
    {{/if}}

    {{#for support_type_list as |type|}}
      {{#if type == "email"}}
        <div class="field" data-type="email">
          {{snippet
            "input-email"
            input_class="field__input"
            input_id="RegisterEmail"
            input_name="customer[email]"
            input_required=true
            input_placeholder=(t "customer.general.email")
          }}
          <label for="RegisterEmail" class="field__label body3">
            {{t "customer.general.email"}}
          </label>
        </div>
      {{/if}}

      {{#if type == "mobile"}}
        <phone-input class="field {{#if has_switch}}display-none{{/if}}" data-type="mobile">
          {{snippet
            "input-phone"
            all_country_dialing_code=all_country_dialing_code
            input_name="customer[phone]"
            input_id="RegisterPhone"
            input_required=true
            code_name="customer[code]"
            country_iso_code=localization.country.iso_code
          }}
        </phone-input>
      {{/if}}
    {{/for}}

    <div class="field">
      <div class="field__container">
        <input
          type="password"
          class="field__input"
          id="Password"
          name="customer[password]"
          required
          placeholder="{{t 'customer.general.password'}}"
        />
        <label for="Password" class="field__label body3">
          {{t "customer.general.password"}}
        </label>
      </div>
      <div class="field__suffix">
        <button type="button" class="button button--link" id="toggle-password">
          {{snippet "icon-eye-invisible" id="pwd--show"}}
          {{snippet "icon-eye" class="display-none" id="pwd--hiden"}}
        </button>
      </div>
    </div>

    {{#if form.register_config.check_tag}}
      <div class="field">
        <div class="field__container">
          <input
            type="text"
            class="field__input"
            id="RegisterVerifyCode"
            name="customer[verifycode]"
            required
            placeholder="{{t 'customer.general.verification_code'}}"
          />
          <label for="RegisterVerifyCode" class="field__label body3">
            {{t "customer.general.verification_code"}}
          </label>
        </div>
        <div class="field__suffix">
          <button class="button button--link verifycode-button">
            {{t "customer.general.send"}}
          </button>
        </div>
      </div>
    {{/if}}

    <div class="field__group">
      {{#if form.register_config.first_name_check}}
        <div class="field">
          <input
            type="text"
            class="field__input"
            id="RegisterFirstName"
            name="customer[first_name]"
            placeholder="{{t 'customer.account.first_name'}}"
          />
          <label for="RegisterFirstName" class="field__label body3">
            {{t "customer.account.first_name"}}
          </label>
        </div>
      {{/if}}

      {{#if form.register_config.last_name_check}}
        <div class="field">
          <input
            type="text"
            class="field__input"
            id="RegisterLastName"
            name="customer[last_name]"
            placeholder="{{t 'customer.account.last_name'}}"
          />
          <label for="RegisterLastName" class="field__label body3">
            {{t "customer.account.last_name"}}
          </label>
        </div>
      {{/if}}
    </div>

    {{#if form.register_config.birthday_check}}
      <div class="field">
        <div class="field__container">
          <input
            class="field__input"
            name="customer[birthday]"
            id="RegisterBirthday"
            autocomplete="off"
            placeholder="{{t 'customer.account.birthday'}}"
          />
          <label for="RegisterBirthday" class="field__label body3">
            {{t "customer.account.birthday"}}
          </label>
        </div>
        <div class="field__suffix">
          {{snippet "icon-date"}}
        </div>
      </div>
    {{/if}}

    {{#if form.register_config.gender_check}}
      <div class="field">
        <select class="field__input" name="customer[gender]" placeholder="{{t 'customer.account.gender'}}">
          <option value="0">
            {{t "customer.account.gender_secret"}}
          </option>
          <option value="1">
            {{t "customer.account.gender_male"}}
          </option>
          <option value="2">
            {{t "customer.account.gender_female"}}
          </option>
        </select>
        <label for="birthday" class="field__label body3">
          {{t "customer.account.gender"}}
        </label>
        {{snippet "icon-arrow"}}
      </div>
    {{/if}}

    <p id="customer-error-message" class="body6"></p>

    <div>
      <button class="button submit" type="submit">
        {{t "customer.general.sign_up"}}
        {{snippet "loading-overlay-spinner"}}
      </button>
    </div>

    {{#if has_privacy_policy or has_terms_of_service}}
      <div class="actions">
        <label>
          <span class="field-checkbox">
            <input type="checkbox" name="agree" required />
            <span class="checkbox"></span>
            <tool-tip tip-position="bottom" controlled>
              {{t "customer.general.user_agreement_tip"}}
            </tool-tip>
          </span>
          <span class="body4">
            {{t "customer.register.tips_agree_with_the_shop"}}
            {{#if has_privacy_policy}}
              <a target="_blank" href="{{shop.privacy_policy.url}}">{{t "customer.register.privacy_policy"}}</a>
            {{/if}}
            {{#if has_terms_of_service}}
              <span>{{t "customer.general.and_button"}}</span>
              <a target="_blank" href="{{shop.terms_of_service.url}}">{{t "customer.register.terms_of_service"}}</a>
            {{/if}}
          </span>
        </label>
      </div>
    {{/if}}

    {{#if (inArray support_type_list "email")}}
      <div class="actions" data-type="email-marketing">
        <label>
          <span class="field-checkbox">
            <input type="checkbox" name="customer[accepts_marketing]" checked value="true" />
            <span class="checkbox"></span>
          </span>
          <span class="body4">
            {{t "unvisiable.customer.error_message_1"}}
          </span>
        </label>
      </div>
    {{/if}}
    <div class="backToSignIn">
      <a class="button button--link" href="{{routes.account_login_url}}">
        {{t "customer.register.go_to_login"}}
      </a>
    </div>

    {{#if form.register_config.company_account_application_entry_visible}}
      <div class="sign-up__company-register">
        <a href="{{routes.company_account_register_url}}" class="button button--link sign-up__link">{{t "customer.company.account.link"}}</a>
      </div>
    {{/if}}
  {{/form}}
</div>

{{#schema}}
{
  "name": "t:sections.main-register.name",
  "class": "section",
  "default": {
    "setting": {
      "padding_top": 80,
      "padding_bottom": 80
    }
  },
  "settings": [
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.main-register.settings.padding_top.label",
      "default": 80
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.main-register.settings.padding_bottom.label",
      "default": 80
    }
  ]
}
{{/schema}}