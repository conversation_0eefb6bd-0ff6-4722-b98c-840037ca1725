{{snippet "stylesheet" href=(asset_url "component-blog.css")}}
{{snippet "stylesheet" href=(asset_url "section-blog-list.css")}}
{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}
<main class="section section-padding page-width" style="--blog-list-columns:{{section.settings.columns}};">
  {{snippet "breadcrumb" class="text-left"}}
  <h1 class="blogslist__title title4">
    {{blog.title}}
  </h1>
  {{#paginate blog.articles by=section.settings.page_number}}
    <div class="bloglist__main">
      <div class="blogslist__page-list">
        <div class="blogslist__list-wrapper">
          <ul
            class="blogslist__list
              blogslist__list--{{section.settings.layout}}
              {{#if (eq section.settings.layout 'list') and (gt (length blog.articles) 2)}}blog-item-grid-list{{/if}}
              list-unstyled"
          >
            {{#for blog.articles as |article|}}
              <li class="blogslist__item hover-image-scale">
                {{snippet
                  "blog-card"
                  info=article
                  show_author=section.settings.is_show_author
                  show_date=section.settings.is_show_date
                  show_cover=section.settings.is_show_cover
                  cover_ratio=section.settings.cover_img_ratio
                  show_desc=section.settings.is_show_desc
                  shopline_attributes=article.shopline_attributes
                }}
              </li>
            {{/for}}
          </ul>
        </div>
        {{#if paginate.pages > 1}}
          {{snippet "pagination" paginate=paginate anchor=""}}
        {{/if}}
      </div>
    </div>
  {{/paginate}}

</main>
{{#schema}}
{
  "name": "t:sections.main-blog-list.name",
  "settings": [
    {
      "type": "select",
      "id": "layout",
      "default": "grid",
      "label": "t:sections.main-blog-list.settings.layout.label",
      "options": [
        {
          "label": "t:sections.main-blog-list.settings.layout.options__0.label",
          "value": "grid"
        },
        {
          "label": "t:sections.main-blog-list.settings.layout.options__1.label",
          "value": "list"
        }
      ]
    },
    {
      "type": "range",
      "id": "page_number",
      "label": "t:sections.main-blog-list.settings.page_number.label",
      "default": 4,
      "max": 24,
      "min": 3,
      "step": 1
    },
    {
      "type": "select",
      "id": "columns",
      "default": "2",
      "label": "t:sections.main-blog-list.settings.columns.label",
      "info": "t:sections.main-blog-list.settings.columns.info",
      "options": [
        {
          "label": "t:sections.main-blog-list.settings.columns.options__0.label",
          "value": "2"
        },
        {
          "label": "t:sections.main-blog-list.settings.columns.options__1.label",
          "value": "3"
        }
      ]
    },
    {
      "type": "switch",
      "id": "is_show_cover",
      "default": true,
      "label": "t:sections.main-blog-list.settings.is_show_cover.label"
    },
    {
      "type": "select",
      "id": "cover_img_ratio",
      "default": "medium",
      "label": "t:sections.main-blog-list.settings.cover_img_ratio.label",
      "options": [
        {
          "label": "t:sections.main-blog-list.settings.cover_img_ratio.options__0.label",
          "value": "auto"
        },
        {
          "label": "t:sections.main-blog-list.settings.cover_img_ratio.options__1.label",
          "value": "small"
        },
        {
          "label": "t:sections.main-blog-list.settings.cover_img_ratio.options__2.label",
          "value": "medium"
        },
        {
          "label": "t:sections.main-blog-list.settings.cover_img_ratio.options__3.label",
          "value": "large"
        }
      ]
    },
    {
      "type": "switch",
      "id": "is_show_date",
      "default": true,
      "label": "t:sections.main-blog-list.settings.is_show_date.label"
    },
    {
      "type": "switch",
      "id": "is_show_author",
      "default": true,
      "label": "t:sections.main-blog-list.settings.is_show_author.label"
    },
    {
      "type": "switch",
      "id": "is_show_desc",
      "default": true,
      "label": "t:sections.main-blog-list.settings.is_show_desc.label"
    },
    {
      "type": "group_header",
      "label": "t:sections.main-blog-list.settings.group_header__0.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.main-blog-list.settings.padding_top.label",
      "default": 60,
      "max": 100,
      "min": 0,
      "step": 2,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.main-blog-list.settings.padding_bottom.label",
      "default": 60,
      "max": 100,
      "min": 0,
      "step": 2,
      "unit": "px"
    }
  ],
  "default": {
    "settings": {
      "layout": "grid",
      "is_show_author": true,
      "is_show_date": true,
      "is_show_cover": true,
      "cover_img_ratio": "medium",
      "padding_top": 60,
      "padding-bottom": 60
    }
  }
}
{{/schema}}