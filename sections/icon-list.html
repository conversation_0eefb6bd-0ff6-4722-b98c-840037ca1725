<link rel="stylesheet" href="{{assets_url 'section-icon-list.css'}}" />

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}

{{#if request.design_mode}}
  <script src="{{asset_url 'theme-editor.js'}}" defer="defer"></script>
{{/if}}

<div class="icon-list page-width section-padding color-scheme-{{section.settings.color_scheme}}">
  <div class="icon-list__title title5">{{section.settings.title}}</div>
  <div class="icon-list__content__item-wrapper grid grid-cols-6-desktop mobile-slider-full-screen">
    {{#for section.blocks as |block|}}
      <a
        class="icon-list__content__item"
        {{#if block.settings.link}}
          href="{{block.settings.link}}"
        {{else}}
          href="javascript:;"
        {{/if}}
        {{{block.shopline_attributes}}}
      >
        <div class="icon-list__content__item-container">
          <div>
            {{#if block.settings.image}}
              {{snippet "image" data=block.settings.image}}
            {{else}}
              <div class="placeholder">
                {{placeholder_svg_tag "image"}}
              </div>
            {{/if}}
          </div>
        </div>
      </a>
    {{/for}}
  </div>
</div>

{{#schema}}
{
  "name": "t:sections.icon-list.name",
  "max_blocks": 12,
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.icon-list.settings.title.label",
      "default": "Popular brands"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "t:sections.icon-list.settings.color_scheme.label",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "t:sections.icon-list.settings.color_scheme.options__0.label"
        },
        {
          "value": "1",
          "label": "t:sections.icon-list.settings.color_scheme.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.icon-list.settings.color_scheme.options__2.label"
        },
        {
          "value": "3",
          "label": "t:sections.icon-list.settings.color_scheme.options__3.label"
        }
      ]
    },
    {
      "type": "group_header",
      "label": "t:sections.icon-list.settings.group_header__0.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.icon-list.settings.padding_top.label",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.icon-list.settings.padding_bottom.label",
      "default": 60
    }
  ],
  "blocks": [
    {
      "type": "icon",
      "name": "t:sections.icon-list.blocks.icon.name",
      "icon": "image",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.icon-list.blocks.icon.settings.image.label",
          "info": "t:sections.icon-list.blocks.icon.settings.image.info"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.icon-list.blocks.icon.settings.link.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "category_index": 4,
      "category": "t:sections.icon-list.presets.presets__0.category",
      "name": "t:sections.icon-list.presets.presets__0.name",
      "settings": {
        "title": "Popular brands",
        "color_scheme": "none",
        "padding_top": 60,
        "padding_bottom": 60
      },
      "blocks": [
        {
          "type": "icon",
          "icon": "image",
          "settings": {
            "image": {},
            "link": ""
          }
        },
        {
          "type": "icon",
          "icon": "image",
          "settings": {
            "image": {},
            "link": ""
          }
        },
        {
          "type": "icon",
          "icon": "image",
          "settings": {
            "image": {},
            "link": ""
          }
        },
        {
          "type": "icon",
          "icon": "image",
          "settings": {
            "image": {},
            "link": ""
          }
        },
        {
          "type": "icon",
          "icon": "image",
          "settings": {
            "image": {},
            "link": ""
          }
        },
        {
          "type": "icon",
          "icon": "image",
          "settings": {
            "image": {},
            "link": ""
          }
        }
      ]
    }
  ]
}
{{/schema}}