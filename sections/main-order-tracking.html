<style>
  button {
    cursor: pointer;
    border: none;
    background: none;
    padding: 0;
    font-family: inherit;
  }

  a {
    color: #0070C0;
    text-decoration: none;
  }

  a:hover {
    text-decoration: underline;
  }

  h2,
  h3,
  p {
    margin: 0;
  }

  ul {
    list-style: none;
    padding: 0;
    margin: 0;
  }

  /* 变量定义 */
  :root {
    --primary-color: #37A89F;
    --primary-border-color: #399E96;
    --text-color-primary: #000000;
    --text-color-secondary: #909090;
    --text-color-light: #5F5F5F;
    --text-color-link: #0070C0;
    --text-color-white: #FFFFFF;
    --background-light-gray: #F9F9F9;
    --border-color-gray: #E6E6E6;
    --border-color-medium-gray: #CDCDCD;
    --border-color-dark-gray: #D8D8D9;
    --timeline-incomplete-color: #D9D9D9;
    --font-inter: 'Inter', sans-serif;
    --font-noto-sans: 'Noto Sans', sans-serif;
    --font-noto-sans-adlam: 'Noto Sans Adlam', 'Noto Sans', sans-serif;
  }

  .loading-spinner {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 1000;
    justify-content: center;
    align-items: center;
  }

  .loading-spinner.show {
    display: flex;
  }

  .spinner {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    border: 4px solid var(--border-color-gray);
    border-top-color: var(--primary-color);
    animation: spin 1s linear infinite;
  }

  @keyframes spin {
    0% {
      transform: rotate(0deg);
    }

    100% {
      transform: rotate(360deg);
    }
  }

  .hide-until-loaded {
    display: none !important;
  }

  .hide-until-loaded.show {
    display: block !important;
  }

  /* 保持特定元素的原始布局 */
  .shipping-info.hide-until-loaded.show {
    display: flex !important;
  }

  /* 页面包裹容器 */
  .page-wrapper {
    font-family: 'Noto Sans', 'Inter', sans-serif;
    margin: 0;
    background-color: #fff;
    color: #000000;
    margin: 0 auto;
    padding: 0 0 32px 0;
    box-sizing: border-box;
  }

  .page-divider {
    border: none;
    border-top: 1px solid rgba(230, 230, 230, 0.5);
    margin: 24px 0;
    width: 100%;
  }

  .page-divider.line-two {
    margin: 20px 20px;
    width: calc(100% - 40px);
  }

  @media (min-width: 768px) {
    .page-wrapper {
      max-width: 672px;
    }
  }

  /* 订单头部 */
  .order-header {
    padding: 0 20px;
    margin: 0 auto;
    max-width: 393px;
  }

  .order-info {
    margin-bottom: 16px;
  }

  .order-info span {
    font-family: var(--font-inter);
    font-weight: 700;
    font-size: 15px;
    line-height: 18px;
    color: var(--text-color-primary);
  }

  /* PC端订单头部样式 */
  @media (min-width: 960px) {
    .order-header {
      max-width: 600px;
      padding: 0 40px;
    }

    .order-info {
      margin-bottom: 20px;
    }

    .order-info span {
      font-size: 16px;
      line-height: 20px;
    }
  }

  /* 包裹多包裹tab的容器 */
  .package-tabs-wrapper {
    text-align: left;
    width: 100%;
    margin-bottom: 12px;
  }

  @media (min-width: 960px) {
    .package-tabs-wrapper {
      margin-bottom: 16px;
    }
  }

  .package-tabs {
    display: inline-flex;
    flex-direction: row;
    align-items: center;
    justify-content: flex-start;
    gap: 5px;
    overflow-x: auto;
    white-space: nowrap;
    max-width: 100%;
  }

  .package-tabs button {
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 3px;
    gap: 10px;
    position: relative;
    width: 76px;
    height: 76px;
    border: 3px solid transparent;
    border-radius: 9px;
    background: var(--text-color-white);
  }

  .package-tabs button.active {
    border: 3px solid var(--primary-border-color);
  }

  .package-tabs button .package-image {
    box-sizing: border-box;
    width: 64px;
    height: 64px;
    background-position: center;
    background-size: cover;
    background-repeat: no-repeat;
    border: 1px solid var(--border-color-medium-gray);
    border-radius: 5px;
  }

  .package-tabs button .package-number {
    position: absolute;
    top: 0;
    right: 0;
    background-color: var(--primary-color);
    color: var(--text-color-white);
    border-radius: 50%;
    width: 20px;
    height: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 12px;
    line-height: 1;
    transform: translate(25%, -25%);
  }

  .shipping-info {
    position: relative;
    width: 352px;
    height: 42px;
    margin: 20px auto;
  }

  .shipping-info>img {
    position: absolute;
    width: 60px;
    height: 20px;
    left: 0;
    top: 0;
    object-fit: contain;
  }

  .shipping-info .tracking-number-container {
    position: absolute;
    left: 72px;
    top: 3px;
    display: flex;
    align-items: center;
  }

  .shipping-info .number-container {
    font-family: var(--font-inter);
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 15px;
    color: var(--text-color-primary);
    text-align: center;
  }

  .shipping-info .copy-button {
    margin-left: 8px;
    border: none;
    background: none;
    cursor: pointer;
    padding: 0;
    display: flex;
    align-items: center;
  }

  .shipping-info #contact-link {
    position: absolute;
    right: 0;
    top: 3px;
    font-family: var(--font-inter);
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 15px;
    text-decoration-line: underline;
    color: var(--primary-color);
  }

  .shipping-info .tracking-link {
    position: absolute;
    left: 0;
    top: 27px;
    font-family: var(--font-inter);
    font-style: normal;
    font-weight: 400;
    font-size: 12px;
    line-height: 15px;
    text-decoration-line: underline;
    color: var(--text-color-link);
  }

  /* PC端商品信息样式 */
  @media (min-width: 960px) {
    .shipping-info {
      width: 520px;
      height: 50px;
      margin: 30px auto;
    }

    .shipping-info>img {
      width: 80px;
      height: 26px;
    }

    .shipping-info .tracking-number-container {
      left: 100px;
      top: 5px;
    }

    .shipping-info .number-container {
      font-size: 14px;
      line-height: 17px;
    }

    .shipping-info .copy-button {
      margin-left: 10px;
    }

    .shipping-info .copy-button img {
      width: 14px;
      height: 12px;
    }

    .shipping-info #contact-link {
      right: 0;
      top: 5px;
      font-size: 14px;
      line-height: 17px;
    }

    .shipping-info .tracking-link {
      left: 0;
      top: 32px;
      font-size: 14px;
      line-height: 17px;
    }
  }

  /* 产品区块 */
  .product-section {
    margin-top: 12px;
    position: relative;
  }

  .product-section h2 {
    font-family: var(--font-noto-sans);
    font-weight: 700;
    font-size: 15px;
    line-height: 20px;
    color: #29252C;
    margin: 0;
    margin-bottom: 31px;
  }

  .product-item {
    display: flex;
    gap: 14px;
    align-items: flex-start;
  }

  @media (min-width: 960px) {
    .product-section {
      margin-top: 16px;
    }

    .product-section h2 {
      font-size: 16px;
      line-height: 22px;
      margin-bottom: 35px;
    }

    .product-item {
      gap: 18px;
    }
  }

  .product-grid {
    flex-grow: 1;
    display: grid;
    grid-template-columns: 1fr auto;
    grid-template-rows: auto auto;
    gap: 6px 10px;
    width: 100%;
  }

  @media (min-width: 960px) {
    .product-grid {
      gap: 8px 16px;
    }
  }

  .product-name {
    font-family: var(--font-noto-sans);
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 19px;
    color: var(--text-color-primary);
    grid-column: 1;
    grid-row: 1;
  }

  .product-quantity {
    font-family: var(--font-noto-sans);
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 19px;
    text-align: right;
    color: var(--text-color-secondary);
    grid-column: 2;
    grid-row: 1;
  }

  .product-options {
    font-family: var(--font-noto-sans);
    font-style: normal;
    font-weight: 500;
    font-size: 12px;
    line-height: 16px;
    color: var(--text-color-secondary);
    grid-column: 1;
    grid-row: 2;
  }

  .product-price {
    font-family: var(--font-inter);
    font-style: normal;
    font-weight: 700;
    font-size: 14px;
    line-height: 17px;
    text-align: right;
    color: var(--text-color-primary);
    grid-column: 2;
    grid-row: 2;
  }

  @media (min-width: 960px) {
    .product-name {
      font-size: 16px;
      line-height: 22px;
    }

    .product-quantity {
      font-size: 16px;
      line-height: 22px;
    }

    .product-options {
      font-size: 14px;
      line-height: 18px;
    }

    .product-price {
      font-size: 16px;
      line-height: 19px;
    }
  }

  /* 配送区块 */
  .delivery-section {
    margin: 20px auto 0 auto;
    padding: 0 18px;
    max-width: 393px;
  }

  .delivery-section h2 {
    font-family: var(--font-noto-sans);
    font-weight: 700;
    font-size: 15px;
    line-height: 20px;
    color: var(--text-color-primary);
    margin-bottom: 10px;
  }

  /* 时间轴 */
  .timeline {
    padding-left: 11px;
    position: relative;
  }

  .timeline-item {
    position: relative;
    padding-left: 24px;
    padding-bottom: 40px;
    min-height: 22px;
  }

  .timeline-item:last-child {
    padding-bottom: 0;
  }

  .timeline-marker {
    position: absolute;
    left: 0;
    top: 1px;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    background: var(--timeline-incomplete-color);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
  }

  .timeline-item::before {
    /* 时间轴竖线 */
    content: '';
    position: absolute;
    left: 10px;
    /* 圆点上下留白，头尾间距 */
    top: 28px;
    bottom: 4px;
    width: 2px;
    background: var(--timeline-incomplete-color);
    border-radius: 1px;
    z-index: 0;
  }

  .timeline-item:last-child::before {
    display: none;
  }

  /* 已完成节点打勾 */
  .timeline-item.completed .timeline-marker::after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background-image: url('{{asset_url "icon-check.svg"}}');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  /* 最后一个已完成节点使用包裹图标 */
  .timeline-item.completed.last-completed .timeline-marker::after {
    mask: url('{{asset_url "icon-package2.svg"}}') no-repeat center/contain;
    background-color: #D9D9D9;
  }

  .timeline-item.completed.last-completed .timeline-status {
    color: var(--primary-color);
    font-weight: 700;
    font-size: 15px;
    line-height: 18px;
  }

  .timeline-item.current .timeline-marker {
    background: var(--primary-color);
  }

  /* 当前节点高亮 */
  .timeline-item.current .timeline-marker::after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 12px;
    height: 12px;
    background-image: url('{{asset_url "icon-check.svg"}}');
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  /* 当前节点为最后一个节点且已完成时使用包裹图标 */
  .timeline-item.current.last-completed .timeline-marker {
    background-color: unset;
  }

  .timeline-item.current.last-completed .timeline-marker::after {
    mask: url('{{asset_url "icon-package2.svg"}}') no-repeat center/contain;
    background-color: #399E96;
    background-image: unset;
    width: 22px;
    height: 22px;
  }

  .timeline-content {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding-top: 1px;
    margin-left: 13px;
  }

  .timeline-status {
    font-family: var(--font-noto-sans-adlam);
    font-weight: 400;
    font-size: 15px;
    line-height: 20px;
    color: var(--text-color-primary);
    margin-right: 10px;
  }

  .timeline-time {
    font-family: var(--font-noto-sans-adlam);
    font-weight: 400;
    font-size: 11px;
    line-height: 15px;
    color: var(--text-color-light);
    text-align: right;
    white-space: nowrap;
    padding-top: 2px;
  }

  .timeline-item.current {
    padding-bottom: 40px;
  }

  .timeline-marker-package {
    position: absolute;
    left: 0;
    top: 1px;
    width: 22px;
    height: 22px;
    border-radius: 50%;
    background: unset;
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1;
  }

  /* 预计送达时间的时间轴样式 */
  .timeline-item-estimated .timeline-marker-package::after {
    content: '';
    display: block;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 22px;
    height: 22px;
    mask: url('{{asset_url "icon-package2.svg"}}') no-repeat center/contain;
    background-color: #D9D9D9;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
  }

  /* 预计送达时间的虚线样式 - SVG方法 */
  .timeline-item-estimated::before {
    content: '';
    position: absolute;
    left: 10px;
    top: 28px;
    bottom: 4px;
    width: 2px;
    background: none;
    border-left: none;
    background-image: url("data:image/svg+xml,%3Csvg width='2' height='100%25' xmlns='http://www.w3.org/2000/svg'%3E%3Cline x1='1' y1='0' x2='1' y2='100%25' stroke='%23D9D9D9' stroke-width='2' stroke-dasharray='5 3' stroke-linecap='round'/%3E%3C/svg%3E");
    background-repeat: repeat-y;
  }

  .timeline-item-estimated .timeline-status {
    position: relative;
    font-family: 'Inter';
    font-style: normal;
    font-weight: 400;
    font-size: 15px;
    line-height: 18px;
    color: #909090;
  }

  .timeline-item-estimated .timeline-status .estimated-date {
    color: #399E96;
    font-variant-numeric: tabular-nums;
    font-weight: 700;
    font-size: 15px;
    line-height: 18px;
  }

  /* 响应式布局 */
  @media (min-width: 768px) {
    .page-wrapper {
      padding: 32px 24px 48px 24px;
    }

    /* PC端的loading样式优化 */
    .loading-spinner {
      position: absolute;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      width: 120px;
      height: 120px;
      border-radius: 10px;
      background-color: rgba(255, 255, 255, 0.9);
      box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
    }

    .spinner {
      width: 50px;
      height: 50px;
      border-width: 5px;
    }

    /* 添加文字提示 */
    .loading-spinner::after {
      content: "読み込み中...";
      position: absolute;
      bottom: 20px;
      font-size: 14px;
      color: var(--text-color-secondary);
      font-family: var(--font-noto-sans);
    }
  }

  /* Popper气泡提示样式 */
  .tooltip-text {
    display: none;
    width: 228px;
    background-color: #FFFFFF;
    color: #909090;
    text-align: left;
    border-radius: 5px;
    padding: 10px 15px;
    box-shadow: 0px 1px 4px rgba(0, 0, 0, 0.25);
    font-size: 10px;
    line-height: 12px;
    z-index: 1000;
  }

  .tooltip-text.show {
    display: block;
  }

  #arrow,
  #arrow::before,
  #arrow-timeline,
  #arrow-timeline::before {
    position: absolute;
    width: 8px;
    height: 8px;
    background: inherit;
  }

  #arrow,
  #arrow-timeline {
    visibility: hidden;
  }

  #arrow::before,
  #arrow-timeline::before {
    content: '';
    visibility: visible;
    border: 1px solid rgba(0, 0, 0, 0.1);
    background: white;
  }

  #tooltip-content-1[data-popper-placement^='top']>#arrow,
  #tooltip-content-timeline[data-popper-placement^='top']>#arrow-timeline {
    bottom: -4px;
  }

  #tooltip-content-1[data-popper-placement^='top']>#arrow::before,
  #tooltip-content-timeline[data-popper-placement^='top']>#arrow-timeline::before {
    transform: rotate(45deg);
    border-top-color: transparent;
    border-left-color: transparent;
  }

  #tooltip-content-1[data-popper-placement^='bottom']>#arrow,
  #tooltip-content-timeline[data-popper-placement^='bottom']>#arrow-timeline {
    top: -4px;
  }

  #tooltip-content-1[data-popper-placement^='bottom']>#arrow::before,
  #tooltip-content-timeline[data-popper-placement^='bottom']>#arrow-timeline::before {
    transform: rotate(225deg);
    border-top-color: transparent;
    border-left-color: transparent;
  }

  #tooltip-content-1[data-popper-placement^='left']>#arrow,
  #tooltip-content-timeline[data-popper-placement^='left']>#arrow-timeline {
    right: -4px;
  }

  #tooltip-content-1[data-popper-placement^='left']>#arrow::before,
  #tooltip-content-timeline[data-popper-placement^='left']>#arrow-timeline::before {
    transform: rotate(135deg);
    border-top-color: transparent;
    border-left-color: transparent;
  }

  #tooltip-content-1[data-popper-placement^='right']>#arrow,
  #tooltip-content-timeline[data-popper-placement^='right']>#arrow-timeline {
    left: -4px;
  }

  #tooltip-content-1[data-popper-placement^='right']>#arrow::before,
  #tooltip-content-timeline[data-popper-placement^='right']>#arrow-timeline::before {
    transform: rotate(315deg);
    border-top-color: transparent;
    border-left-color: transparent;
  }

  .tooltip-container {
    color: var(--text-color-secondary);
  }

  .tooltip-container .tooltip-text::before,
  .tooltip-container .tooltip-text::after {
    content: none;
    display: none;
  }

  /* iOS弹窗样式 */
  .ios-alert-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.4);
    display: none;
    justify-content: center;
    align-items: center;
    z-index: 1100;
    opacity: 0;
    transition: opacity 0.2s ease-in-out;
  }

  .ios-alert-overlay.show {
    display: flex;
    opacity: 1;
  }

  .ios-alert-box {
    background-color: rgba(248, 248, 248);
    border-radius: 14px;
    width: 270px;
    max-width: 90%;
    text-align: center;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    transform: scale(1.1);
    transition: transform 0.2s ease-in-out;
  }

  .ios-alert-overlay.show .ios-alert-box {
    transform: scale(1);
  }

  .ios-alert-title {
    font-size: 17px;
    font-weight: 600;
    padding: 18px 16px 5px 16px;
    color: #000;
  }

  .ios-alert-message {
    font-size: 13px;
    padding: 0px 16px 18px 16px;
    color: #000;
    line-height: 1.4;
    max-height: 50vh;
    /* 限制最大高度 */
    overflow-y: auto;
    /* 内容过多时可滚动 */
  }

  .ios-alert-message div {
    text-align: left;
  }

  .ios-alert-message strong {
    display: block;
    margin-top: 5px;
    font-weight: 500;
  }

  /* 多联系方式列表样式 */
  .ios-alert-message div.contact-item {
    margin-top: 10px;
    text-align: left;
    padding: 5px 0;
  }

  .ios-alert-buttons {
    display: flex;
    width: 100%;
    border-top: 0.5px solid rgba(60, 60, 67, 0.36);
    flex-direction: row;
    /* 默认水平排列 */
  }

  .ios-alert-button {
    flex: 1;
    padding: 12px 5px;
    font-size: 17px;
    background-color: transparent;
    border: none;
    cursor: pointer;
    color: #007AFF;
    outline: none;
    -webkit-tap-highlight-color: rgba(0, 0, 0, 0);
  }

  /* 水平排列时的分隔线 */
  .ios-alert-buttons[style*="flex-direction: row"] .ios-alert-button:not(:first-child),
  .ios-alert-buttons:not([style*="flex-direction"]) .ios-alert-button:not(:first-child) {
    border-left: 0.5px solid rgba(60, 60, 67, 0.36);
  }

  /* 垂直排列时不需要左边框 */
  .ios-alert-buttons[style*="flex-direction: column"] .ios-alert-button:not(:first-child) {
    border-left: none;
  }

  .ios-alert-button-call {
    font-weight: 600;
  }

  .ios-alert-button:active {
    background-color: rgba(220, 220, 220, 0.9);
  }

  .number-container {
    font-variant-numeric: tabular-nums;
  }
</style>

<script src="{{asset_url 'popper.min.js'}}" defer></script>

<div class="loading-spinner show">
  <div class="spinner"></div>
</div>

<script>
  (function () {
    const loadingSpinner = document.querySelector('.loading-spinner');
    if (loadingSpinner && !loadingSpinner.classList.contains('show')) {
      loadingSpinner.classList.add('show');
    }
  })();
</script>

<div class="page-wrapper">
  <hr class="page-divider">

  <!-- 1. 订单头部信息 -->
  <section class="order-header">
    <div class="order-info">
      <span>NO. #</span>
    </div>
    <div class="package-tabs-wrapper hide-until-loaded">
      <div class="package-tabs"></div>
      <section class="product-section hide-until-loaded">
        <div class="product-item">
          <div class="product-grid">
            <div class="product-name"></div>
            <div class="product-quantity"></div>
            <div class="product-options"></div>
            <div class="product-price"></div>
          </div>
        </div>
      </section>
    </div>
  </section>

  <hr class="page-divider line-two">

  <!-- 2. 商品信息 -->
  <div class="shipping-info hide-until-loaded">
    <img src="" alt="Shipping Carrier">
    <div class="tracking-number-container">
      <span class="number-container"></span>
      <button class="copy-button">
        <img width="12" height="10" src="{{asset_url 'copy.svg'}}" alt="Copy">
      </button>
    </div>
    <a href="#" id="contact-link">お問い合わせ＞</a>
    <a href="#" class="tracking-link">追跡情報を確認＞</a>
  </div>

  <!-- 3. 配送状况 -->
  <section class="delivery-section hide-until-loaded">
    <h2>配送状況</h2>

    <ul class="timeline"></ul>
  </section>

</div>

<div id="ios-alert-modal" class="ios-alert-overlay">
  <div class="ios-alert-box">
    <div class="ios-alert-title">お問い合わせ</div>
    <div class="ios-alert-message">
      <div>以下の番号にお問い合わせください：</div><strong class="number-container"></strong>
    </div>
    <div class="ios-alert-buttons">
      <button id="ios-alert-cancel" class="ios-alert-button ios-alert-button-cancel">キャンセル</button>
      <button id="ios-alert-call" class="ios-alert-button ios-alert-button-call">発信</button>
    </div>
  </div>
</div>

<div id="copy-alert-modal" class="ios-alert-overlay">
  <div class="ios-alert-box">
    <div class="ios-alert-title">コピー完了</div>
    <div class="ios-alert-message">追跡番号がクリップボードにコピーされました</div>
    <div class="ios-alert-buttons">
      <button id="copy-alert-ok" class="ios-alert-button ios-alert-button-call">OK</button>
    </div>
  </div>
</div>

<script>
  document.addEventListener('DOMContentLoaded', function () {
    const trigger = document.getElementById('tooltip-trigger-1');
    const tooltip = document.getElementById('tooltip-content-1');
    const arrowElement = document.getElementById('arrow');
    let popperInstance = null;

    function createPopperInstance() {
      if (popperInstance) {
        popperInstance.destroy();
        popperInstance = null;
      }
      popperInstance = Popper.createPopper(trigger, tooltip, {
        placement: 'top',
        modifiers: [
          {
            name: 'offset',
            options: {
              offset: [0, 10],
            },
          },
          {
            name: 'arrow',
            options: {
              element: arrowElement,
              padding: 5,
            },
          },
          {
            name: 'preventOverflow',
            options: {
              padding: 10,
            },
          }
        ],
      });
      tooltip.classList.add('show');
    }

    function destroyPopperInstance() {
      if (popperInstance) {
        tooltip.classList.remove('show');
        popperInstance.destroy();
        popperInstance = null;
      }
    }

    trigger.addEventListener('click', (event) => {
      event.stopPropagation();
      if (popperInstance) {
        destroyPopperInstance();
      } else {
        createPopperInstance();
      }
    });

    document.addEventListener('click', (event) => {
      if (tooltip && !tooltip.contains(event.target) && trigger && !trigger.contains(event.target)) {
        destroyPopperInstance();
      }
    });

    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        destroyPopperInstance();
      }
    });
  });

  let contactPhoneNumbers = [];

  window.updateContactInfo = function (sku) {
    const contactLink = document.getElementById('contact-link');
    contactPhoneNumbers = [];

    if (sku.channelContacts && sku.channelContacts.length > 0) {
      if (contactLink) {
        contactLink.style.display = 'block';
      }

      // 存储所有联系方式
      contactPhoneNumbers = sku.channelContacts.map(contact => ({
        usageCode: contact.usageCode,
        usageDescription: contact.usageDescription || '問い合わせ',
        phoneNumber: contact.phoneNumber
      }));

      // 更新弹窗内容
      updateContactModal();
    } else {
      // 兼容旧版API，检查是否有单个联系电话
      if (sku.channelContact) {
        if (contactLink) {
          contactLink.style.display = 'block';
        }
        contactPhoneNumbers = [{
          usageCode: 'DEFAULT',
          usageDescription: '問い合わせ',
          phoneNumber: sku.channelContact
        }];

        // 更新弹窗内容
        updateContactModal();
      } else {
        // 如果没有联系电话，隐藏联系链接
        if (contactLink) {
          contactLink.style.display = 'none';
        }
      }
    }
  };

  // 更新联系弹窗内容
  function updateContactModal() {
    const messageContainer = document.querySelector('.ios-alert-message');
    const buttonsContainer = document.querySelector('.ios-alert-buttons');

    if (!messageContainer || !buttonsContainer) return;

    if (contactPhoneNumbers.length === 1) {
      // 单个联系电话的情况 - 保持原有布局
      messageContainer.innerHTML = '<div>以下の番号にお問い合わせください：</div><strong class="number-container">' + contactPhoneNumbers[0].phoneNumber + '</strong>';

      buttonsContainer.innerHTML = `
        <button id="ios-alert-cancel" class="ios-alert-button ios-alert-button-cancel">キャンセル</button>
        <button id="ios-alert-call" class="ios-alert-button ios-alert-button-call" data-phone="${contactPhoneNumbers[0].phoneNumber}">発信</button>
      `;

      // 重新绑定事件
      document.getElementById('ios-alert-cancel').addEventListener('click', function () {
        document.getElementById('ios-alert-modal').classList.remove('show');
      });

      document.getElementById('ios-alert-call').addEventListener('click', function () {
        const phone = this.getAttribute('data-phone');
        window.location.href = 'tel:' + phone;
        document.getElementById('ios-alert-modal').classList.remove('show');
      });
    } else {
      // 多个联系电话的情况 - 显示列表
      let messageHTML = '<div>以下の番号にお問い合わせください：</div>';
      let buttonsHTML = '';

      contactPhoneNumbers.forEach((contact, index) => {
        // 添加联系方式描述和电话号码
        messageHTML += `<div class="contact-item">
          <div style="font-weight: 600;">${contact.usageDescription}</div>
          <div class="number-container" style="font-weight: 500;">${contact.phoneNumber}</div>
        </div>`;

        // 为每个联系方式添加按钮
        buttonsHTML += `<button class="ios-alert-button ios-alert-button-call" data-phone="${contact.phoneNumber}" style="border-top: 0.5px solid rgba(60, 60, 67, 0.36);">
          ${contact.usageDescription}に発信
        </button>`;
      });

      // 添加取消按钮
      buttonsHTML += `<button id="ios-alert-cancel" class="ios-alert-button ios-alert-button-cancel" style="border-top: 0.5px solid rgba(60, 60, 67, 0.36);">キャンセル</button>`;

      messageContainer.innerHTML = messageHTML;
      buttonsContainer.innerHTML = buttonsHTML;

      // 重新设置按钮容器样式为纵向排列
      buttonsContainer.style.flexDirection = 'column';

      // 重新绑定事件
      document.querySelectorAll('.ios-alert-button-call').forEach(button => {
        button.addEventListener('click', function () {
          const phone = this.getAttribute('data-phone');
          window.location.href = 'tel:' + phone;
          document.getElementById('ios-alert-modal').classList.remove('show');
        });
      });

      document.getElementById('ios-alert-cancel').addEventListener('click', function () {
        document.getElementById('ios-alert-modal').classList.remove('show');
      });
    }
  };

  document.addEventListener('DOMContentLoaded', function () {
    // 电话联系弹窗处理
    const contactLink = document.getElementById('contact-link');
    const modal = document.getElementById('ios-alert-modal');

    if (contactLink && modal) {
      contactLink.addEventListener('click', function (event) {
        event.preventDefault();

        // 重置按钮容器样式（以防之前被修改）
        const buttonsContainer = document.querySelector('.ios-alert-buttons');
        if (buttonsContainer) {
          buttonsContainer.style.flexDirection = contactPhoneNumbers.length > 1 ? 'column' : 'row';
        }

        modal.classList.add('show');
      });

      modal.addEventListener('click', function (event) {
        if (event.target === modal) {
          modal.classList.remove('show');
        }
      });
    }

    // 复制追踪号码弹窗处理
    const copyModal = document.getElementById('copy-alert-modal');
    const copyOkButton = document.getElementById('copy-alert-ok');

    if (copyModal && copyOkButton) {
      copyOkButton.addEventListener('click', function () {
        copyModal.classList.remove('show');
      });

      copyModal.addEventListener('click', function (event) {
        if (event.target === copyModal) {
          copyModal.classList.remove('show');
        }
      });
    }

    // 全局 ESC 键处理
    document.addEventListener('keydown', (event) => {
      if (event.key === 'Escape') {
        // 关闭所有可能打开的弹窗
        const allModals = document.querySelectorAll('.ios-alert-overlay');
        allModals.forEach(modal => {
          if (modal.classList.contains('show')) {
            modal.classList.remove('show');
          }
        });
      }
    });

    // 全局复制函数
    window.showCopySuccessModal = function (text) {
      if (copyModal) {
        copyModal.classList.add('show');

        // 3秒后自动关闭
        setTimeout(() => {
          copyModal.classList.remove('show');
        }, 3000);
      }
    };
  });

  // 全局变量
  let orderInfoElement;
  let packageTabsContainer;
  let shippingInfoContainer;
  let productSectionContainer;
  let timelineContainer;

  // 货币代码到符号的映射
  function getCurrencySymbol(currencyCode) {
    const currencySymbols = {
      'JPY': '¥',
      'USD': '$',
      'EUR': '€',
      'GBP': '£',
      'CNY': '¥',
      'KRW': '₩',
      'THB': '฿',
      'SGD': 'S$',
      'MYR': 'RM',
      'IDR': 'Rp',
      'PHP': '₱',
      'VND': '₫',
      'HKD': 'HK$',
      'TWD': 'NT$',
      'AUD': 'A$',
      'CAD': 'C$',
      'NZD': 'NZ$'
    };

    return currencySymbols[currencyCode] || currencyCode;
  }

  // 格式化数字，添加千分位分隔符
  function formatNumberWithCommas(number) {
    return number.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ",");
  }

  // 显示加载状态
  function showLoading() {
    const loadingSpinner = document.querySelector('.loading-spinner');
    if (loadingSpinner && !loadingSpinner.classList.contains('show')) {
      loadingSpinner.classList.add('show');
    }
  }

  // 隐藏加载状态
  function hideLoading() {
    const loadingSpinner = document.querySelector('.loading-spinner');
    if (loadingSpinner) {
      loadingSpinner.classList.remove('show');
    }
  }

  // 显示错误信息
  function showError(message) {
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    errorElement.style.color = 'red';
    errorElement.style.padding = '10px';
    errorElement.style.margin = '10px 0';
    errorElement.style.textAlign = 'center';
    errorElement.textContent = message;

    const pageWrapper = document.querySelector('.page-wrapper');
    if (pageWrapper) {
      pageWrapper.insertBefore(errorElement, pageWrapper.firstChild);
    }
  }

  // 渲染订单跟踪数据
  function renderOrderTrackingData(data) {
    if (orderInfoElement && data.orderNo) {
      orderInfoElement.textContent = `NO. #${data.orderNo}`;
    }

    if (!data.skus || data.skus.length === 0) {
      return;
    }

    document.querySelectorAll('.hide-until-loaded').forEach(element => {
      element.classList.add('show');
    });

    if (packageTabsContainer && data.skus.length > 0) {
      renderPackageTabs(data.skus);
    }

    renderSkuData(data.skus[0]);
  }

  // 渲染包裹标签
  function renderPackageTabs(skus) {
    packageTabsContainer.innerHTML = '';
    const numbers = ['①', '②', '③', '④', '⑤', '⑥', '⑦', '⑧', '⑨', '⑩', '⑪', '⑫', '⑬', '⑭', '⑮', '⑯', '⑰', '⑱', '⑲', '⑳', '㉑', '㉒', '㉓', '㉔', '㉕', '㉖', '㉗', '㉘', '㉙', '㉚', '㉛', '㉜', '㉝', '㉞', '㉟', '㊱', '㊲', '㊳', '㊴', '㊵', '㊶', '㊷', '㊸', '㊹', '㊺', '㊻', '㊼', '㊽', '㊾', '㊿'];

    skus.forEach((sku, index) => {
      const button = document.createElement('button');
      button.className = index === 0 ? 'active' : '';
      button.dataset.skuIndex = index;

      const imageContainer = document.createElement('div');
      imageContainer.className = 'package-image';

      if (sku.skuImage) {
        imageContainer.style.backgroundImage = `url(${sku.skuImage})`;
      } else {
        imageContainer.style.backgroundColor = '#f5f5f5';
      }

      button.appendChild(imageContainer);

      button.addEventListener('click', function () {
        packageTabsContainer.querySelectorAll('button').forEach(btn => {
          btn.classList.remove('active');
        });

        this.classList.add('active');

        renderSkuData(skus[index]);
      });

      packageTabsContainer.appendChild(button);
    });
  }

  // 渲染SKU数据
  function renderSkuData(sku) {
    const logoMap = {
      "SAGAWA": "{{asset_url 'logo-sagawa.png'}}",
      "YAMATO": "{{asset_url 'logo-dahe.png'}}",
    };
    if (shippingInfoContainer) {
      console.log('sku.expressNo: ', sku.expressNo);
      if (!sku.expressNo) {
        shippingInfoContainer.style.setProperty('display', 'none', 'important');
      } else {
        shippingInfoContainer.style.setProperty('display', 'flex', 'important');
      }
      const carrierLogoImg = shippingInfoContainer.querySelector('img');
      const trackingNumberElement = shippingInfoContainer.querySelector('.number-container');
      const trackingLinkElement = shippingInfoContainer.querySelector('.tracking-link');
      const contactLinkElement = shippingInfoContainer.querySelector('#contact-link');
      const copyButton = shippingInfoContainer.querySelector('.copy-button');

      let logoSrc = '';
      let carrierName = '';
      if (carrierLogoImg) {
        if (sku.channelCode) {
          carrierLogoImg.style.display = 'block';
          logoSrc = logoMap[sku.channelCode.toUpperCase()];
          carrierName = sku.channelCode;
        } else {
          carrierLogoImg.style.display = 'none';
        }
        carrierLogoImg.src = logoSrc || '';
        carrierLogoImg.alt = carrierName;
      }

      if (trackingNumberElement && sku.expressNo) {
        trackingNumberElement.textContent = sku.expressNo;
        trackingNumberElement.dataset.trackingNumber = sku.expressNo;

        if (copyButton) {
          copyButton.addEventListener('click', function () {
            navigator.clipboard.writeText(sku.expressNo)
              .then(() => {
                window.showCopySuccessModal(sku.expressNo);
              })
              .catch(err => {
                console.error('复制失败:', err);
              });
          });
        }
      }

      if (trackingLinkElement && sku.channelLink) {
        trackingLinkElement.href = sku.channelLink;
      }

      // Update contact link visibility based on channelContacts
      if (contactLinkElement) {
        if (sku.channelContacts && sku.channelContacts.length > 0) {
          contactLinkElement.style.display = 'block';
        } else if (sku.channelContact) {
          contactLinkElement.style.display = 'block';
        } else {
          contactLinkElement.style.display = 'none';
        }
      }
    }

    if (productSectionContainer && sku) {
      renderProductInfo(sku);
    }

    if (timelineContainer && sku.records) {
      renderTimeline(sku);
    }

    // 更新联系信息
    if (window.updateContactInfo) {
      window.updateContactInfo(sku);
    }
  }

  // 渲染产品信息
  function renderProductInfo(sku) {
    const productItemContainer = productSectionContainer.querySelector('.product-item');

    if (productItemContainer) {
      const productName = productItemContainer.querySelector('.product-name');
      const productPrice = productItemContainer.querySelector('.product-price');
      const productOptions = productItemContainer.querySelector('.product-options');
      const productQuantity = productItemContainer.querySelector('.product-quantity');

      if (productName) {
        if (sku.skuDescription) {
          productName.textContent = sku.skuDescription;
        } else {
          productName.textContent = '';
        }
      }

      if (productPrice) {
        if (sku.price) {
          const currencySymbol = getCurrencySymbol(sku.priceCurrency || 'JPY');
          const formattedPrice = formatNumberWithCommas(sku.price);
          productPrice.textContent = `${currencySymbol}${formattedPrice}`;
        } else {
          productPrice.textContent = '';
        }
      }

      if (productOptions) {
        if (sku.options) {
          productOptions.textContent = sku.options;
        } else {
          productOptions.textContent = '';
        }
      }

      if (productQuantity) {
        if (!isNaN(sku.skuCount) && sku.skuCount !== null) {
          productQuantity.textContent = `x${sku.skuCount}`;
        } else {
          productQuantity.textContent = '';
        }
      }
    }
  }

  // 渲染时间轴
  function renderTimeline(sku) {
    if (!sku.records || !sku.records.length) {
      timelineContainer.innerHTML = '<p>配送状況の情報がありません</p>';
      return;
    }

    timelineContainer.innerHTML = '';

    // 倒序
    const sortedRecords = [...sku.records].filter(record => record.nodeStatus !== 'PENDING').reverse();

    // 如果sku.dateStr有值，表示配送还未结束，添加预计送达时间到时间轴
    if (sku.dateStr) {
      const estimatedLi = document.createElement('li');
      estimatedLi.className = 'timeline-item timeline-item-estimated';

      // 获取预计送达时间内容
      const estimatedDateText = 'お届けまでの目安：';
      const estimatedDateValue = sku.dateStr;

      // 复制tooltip元素
      const tooltipTrigger = document.getElementById('tooltip-trigger-1');
      const tooltipContent = document.getElementById('tooltip-content-1');

      // 创建新的tooltip ID，避免ID冲突
      const newTooltipTriggerId = 'tooltip-trigger-timeline';
      const newTooltipContentId = 'tooltip-content-timeline';

      estimatedLi.innerHTML = `
        <div class="timeline-marker timeline-marker-package"></div>
        <div class="timeline-content">
          <span class="timeline-status">
            ${estimatedDateText} <span class="estimated-date">${estimatedDateValue}</span>
            <span id="${newTooltipTriggerId}" class="tooltip-container"
              style="position: relative; display: inline-block; cursor: pointer; top: -2.5px;">
              <img style="width: 13px; height: 13px; vertical-align: bottom;" src="{{asset_url 'circle-question.svg'}}"
                alt="Question">
            </span>
            <span id="${newTooltipContentId}" class="tooltip-text" role="tooltip">
              ※お届け時期は目安のため、前後する場合が ございますので、予めご了承ください。
              <div id="arrow-timeline" data-popper-arrow></div>
            </span>
          </span>
        </div>
      `;

      // 在DOM添加完成后初始化tooltip
      setTimeout(() => {
        const newTrigger = document.getElementById(newTooltipTriggerId);
        const newTooltip = document.getElementById(newTooltipContentId);
        const newArrowElement = document.getElementById('arrow-timeline');

        if (newTrigger && newTooltip && newArrowElement) {
          let newPopperInstance = null;

          function createNewPopperInstance() {
            if (newPopperInstance) {
              newPopperInstance.destroy();
              newPopperInstance = null;
            }
            newPopperInstance = Popper.createPopper(newTrigger, newTooltip, {
              placement: 'top',
              modifiers: [
                {
                  name: 'offset',
                  options: {
                    offset: [0, 10],
                  },
                },
                {
                  name: 'arrow',
                  options: {
                    element: newArrowElement,
                    padding: 5,
                  },
                },
                {
                  name: 'preventOverflow',
                  options: {
                    padding: 10,
                  },
                }
              ],
            });
            newTooltip.classList.add('show');
          }

          function destroyNewPopperInstance() {
            if (newPopperInstance) {
              newTooltip.classList.remove('show');
              newPopperInstance.destroy();
              newPopperInstance = null;
            }
          }

          newTrigger.addEventListener('click', (event) => {
            event.stopPropagation();
            if (newPopperInstance) {
              destroyNewPopperInstance();
            } else {
              createNewPopperInstance();
            }
          });

          document.addEventListener('click', (event) => {
            if (newTooltip && !newTooltip.contains(event.target) && newTrigger && !newTrigger.contains(event.target)) {
              destroyNewPopperInstance();
            }
          });
        }
      }, 100);

      timelineContainer.appendChild(estimatedLi);
    }

    sortedRecords.forEach((record, index, array) => {
      const li = document.createElement('li');
      li.className = 'timeline-item';

      if (index === 0) { // 因为之前reverse了
        li.classList.add('current');

        // 如果dateStr为空，表示已配送完成，最后一个节点使用包裹图标
        if (!sku.dateStr && array.length > 0) {
          li.classList.add('completed');
          li.classList.add('last-completed');
        }
      } else {
        li.classList.add('completed');
      }

      li.innerHTML = `
        <div class="timeline-marker"></div>
        <div class="timeline-content">
          <span class="timeline-status">${record.nodeDescription || ''}</span>
          <time class="timeline-time" datetime="${record.dateStr}">${record.dateStr || ''}</time>
        </div>
      `;

      timelineContainer.appendChild(li);
    });
  }

  // 获取订单跟踪数据
  async function fetchOrderTrackingData() {
    try {
      // 初始化DOM元素引用
      orderInfoElement = document.querySelector('.order-info span');
      packageTabsContainer = document.querySelector('.package-tabs');
      shippingInfoContainer = document.querySelector('.shipping-info');
      productSectionContainer = document.querySelector('.product-section');
      timelineContainer = document.querySelector('.timeline');

      const urlParams = new URLSearchParams(window.location.search);
      const encodedParams = urlParams.get('params');

      let orderNo = null;
      let skuCode = null;
      let email = null;

      if (encodedParams) {
        try {
          const decodedParamsString = atob(encodedParams);
          const decodedParams = new URLSearchParams(decodedParamsString);

          orderNo = decodedParams.get('order_number');
          skuCode = decodedParams.get('skuCode');
          email = decodedParams.get('email');
        } catch (error) {
          console.error('Error decoding params:', error);
        }
      } else {
        orderNo = urlParams.get('order_number');
        skuCode = urlParams.get('skuCode');
        email = urlParams.get('email');
      }

      const language = window.Shopline?.locale || 'ja';
      let apiUrl = `https://gateway.caguuu.cn/gaia-fulfillment/order-track-for-customer`;

      const paramsObj = {
        orderNo: orderNo,
      };
      if (skuCode) {
        paramsObj.skuCode = skuCode;
      }
      if (email) {
        paramsObj.email = email;
      }

      const paramsJson = JSON.stringify(paramsObj);
      const paramsBase64 = btoa(paramsJson);

      apiUrl = `${apiUrl}?params=${encodeURIComponent(paramsBase64)}`;

      const response = await fetch(apiUrl, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Accept-Language': language
        },
        mode: 'cors',
      });

      if (!response.ok) {
        throw new Error(`API request failed with status ${response.status}`);
      }

      const data = await response.json();

      if (data.success && data.data) {
        renderOrderTrackingData(data.data);
      } else {
        console.error('API returned unsuccessful response:', data);
        showError(data.message);
      }
    } catch (error) {
      console.error('Error fetching order tracking data:', error);
      showError('データの取得に失敗しました。後でもう一度お試しください。');
    } finally {
      hideLoading();
    }
  }

  // 使用setTimeout确保DOM基本结构已加载
  setTimeout(fetchOrderTrackingData, 0);
</script>