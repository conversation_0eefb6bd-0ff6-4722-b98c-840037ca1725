<div class="{{#if section.settings.include_margins}}page-width{{/if}}">
  {{#for section.blocks as |block|}}
    {{render block}}
  {{/for}}
</div>

{{#schema}}
{
  "name": "t:sections.apps.name",
  "tag": "section",
  "class": "spaced-section",
  "settings": [
    {
      "type": "switch",
      "id": "include_margins",
      "label": "t:sections.apps.settings.include_margins.label",
      "default": true
    }
  ],
  "blocks": [
    {
      "type": "@app"
    }
  ],
  "presets": []
}
{{/schema}}