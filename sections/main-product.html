<section id="MainProduct-{{section.id}}" class="page-width section-padding" data-section="{{section.id}}">
  {{assign "theme_settings" settings}}
  {{assign "product_form_id" (append "product-form-" section.id)}}
  {{assign "variant_images" (map (where product.images "attached_to_variant" true) "src")}}

  {{assign "default_selected_variant" section.settings.default_selected_variant}}
  {{assign "is_selected_variant" product.selected_variant}}
  {{assign "featured_variant" ""}}
  {{#if default_selected_variant or is_selected_variant}}
    {{assign "featured_variant" product.selected_or_first_available_variant}}
  {{/if}}

  {{assign "product_mobile_class" (append "product--mobile-" section.settings.product_mobile_thumbnail_image_hide)}}

  {{snippet
    "section-padding-creator"
    section_id=section.id
    padding_top=section.settings.padding_top
    padding_bottom=section.settings.padding_bottom
  }}

  {{snippet "stylesheet" href=(asset_url "component-tool-tip.css")}}
  <script src="{{asset_url 'component-tool-tip.js'}}" defer></script>

  {{#if request.design_mode}}
    <script src="{{asset_url 'theme-editor.js'}}" defer="defer"></script>
  {{/if}}

  {{assign "youtube_control" 1}}
  {{assign "youtube_show_playlist" 1}}
  {{#if section.settings.youtube_simple_style}}
    {{assign "youtube_control" 0}}
    {{assign "youtube_show_playlist" 0}}
    {{#if section.settings.video_loop}}
      {{assign "youtube_show_playlist" 1}}
    {{/if}}
  {{/if}}

  {{#if settings.show_pc_breadcrumb or settings.show_mobile_breadcrumb}}
    <script src="{{asset_url 'component-product-breadcrumb.js'}}" defer></script>
  {{/if}}

  {{snippet "stylesheet" href=(asset_url "component-product-modal.css")}}
  {{snippet "stylesheet" href=(asset_url "section-collapsible-content.css")}}
  {{snippet "stylesheet" href=(asset_url "section-main-product.css")}}
  {{snippet "stylesheet" href=(asset_url "component-accordion.css")}}
  {{snippet "stylesheet" href=(asset_url "section-main-product-media-gallery.css")}}
  {{snippet "stylesheet" href=(asset_url "component-price.css")}}
  {{#if product.quantity_price_breaks_configured}}
    {{snippet "stylesheet" href=(asset_url "component-volume-pricing.css")}}
    <script src="{{asset_url 'price-per-item.js'}}" defer="defer"></script>
    <script src="{{asset_url 'component-show-more.js'}}" defer="defer"></script>
  {{/if}}
  <script src="{{asset_url 'product-info.js'}}" defer="defer"></script>
  <script src="{{asset_url 'section-main-product.js'}}" defer="defer"></script>
  <script src="{{asset_url 'component-product-form.js'}}" defer="defer"></script>
  <script src="{{asset_url 'component-quantity-input.js'}}" defer></script>

  {{#if (size product.media) > 0}}
    <script src="{{asset_url 'component-slider.js'}}" defer="defer"></script>
    <script src="{{asset_url 'component-product-modal.js'}}" defer="defer"></script>
    <script src="{{asset_url 'component-media-gallery.js'}}" defer="defer"></script>
    {{#if section.settings.magnifier_interactive_type == "mode_2"}}
      <link rel="stylesheet" href="{{asset_url 'lib-photoswipe.css'}}" />
      <script src="{{asset_url 'lib-photoswipe.umd.min.js'}}" defer="defer"></script>
      <script src="{{asset_url 'lib-photoswipe-lightbox.umd.min.js'}}" defer="defer"></script>
      <script src="{{asset_url 'component-product-photo-swipe.js'}}" defer="defer"></script>
    {{/if}}
  {{/if}}

  <main-product-detail
    class="product grid grid-cols-1 grid-cols-2-desktop product--left
      product--{{section.settings.product_image_pc_show_style}}
      {{product_mobile_class}}
      product--{{section.settings.product_image_size}}
      product--mobile-{{section.settings.product_mobile_image_fill_type}}
      {{#if product.media.length}}
        grid--2-col-tablet
      {{else}}
        product--no-media
      {{/if}}
      "
    data-product-id="{{product.id}}"
  >
    
    <div class="product__media-wrapper">
      {{snippet
        "product-media-gallery"
        variant_images=variant_images
        pc_magnifier_type=section.settings.pc_magnifier_type
        image_quality=section.settings.image_quality
        magnifier_interactive_type=section.settings.magnifier_interactive_type
        youtube_control=youtube_control
        youtube_show_playlist=youtube_show_playlist
      }}
      {{#for section.blocks as |block|}}
          {{#startsWith "shopline://apps" block.type}}
            {{render block}}
          {{/startsWith}}
          {{#if block.type == "product_additional"}}
            {{!-- <accordion-component
              class="product__info-item accordion product__details-wrapper"
              {{{block.shopline_attributes}}}
            >
              <details id="Details-{{block.id}}-{{section.id}}" class="product__details-container">
                <summary id="Summary-{{block.id}}-{{section.id}}">
                  {{snippet "icon-select" icon=block.settings.icon}}
                  <h3 class="accordion__title body2 fw-bold">
                    {{block.settings.title}}
                  </h3>
                  {{snippet "icon-arrow"}}
                </summary>
                <template>
                  <div class="accordion__content rte" id="CollapsibleAccordion-{{block.id}}-{{section.id}}">
                    {{{block.settings.description}}}
                    {{{block.settings.custom_page.content}}}
                  </div>
                </template>

              </details>
            </accordion-component> --}}
            {{!-- 商品描述 --}}
          {{else if block.type == "description_accordion"}}
            <div style="margin-left:110px;" class="product__info-item accordion product__details-wrapper disappear" {{{block.shopline_attributes}}}>
              <details
                id="Details-{{block.id}}-{{section.id}}"
                class="product__details-container"
                {{#unless block.settings.fold}}open{{/unless}}
              >
                <summary id="Summary-{{block.id}}-{{section.id}}">
                  <h3 class="accordion__title body2 fw-bold">
                    {{block.settings.title}}
                  </h3>
                  {{snippet "icon-arrow"}}
                </summary>
                <expandable-content data-content-id="CollapsibleAccordion-{{block.id}}-{{section.id}}">
                  <div class="accordion__content rte" id="CollapsibleAccordion-{{block.id}}-{{section.id}}">
                    {{{product.description}}}
                  </div>
                  <template>
                    {{snippet "expandable-content-controls" class="accordion__footer"}}
                  </template>
                </expandable-content>
              </details>
            </div>
          {{/if}}
        {{/for}}
    </div>
    
    <div class="product__info-wrapper">
      <product-info
        data-section="{{section.id}}"
        id="ProductInfo-{{section.id}}"
        class="product__info-container {{#if section.settings.product_info_sticky}}product__column-sticky{{/if}}"
      >
        {{snippet "breadcrumb" class="text-left"}}

        {{#for section.blocks as |block|}}
          {{#startsWith "shopline://apps" block.type}}
            {{render block}}
          {{/startsWith}}
          {{#if block.type == "product_additional"}}
            <accordion-component
              class="product__info-item accordion product__details-wrapper"
              {{{block.shopline_attributes}}}
            >
              <details id="Details-{{block.id}}-{{section.id}}" class="product__details-container">
                <summary id="Summary-{{block.id}}-{{section.id}}">
                  {{snippet "icon-select" icon=block.settings.icon}}
                  <h3 class="accordion__title body2 fw-bold">
                    {{block.settings.title}}
                  </h3>
                  {{snippet "icon-arrow"}}
                </summary>
                <template>
                  <div class="accordion__content rte" id="CollapsibleAccordion-{{block.id}}-{{section.id}}">
                    {{{block.settings.description}}}
                    {{{block.settings.custom_page.content}}}
                <div class="attachment-download-box">
                  </div>
                  </div>

                </template>

              </details>
            </accordion-component>
            {{!-- 商品描述 --}}
          {{else if block.type == "description_accordion"}}
            <div class="product__info-item accordion product__details-wrapper showBox" {{{block.shopline_attributes}}}>
              <details
                id="Details-{{block.id}}-{{section.id}}"
                class="product__details-container"
                {{#unless block.settings.fold}}open{{/unless}}
              >
                <summary id="Summary-{{block.id}}-{{section.id}}">
                  <h3 class="accordion__title body2 fw-bold">
                    {{block.settings.title}}
                  </h3>
                  {{snippet "icon-arrow"}}
                </summary>
                <expandable-content data-content-id="CollapsibleAccordion-{{block.id}}-{{section.id}}">
                  <div class="accordion__content rte" id="CollapsibleAccordion-{{block.id}}-{{section.id}}" style="max-height">
                    {{{product.description}}}
                  </div>
                  <template>
                    {{snippet "expandable-content-controls" class="accordion__footer"}}
                  </template>
                </expandable-content>
              </details>
            </div>
         {{else if block.type == "quantity_selector"}}
            {{assign "volume_pricing_array" featured_variant.quantity_price_breaks}}
            {{assign "current_qty_for_volume_pricing" featured_variant.quantity_rule.min}}
            <div
              id="Quantity-Form-{{section.id}}"
              class="product__info-item product__info-item--quantity-input
                {{block.settings.width}}
                {{block.settings.layout_direction}}"
              {{{block.shopline_attributes}}}
            >
              <div class="quantity-input-label body3 fw-bold">
                {{t "order.order_details.products.quantity"}}
              </div>
              {{snippet
                "quantity-input"
                block=block
                id=section.id
                form_id=product_form_id
                min=featured_variant.quantity_rule.min
                max=featured_variant.quantity_rule.max
                step=featured_variant.quantity_rule.increment
                cart_qty=0
                value=featured_variant.quantity_rule.min
              }}
              <div class="volume-pricing-box">
                {{#if product.quantity_price_breaks_configured}}
                  <price-per-item
                    class="body4 fw-bold"
                    id="Price-Per-Item-{{section.id}}"
                    data-section-id="{{section.id}}"
                    data-variant-id="{{featured_variant.id}}"
                  >
                    {{#if featured_variant.quantity_price_breaks.length > 0}}
                      <div class="price-per-item">
                        {{#if current_qty_for_volume_pricing < (get "minimum_quantity" (first volume_pricing_array))}}
                          {{assign "variant_price" (money_with_currency featured_variant.price)}}
                          <span class="price-per-item--current">{{{t
                              "products.product_details.each_price"
                              price=variant_price
                            }}}</span>
                        {{else}}
                          {{assign "price_break_price" ""}}
                          {{#for volume_pricing_array as |volume_price_item|}}
                            {{#if current_qty_for_volume_pricing >= volume_price_item.minimum_quantity}}
                              {{assign "price_break_price" (money_with_currency volume_price_item.price)}}
                            {{/if}}
                          {{/for}}
                          <span class="price-per-item--current">{{{t
                              "products.product_details.each_price"
                              price=price_break_price
                            }}}</span>
                        {{/if}}
                      </div>
                    {{else if featured_variant}}
                      {{assign "variant_price" (money_with_currency featured_variant.price)}}
                      <div class="price-per-item">
                        <span class="price-per-item--current">{{{t
                            "products.product_details.each_price"
                            price=variant_price
                          }}}</span>
                      </div>
                    {{/if}}
                  </price-per-item>
                {{/if}}
                <div class="quantity__rules body4">
                  {{#if featured_variant.quantity_rule.increment > 1}}
                    <span class="divider">
                      {{t "products.product_details.moq_increment" num=featured_variant.quantity_rule.increment}}
                    </span>
                  {{/if}}
                  {{#if featured_variant.quantity_rule.min > 1}}
                    <span class="divider">
                      {{t "products.product_details.moq_minimum" num=featured_variant.quantity_rule.min}}
                    </span>
                  {{/if}}
                  {{#if featured_variant.quantity_rule.max}}
                    <span class="divider">
                      {{t "products.product_details.moq_maximum" num=featured_variant.quantity_rule.max}}
                    </span>
                  {{/if}}
                </div>
                {{#if product.quantity_price_breaks_configured}}
                  <volume-pricing id="Volume-{{section.id}}">
                    {{#if featured_variant.quantity_price_breaks.length > 0}}
                      <div class="body3 fw-bold volume-pricing-title">{{t
                          "products.product_details.price_break_title"
                        }}</div>
                      <ul class="list-unstyled body3">
                        <li>
                          <span>{{featured_variant.quantity_rule.min}}+</span>
                          {{assign "price" (money_with_currency featured_variant.price)}}
                          <span data-text="{{t 'products.product_details.each_price' price=price}}">{{{t
                              "products.product_details.each_price"
                              price=price
                            }}}</span>
                        </li>
                        {{#for featured_variant.quantity_price_breaks as |price_break|}}
                          {{assign "price_break_price" (money_with_currency price_break.price)}}
                          <li class="{{#if forloop.index0 >= 2}}show-more-item display-none{{/if}}">
                            <span>{{price_break.minimum_quantity}}<span>+</span></span>
                            <span data-text="{{t 'products.product_details.each_price' price=price_break_price}}">{{{t
                                "products.product_details.each_price"
                                price=price_break_price
                              }}}</span>
                          </li>
                        {{/for}}
                      </ul>
                      {{#if featured_variant.quantity_price_breaks.length >= 3}}
                        <show-more-button>
                          <button class="button button--link" id="Show-More-{{section.id}}" type="button">
                            <span class="show-more-expand">{{t
                                "products.product_details.price_breaks_view_more"
                              }}</span>
                            <span class="show-more-fold display-none">{{t
                                "products.product_details.price_breaks_view_less"
                              }}</span>
                          </button>
                        </show-more-button>
                      {{/if}}
                    {{/if}}
                  </volume-pricing>
                {{/if}} 
               </div>
            </div>
          {{else if block.type == "inventory"}}
            <div
              class="product__info-item product__inventory body5"
              id="inventory-{{section.id}}"
              role="status"
              {{{block.shopline_attributes}}}
            >
              {{#if featured_variant.inventory_management}}
                {{#if featured_variant.inventory_quantity > 0}}
                  {{#if featured_variant.inventory_quantity <= block.settings.inventory_threshold}}
                    <span class="low-stock">
                      {{#if block.settings.show_inventory_quantity}}
                        {{t
                          "products.product_details.inventory_low_stock_show_count"
                          quantity=featured_variant.inventory_quantity
                        }}
                      {{else}}
                        {{t "products.product_details.inventory_low_stock"}}
                      {{/if}}
                    </span>
                  {{else}}
                    <span class="in-stock">
                      {{#if block.settings.show_inventory_quantity}}
                        {{t
                          "products.product_details.inventory_in_stock_show_count"
                          quantity=featured_variant.inventory_quantity
                        }}
                      {{else}}
                        {{t "products.product_details.inventory_in_stock"}}
                      {{/if}}
                    </span>
                  {{/if}}
                {{else}}
                  <span class="out-stock">
                    {{#if featured_variant.inventory_policy == "continue"}}
                      {{t "products.product_details.inventory_out_of_stock_continue_selling"}}
                    {{else}}
                      {{t "products.product_details.inventory_out_of_stock"}}
                    {{/if}}
                  </span>
                {{/if}}
              {{/if}}
            </div> 
          {{else if block.type == "title"}}
            <h1 class="product__info-item title5" {{{block.shopline_attributes}}}>{{product.title}}</h1>
          {{else if block.type == "variant_sku"}}
            <div class="product__info-item body4" id="variant_sku_no_{{section.id}}" {{{block.shopline_attributes}}}>
              商品コード：{{featured_variant.sku}}
            </div>
          {{else if block.type == "dividing_line"}}
            <div
              {{{block.shopline_attributes}}}
              class="product__dividing-line display-none
                {{#if block.settings.show_pc_line}}display-block-desktop{{/if}}
                {{#if block.settings.show_mobile_line}}display-block-tablet{{/if}}"
              style="
                --product-dividing-line-height:{{block.settings.dividing_line_height}}px;
                --product-desktop-dividing-line-height:{{block.settings.desktop_dividing_line_height}}px;
                background:{{block.settings.dividing_line_color}};
                --product-dividing-line-style:{{block.settings.dividing_line_style}}"
            ></div>
          {{else if block.type == "text"}}
            <div
              class="product__info-item rte
                {{#if block.settings.text_style == 'body'}}body3{{/if}}
                {{#if block.settings.text_style == 'subtitle'}}body2{{/if}}
                {{#if block.settings.text_style == 'uppercase'}}body3 text-uppercase{{/if}}"
              {{{block.shopline_attributes}}}
            >
              {{{block.settings.text}}}
            </div>
          {{else if block.type == "html"}}
            <div class="product__info-item" {{{block.shopline_attributes}}}>{{{block.settings.html}}}</div>
          {{else if block.type == "price"}}
            {{#if block.settings.font_size_flexible}}
              <style>
                  #price-{{section.id}} {
                    --sale-price-font-size: {{block.settings.sale_price_pc_font_size}}px;
                    --regular-price-font-size: {{block.settings.regular_price_pc_font_size}}px;
                    --save-price-font-size: {{block.settings.save_price_pc_font_size}}px;
                  }
                  @media screen and (max-width: 959px) {
                    #price-{{section.id}} {
                      --sale-price-font-size: {{block.settings.sale_price_mobile_font_size}}px;
                      --regular-price-font-size: {{block.settings.regular_price_mobile_font_size}}px;
                      --save-price-font-size: {{block.settings.save_price_mobile_font_size}}px;
                    }
                  }
              </style>
            {{/if}} 
            <div class="product__info-item" id="price-{{section.id}}" {{{block.shopline_attributes}}}>
              {{#if featured_variant}}
                {{snippet
                  "price"
                  block=block
                  section_id=section.id
                  product=product
                  featured_variant=featured_variant
                  use_variant=true
                  cart=cart
                  shop=shop
                  show_save=true
                  save_style=block.settings.save_style
                  discount_style=block.settings.discount_style
                }}
              {{else}}
                {{#capture "sale_from_font_bold"}}
                  {{#if block.settings.sale_font_bold}}fw-bold{{/if}}
                {{/capture}}
                <div class="price">
                  <div class="price__container">
                    <div class="price__sale body2">
                      <span
                        class="{{sale_from_font_bold}}
                          price-item price-item--sale
                          {{#unless block.settings.font_size_flexible}}{{block.settings.sale_font_size}}{{/unless}}"
                      >{{{t "products.product_list.from_price_html" price=(money_with_currency product.price_min)}}}
                      </span>
                    </div>
                  </div>
                </div>
              {{/if}}
              {{#if product.quantity_price_breaks_configured}}
                <div class="volume-pricing-note">
                  <span class="body4">{{t "products.product_list.price_break_tag"}}</span>
                </div>
              {{/if}}
              {{#if cart.taxes_included or shop.shipping_policy.body}}
                <div class="body6 rte">
                  {{#if cart.taxes_included}}
                    {{t "products.product_details.include_taxes"}}
                  {{/if}}
                  {{#if shop.shipping_policy.body}}
                    {{{t "products.product_details.shipping_policy_html" link=shop.shipping_policy.url}}}
                  {{/if}}
                </div>
              {{/if}}
            </div> 
           {{else if block.type == "description"}}
            {{#if block.settings.location == "image_right"}}
              <expand-component
                class="product__info-description-expand-container product__info-item
                  {{#if block.settings.is_fold}}expand-limit-height{{/if}}"
              >
                <div class="expand-wrapper">
                  <div class="product__info-description body3 rte" {{{block.shopline_attributes}}}>
                    {{{product.description}}}
                  </div>
                </div>
                {{#if block.settings.is_fold}}
                  <div class="expand-view-more-box body3">
                    <div class="expand-view-more-button">
                      <span>{{t "products.product_details.price_breaks_view_more"}}</span>
                      {{snippet "icon-arrow"}}
                    </div>
                    <div class="expand-view-less-button">
                      <span>{{t "products.product_details.price_breaks_view_less"}}</span>
                      {{snippet "icon-arrow" class_name="less-button-rotate"}}
                    </div>
                  </div>
                {{/if}}
              </expand-component>
            {{/if}} 
          {{else if block.type == "share"}}
            <div class="product__info-item" {{{block.shopline_attributes}}}>
              {{snippet
                "share-card"
                theme_settings=theme_settings
                share_type="product_detail"
                share_data=product
                domain=request.host
              }}
            </div>
          {{else if block.type == "variant_picker"}}
            {{#if (isFalsey product.has_only_default_variant)}}
              <div class="product__info-item" {{{block.shopline_attributes}}}>
                {{snippet
                  "product-variant-picker"
                  section=section
                  block=block
                  product=product
                  product_form_id=product_form_id
                }}
              </div>
            {{/if}}
          {{else if block.type == "buy_buttons"}}
            <div class="product__info-item" {{{block.shopline_attributes}}}>
              <div class="{{#if block.settings.button_layout == 'float'}}display-none-tablet{{/if}} quick-add-show">
                {{snippet
                  "product-buy-buttons"
                  section=section
                  block=block
                  product=product
                  product_form_id=product_form_id
                }}
              </div>
              {{#if block.settings.button_layout == "float" or block.settings.button_layout == "both"}}
                <link rel="stylesheet" href="{{asset_url 'product-float-buy-buttons.css'}}" />
                <script src="{{asset_url 'product-float-buy-buttons.js'}}" defer="defer"></script>
                <product-float-buy-buttons
                  id="{{product_form_id}}"
                  class="display-none-desktop quick-add-hidden"
                  data-button-layout="{{block.settings.button_layout}}"
                >
                  {{snippet
                    "product-buy-buttons"
                    section=section
                    block=block
                    product=product
                    product_form_id=(append product_form_id "-float")
                  }}
                </product-float-buy-buttons>
              {{/if}}
            </div>
          {{else if block.type == "icon"}}
            <div class="product__info-item product__info-icon" {{{block.shopline_attributes}}}>
              {{snippet "product-icon-list" block=block}}
            </div>
          {{else if block.type == "highlight"}}
            <div class="product__info-item" {{{block.shopline_attributes}}}>
              {{snippet "product-highlight" value=product.metafields.highlights.list.value}}
            </div> 
          {{/if}}
        {{/for}}
      </product-info>
    </div>
  </main-product-detail>

  {{#for section.blocks as |block|}}
    {{#if block.type == "description" and block.settings.location == "image_bottom"}}
      <expand-component
        class="product__info-description-expand-container product__info-description-expand-container--bottom
          {{#if block.settings.is_fold}}expand-limit-height{{/if}}"
      >
        <div class="expand-wrapper">
          <div
            class="product__info-description product__info-description--bottom body3 rte"
            {{{block.shopline_attributes}}}
          >
            {{{product.description}}}
          </div>
        </div>
        {{#if block.settings.is_fold}}
          <div class="expand-view-more-box body3">
            <div class="expand-view-more-button">
              <span>{{t "products.product_details.price_breaks_view_more"}}</span>
              {{snippet "icon-arrow"}}
            </div>
            <div class="expand-view-less-button">
              <span>{{t "products.product_details.price_breaks_view_less"}}</span>
              {{snippet "icon-arrow" class_name="less-button-rotate"}}
            </div>
          </div>
        {{/if}}
      </expand-component>
    {{/if}}
  {{/for}}

  {{snippet "product-media-modal" variant_images=variant_images image_quality=section.settings.image_quality}}
  {{snippet
    "product-photo-swipe-source"
    section=section
    variant_images=variant_images
    quality=section.settings.image_quality
  }}

  {{~#if product.selected_or_first_available_variant.featured_media}}
    {{assign "seo_media" product.selected_or_first_available_variant.featured_media}}
  {{~else}}
    {{assign "seo_media" product.featured_media}}
  {{~/if}}
    <style>
    .recommend-product-item {
      margin-bottom: 8px !important;
    }
    .product-recommend .product-item-swiper-list .swiper-box .swiper-container .swiper-slide {
      border-radius: var(--border-radius) !important;
    }
    .recommend-product-item-info .recommend-product-item-title {
      margin: 0 8px 0 12px !important;
    }
    .recommend-product-item-info .recommend-product-item-price {
      position: inherit !important;
      margin: 0 8px 0 12px !important;
    }
    .recommend-product-item-info .recommend-product-item-price span.sale-price {
      font-size: var(--body2-font-size) !important;
    }
    @media screen and (max-width: 960px){
    .recommend-product-item-info .recommend-product-item-price span.sale-price {
        font-size: 15px !important;
    }
}
    .recommend-product-item-info .recommend-product-item-price span.save-price {
      top: 8px;
      left: 8px;
      display: flex;
      justify-content: center;
      align-items: center;
      height: 22.4px !important;
      line-height: 22.4px !important;
      z-index: 2;
      position: absolute;
      padding: 0 8px;
      color: #fff;
      border-radius: 40px;
      margin: 0 !important;
      font-weight: var(--body-bold-font-weight) !important;
      font-size: var(--body5-font-size) !important;
    }
    .recommend-product-item-info .recommend-product-item-price > span.sale-price {
      margin: 0 !important;
    }
    .accordion__content{
        overflow:hidden;
    }
    .product__media-wrapper>media-gallery{
        position:static !important;
    }
    .product__info-wrapper>.product__column-sticky{
        position:static;
        /* top:20px; */
    }
    #Slider-Thumbnails-main-product-info>li>button{
        border-radius:10px;
        overflow:hidden;
    }
    product-thumbnail-opener>.global-media-settings{
        border-radius:20px;
        overflow:hidden;
    }
    @media screen and (min-width: 960px) {
    .product__info-wrapper{
        max-width: 40% !important;
        width: 40% !important;
        height: 100vh;
        position: sticky;
        top: 0px;
        overflow: auto;
    }
    .product__media-wrapper{
        max-width: 60% !important;
        width: 60% !important;
    }
}
.showBox{
    display:none;
}
#shopline-section-172743108959568bd673{
    display:none;
}
.advc-file-list__style-list a div{
    font-size:16px;
    padding:0 6px;
}
.list-item__download--icon::after{
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-family: sl-icon-logo !important;
    font-size: 16px;
    font-style: normal;
}
a.advc-file-list__list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.list-item__download-outline {
    border: 1px solid;
    border-radius: 50px;
    height:30px;
    line-height: 26px;
}
.advc-icon-xiazai::after{
    content: "\e648";
}
@media screen and (max-width: 959px){
    .disappear{
        display:none;
    }
    .showBox{
        display:block;
    }
}
.Thumbs-Animation-Main-Box{
    display:none;
}
{{!-- 手机端自动样式 --}}
.recommend-grid{
    justify-content: space-around;
}
.recommend-grid::-webkit-scrollbar {
    display: none;
}
.col{
    padding:0 !important;
    margin:0 0 10px 0 !important;
    border-radius:10px;
    overflow:hidden;
    width:46% !important;
    /* flex-basis:80%;
    width:auto !important; */
    background: rgb(250, 250, 250);
}
.product-grid-font{
    font-size:14px !important;
}
.sale-price{
    font-size:15px !important;
}

{{!-- 商品详细页购物小窗口 --}}
@media screen and (min-width: 959px){
/* .addToCartPluginPC{
    padding: 12px 20px !important;
    width:fit-content !important;
    right:10px;
    bottom:10px !important;
    left:auto !important;
    border-radius: 15px;
    box-shadow: 1px 1px 5px #6666665c !important;
}
.pdp_button_text{
    font-size:15px !important;
    font-weight:100 !important;
}
.productInfo>.info{
    width:260px !important;
}
.addToCartPluginPC .productInfoImg{
    width: 80px !important;
    height: auto !important;
    margin-right: 10px  !important;
}
.productInfo>.info>.title{
   -webkit-line-clamp: 2 !important;
   font-size:14px  !important;
   font-weight:100 !important;
}
.productInfo>.info>.JS_plugin_skuAttribute_name_join{
    font-size:10px !important;
    white-space: wrap !important;
    color: rgb(23 23 23 / 60%);
}
.product-price-sales_addToCartPluginPC>span{
    color:#000 !important;
    font-size: 16px !important;
} */
div#addToCartPluginPC {
    border-radius: 10px;
    width: fit-content;
    align-items: center;
    background-color: rgb(36, 36, 36);
    right: 15px;
    height: 130px;
    width: 550px;
    left: auto;
    padding: 12px 15px !important;
    display: flex;
}
div#ADD_TO_CART_addToCartPluginPC {
    width: 155px !important;
    height: 60px;
    text-align: center;
    font-size: 15px;
    border-radius: 100px !important;
    box-size: border-contant;
    background: #000000;
    color: #ffffff;
}
.addToCartPluginPC .productInfo .info .title {
    font-weight: 100 !important;
    font-size: 16px !important;
    color: #000 !important;
    display:block !important;
    line-height: 1.6;
}
.addToCartPluginPC .productInfo .info {
    justify-content: start !important;
    width: fit-content !important;
    max-width: 260px;
}
.addToCartPluginPrice>.price>span{
  color:#000 !important;
  font-size:15px !important;
  font-weight:550 !important;
}
.addToCartPluginPC .buttonWrap>.pdp_button_text {
    font-size: 16px;
    font-weight: 100;
}
.skuAttribute.JS_plugin_skuAttribute_name_join {
    color: rgb(23, 23, 23, 0.6);
    font-size: 11px !important;
}
#addToCartPluginPC > div.productInfo > div.info > div.addToCartPluginPrice > p.originPrice.product-price-origin_addToCartPluginPC.notranslate.isolate{
    color: rgb(135 135 135) !important;
    font-size: 14px !important;
}
svg.mp-loading__circular>path {
    stroke: #fff !important;
}
.addToCartPluginPC .productInfoImg .productImg{
  background:#0000 !important;
}
}
{{!-- 改变商品规格选择状态 --}}
.product-form__input input[type="radio"]:checked + label:not(.product-form--color-swatch),.product-form__input input[type="radio"] + label:not(.product-form--color-swatch):hover {
    background-color: #fff !important;
    color: #000 !important;
    box-shadow: 0 0 0 2px rgb(25,25,25) !important;
    /* border:0 !important; */
    transition: 0.3s all;
}
.product-form__input input[type="radio"] + label:not(.product-form--color-swatch){
    border-radius:5px;
    overflow:hidden;
}
{{!-- 评论展示样式 --}}
.price__container {
display: flex !important;
align-items: center;
justify-content: space-between;
width: 100%;
}
.price{
width: 100%;
}
.product-plugin-comment-rate-star{
width:auto !important;
}
.product-plugin-comment-rate-star > .plugin-product-comment-commentSynthesize {
font-size: 13px !important;
margin-right:8px;
}
.product-plugin-comment-rate-star > div:nth-last-child(1){
    margin:0 !important;
    font-size:11px !important;
    opacity:1 !important;
}
.product-plugin-comment-rate-star{
    color:#000 !important;
}
.product-plugin-comment-rate-star-half>div {
    top: -2px !important;
}
/* .product-plugin-comment-rate-star.plugin-product-comment-product-subTitle-start {
    position: absolute;
    right: 0;
} */
.plugin-product-comment-commentSynthesize:nth-child(2) {
    display: none;
}
/* 限时促销价格的评论 */
/* div#sales__flash-sale-price-container{
    justify-content: space-between;
} */
/* .product-plugin-comment-rate-star {
    opacity:0;
} */
/* 商品详细页推荐商品组件样式 */
.swiper-container.swiper-container-initialized.swiper-container-horizontal.swiper-container-pointer-events{
    cursor: auto !important;
}
.swiper-wrapper{
    overflow: auto;
    transform:none !important;
    scroll-behavior: smooth;
    gap:20px;
}
.swiper-wrapper>div{
    margin-right:0 !important;
}
.swiper-wrapper::-webkit-scrollbar {
    display: none; /* 隐藏滚动条 */
}
span.sale-price {
font-size: 18px;
padding: 0 0 0 8px;
font-weight: 600;
}
.swiper-slide {
background: rgb(250, 250, 250);
border-radius:15px;
overflow:hidden;
width: 19% !important
}
.recommend-product-item-price{
margin:0 8px !important;
justify-content: start !important;
}
.product-grid-font{
-webkit-line-clamp:1 !important;
margin:0 8px !important;
font-size:16px !important;
}
.notranslate{
font-weight:600 !important;
font-size:17px;
}
/* .swiper-button-prev{
display:none;
} */
.recommend-product-item-info{
text-align:left;
}
@media screen and (max-width: 959px){
.swiper-container{
pointer-events: none;
}
.swiper-box{
overflow:auto;
}
.swiper-box::-webkit-scrollbar {
    display: none;
}
.swiper-container,.swiper-wrapper{
width: fit-content !important;
}
.swiper-slide{
width: calc((196vw - 20px) /3) !important;
margin-right:10px !important;;
}
.recommend-product-item{
max-width:none !important;;
}
.swiper-button-next{
display:none !important;;
}
}
/* 商品详情页【关联商品】折扣样式 */
.recommend-product-item-info .recommend-product-item-price>span.origin-price {
    text-decoration: none;
}
.recommend-product-item-info .recommend-product-item-price {
    flex-direction: row-reverse;
}
.sale-price {
    font-size: var(--body2-font-size) !important;
}

.recommend-product-item-info .recommend-product-item-price>span{
  margin:0 !important;
}
.recommend-product-item-info .recommend-product-item-price{
  justify-content: start;
  position: relative;
}

span.origin-price.notranslate {
    text-decoration: line-through !important;
    color: rgb(var(--color-light-text)) !important;
    font-size: var(--body5-font-size) !important;
    display: flex;
    align-items: center;

}
.notranslate {
  font-size: var(--body5-font-size) !important;
}
.recommend-product-item-info .recommend-product-item-price>span.save-price {
    /* width: 100px; */
    text-align: center;
    background: var(--plugin-recommend-color_discount);
    color: #ffffff;
    font-size: 14px;
    font-weight: var(--body-bold-font-weight) !important;
    position: absolute;
    bottom: 70px;
    padding: 4px 8px;
}
span.recommend-product-item-sale-tag.body4 {
    display: none !important;
}
.recommend-product-item-price.body-font.display-center>span:nth-child(2){
  color:var(--plugin-recommend-color_discount) !important;
}
span.sale-price{
  padding:0 !important;
}
@media screen and (max-width:960px){
.sale-price {
    font-size: 12px !important;
    font-weight:600 !important;
}
span.origin-price.notranslate {
    font-size: var(--body5-font-size);
}
.recommend-product-item-info .recommend-product-item-price>span{
  margin:0 0 0 6px !important;
}
}
/* 商品详情页【精选商品】【最近浏览商品】按钮样式统一 */
svg.icon.icon-arrow {
    width: 13px;
    height: 13px;
}
svg.icon.icon-arrow>path {
	stroke-width:1;
}
button.slider-product-recently-viewed__button:hover {
    border: 1px solid rgba(var(--color-entry-line)) !important;
}
.slider-product-recently-viewed__button{
    display: flex;
    align-items: center;
    justify-content: center;
}
.swiper-button-next:hover,.swiper-button-prev:hover{
    transform: translateY(-50%) scale(1) !important;
    box-shadow: none !important;
}
.swiper-button-prev.swiper-button-disabled,.swiper-button-next.swiper-button-disabled:after{
    color: rgba(var(--color-text), 0.3) !important;
}
.slider-product-recently-viewed__button:disabled {
    color: rgba(var(--color-text), 0.3);
    cursor: not-allowed !important;
}
/* 最近浏览商品显示促销折扣样式 */
div#sales__flash-sale-price-container>div:nth-child(1) {
    color: rgb(225 29 72) !important;
    font-weight: var(--body-bold-font-weight) !important;
    font-size: 16px !important;
}
div#sales__flash-sale-price-container>div:nth-child(2) {
    color: rgb(var(--color-light-text));
    font-size: var(--body5-font-size) !important;
    font-weight: 600;
    margin: 0 !important;
}
/* 商品详情页add to card向上移动一点距离 */
.addToCartPluginPC.addToCartPluginPC_BOTTOM.open {
    transform: translate3d(0, -10px, 0);
}
{{!-- 评论最后一颗星 --}}
.product-plugin-comment-rate-star-half>div {
    top: -1px !important;
}
/* 商品详细查看详细样式 */
div#CollapsibleAccordion-1727431461753c371c26-main-product-info {
    margin-bottom: 0 !important;
}
.expandable-content__controls .expandable-content__mask {
    bottom: 100%;
    height: 200px;
}
.expandable-content__buttons.body3.fw-bold {
    left: 50%;
    transform: translate(-50%, 0);
    width: 100%;
    bottom: 10px;
    display: flex;
    justify-content: center;
}
span.button--link.button-more {
    width: 70%;
    text-align: center;
    border: 1px solid #000;
    padding: 10px 0;
    font-size: 12px;
    justify-content: center;
    gap: 5px;
    bottom: 10px;
}
.button--link::after {
    height: 0;
}
span.button--link.button-less {
    max-width: 600px;
    width: 100%;
    border-top: 1px solid;
    justify-content: center;
    align-items: center;
    padding-top: 10px;
}
#shopline-section-main-product-info .accordion__content {
    position: relative;
    left: 50%;
    transform: translateX(-50%);
}
.advc-carousel-images-with-text__item.splide__slide.advc-a-nested {
    margin: 0 !important;
}
.removeUl{
    transform:translateX(0) !important;
}
.advc-carousel-images-with-text__list.splide__list{
    gap:10px;
}
</style>
<script>
setTimeout(() => {
const listItems = document.querySelectorAll('.advc-carousel-images-with-text__list.splide__list li');
let allSvgPresent = true;
let currentLiCount = listItems.length;
listItems.forEach(item => {
    const svgImage = item.querySelector('svg.advc-image');
    if (svgImage) {
        item.remove();
        currentLiCount--;
    } else {
        allSvgPresent = false;
    }
    item.addEventListener('click', () => {
        const href = item.getAttribute('href');
        if (href) {
            window.location.href = href;
        }
    });
});


const splideElement = document.querySelector('.advc-carousel-images-with-text.advc-container');

if (allSvgPresent) {
    if (splideElement) {
        splideElement.style.display = 'none';
    }
} else {
    const newDesktopCol = currentLiCount;
    if (splideElement) {
        splideElement.style.setProperty('--advc-desktop-col', newDesktopCol);
        splideElement.setAttribute('data-block-length', newDesktopCol);
        splideElement.setAttribute('data-desktop-col', newDesktopCol);
        if (newDesktopCol < 3 && window.innerWidth >= 960) {
            splideElement.style.setProperty('--advc-desktop-col', 3);
        }
        if (newDesktopCol === 3 && window.innerWidth < 960) {
        listItems.forEach(item => {

});
        for (let i = 0; i < listItems.length-2; i++) {
            listItems[i].remove();
        }
        const listElement = document.querySelector('.advc-carousel-images-with-text__list.splide__list');
        listElement.style.justifyContent="center";
        listElement.classList.add("removeUl");
        }
    }
}
}, 3000);
/* 20250305搬迁自定义代码 */
// 移动端点击sku到顶部
        function addClickListener() {
            const labels = document.querySelectorAll('.product__info-item > variant-radios > fieldset > .product-form__input--right > label');
            const backToTopIcon = document.querySelector('.advc-back-to-top--icon');
            labels.forEach(label => {
                label.addEventListener('click', () => {
                    if (backToTopIcon) {
                        backToTopIcon.click();
                    }
                });
            });
            setTimeout(()=>{
                const labelsColors = document.querySelectorAll('.color-palette-attr-value.color-palette-value-bg');
                if(labelsColors){
                labelsColors.forEach(label => {
                    label.addEventListener('click', () => {
                        if (backToTopIcon) {
                            backToTopIcon.click();
                        }
                    });
                });
                }
            },1500);
        }
        function checkScreenSize() {
            if (window.innerWidth < 750) {
                addClickListener();
            }
        }
        checkScreenSize();
window.addEventListener('resize', checkScreenSize);
// 商品详情页Add to Card响应修改
document.addEventListener('DOMContentLoaded', function() {
  function checkScrollPosition(isMobile) {
    const targetElement = document.querySelector('.shopline-section.spaced-section');
    const addToCartElement = isMobile ? document.querySelector('div#addToCartPluginMobile') : document.querySelector('div#addToCartPluginPC');
    const targetRect = targetElement.getBoundingClientRect();
    const buttonElement = document.querySelector('button.btn.btn-primary.pay-button-common-item.pay-button-buy-now.button');
    const buttonRect = buttonElement.getBoundingClientRect();
    const windowHeight = window.innerHeight;
    if (isMobile) {
      if (buttonRect.bottom > windowHeight) {
        addToCartElement.style.transform = "translate3d(0, 100%, 0)";
      } else if (buttonRect.bottom < 0 && targetRect.bottom > windowHeight) {
        addToCartElement.style.transform = "translate3d(0, 0, 0)";
      } else if (targetRect.bottom < 0) {
        addToCartElement.style.transform = "translate3d(0, 100%, 0)";
      } else {
        addToCartElement.style.transform = "translate3d(0, 100%, 0)";
      }
    } else {
      const buttonInView = buttonRect.bottom > 0 && buttonRect.top < windowHeight;
      const targetInView = targetRect.top < windowHeight && targetRect.bottom > 0;
      if (buttonInView) {
        addToCartElement.style.transform = "translate3d(0, 100%, 0)";
      } else if (!buttonInView && targetInView) {
        addToCartElement.style.transform = "translate3d(0, -10px, 0)";
      } else {
        addToCartElement.style.transform = "translate3d(0, 100%, 0)";
      }
    }
  }
  function handleScroll() {
    const isMobile = window.innerWidth <= 960;
    checkScrollPosition(isMobile);
  }
  window.addEventListener('scroll', handleScroll);
  window.addEventListener('resize', handleScroll);
  handleScroll();
});



// 小窗口加购字体
document.addEventListener("DOMContentLoaded", () => {
    const handleScroll = () => {
        if (document.querySelector("#ADD_TO_CART_addToCartPluginPC")) {
            document.querySelector("#addToCartPluginPC > div.productInfo > div.info").appendChild(document.querySelector("#addToCartPluginPC > div.rightContent > div.addToCartPluginPrice"));
            const priceElement = document.querySelector(".addToCartPluginPrice> .price> span");
            const priceElement1 = document.querySelector("#addToCartPluginPC > div.productInfo > div.info > div.addToCartPluginPrice.title3 > p.originPrice.product-price-origin_addToCartPluginPC.notranslate.isolate.body4");
            const updatedText = priceElement.textContent.replace(/\s*JPY/, '').trim();
            const updatedText1 = priceElement1.textContent.replace(/\s*JPY/, '').trim();
            priceElement.textContent = updatedText;
            priceElement1.textContent = updatedText1;
            window.removeEventListener("scroll", handleScroll);
        }
    };
    window.addEventListener("scroll", handleScroll);
});
// 商品详情页点击标签保持文案商品コード：
document.addEventListener('DOMContentLoaded', () => {
    const labels = document.querySelectorAll('label.body3.small');
    const skuDiv = document.getElementById('variant_sku_no_main-product-info');
    labels.forEach(label => {
        label.addEventListener('click', () => {
          setTimeout(() => {
            const currentText = skuDiv.textContent.trim();
            if (!currentText.startsWith('商品コード：')) {
                    skuDiv.textContent = `商品コード：${currentText}`;
            }
                }, 1);
        });
    });
});
// 评论显示
function handleBlock() {
    var price = document.querySelector("#price-main-product-info > div.price > .price__container");
    const block = document.querySelector("#shopline-block-17274328078781611002 > div.product-plugin-comment-rate-star");
    if (block && price ) {
        const reviewElement = document.querySelector(".product-plugin-comment-rate-star.plugin-product-comment-product-subTitle-start>div:nth-child(2)>div:nth-child(2)");
        if (reviewElement) {
            const currentText = reviewElement.textContent.replace("(", "").replace(")", "");
            reviewElement.textContent = `(${currentText} 件)`;
            console.log('修改评论文本:', reviewElement);
        }
        price.appendChild(block);
        block.style.opacity = 1;
        observer.disconnect();
    }
}
const observer = new MutationObserver((mutations, obs) => {
    handleBlock();
});
observer.observe(document.body, {
    childList: true,
    subtree: true
});
document.addEventListener('DOMContentLoaded', handleBlock);

// 检测是否有下载说明书
setTimeout(()=>{
    const fileList = document.querySelector('.advc-file-list__style-list');
    const accordionContent = document.querySelector('#Details-172766412923245f0cfd-main-product-info');
    const summary = accordionContent.querySelector('summary');
    if (fileList && accordionContent) {
        summary.insertAdjacentElement('afterend', fileList);
        const links = document.querySelectorAll('.advc-file-list__style-list a');
        links.forEach(link => {
        const href = link.getAttribute('href');
        if (href && href == 'javascript:;') {
            link.style.display = "none";   
        }
    });
}
},2000);
// 商品详细页推荐商品组件样式
function buttonShow(){
    document.querySelector('.swiper-button-next').addEventListener('click', function() {
    const slideWidth = document.querySelector('.swiper-slide').clientWidth;
    const wrapper = document.querySelector('.swiper-wrapper');
    // const moveDistance = slideWidth + 20; 
    const moveDistance = slideWidth * 5 + 100; 
    wrapper.scrollLeft += moveDistance;
});
document.querySelector('.swiper-button-prev').addEventListener('click', function() {
    const slideWidth = document.querySelector('.swiper-slide').clientWidth;
    const wrapper = document.querySelector('.swiper-wrapper');
    const moveDistance = slideWidth * 5 + 100;
    // const moveDistance = slideWidth + 20; 
    wrapper.scrollLeft -= moveDistance; 
});
};
window.addEventListener('scroll', function() {
    if (window.innerWidth >= 960) {
        buttonShow();
    }else{
    const swiperSlides = document.querySelectorAll('.swiper-slide');
    swiperSlides.forEach(slide => {
        const overlay = document.createElement('div');
        overlay.style.position = 'absolute';
        overlay.style.top = '0';
        overlay.style.left = '0';
        overlay.style.right = '0';
        overlay.style.bottom = '0';
        overlay.style.cursor = 'pointer';
        overlay.style.background = 'rgba(255, 255, 255, 0)'; 
        overlay.style.pointerEvents = 'auto'; 
        overlay.onclick = function(event) {
            const link = slide.querySelector('a');
            if (link) {
                window.location.href = link.href; 
            }
        };
        slide.appendChild(overlay);
    });
    }
    const salePriceElements = document.querySelectorAll('.recommend-product-item .sale-price');
    salePriceElements.forEach(function(salePriceElement) {
        salePriceElement.textContent = salePriceElement.textContent.replace(/より/g, '');
    });
});
// 商品详情页【最近浏览商品】商品图片轮播显示
const handleProductBlocks = () => {
    // 检查页面宽度是否大于 960 像素
    if (window.innerWidth < 960) return;

    const productBlocks = document.querySelectorAll('.slider-product-recently-viewed > .slider--mobile > .slider__slide');
    if (productBlocks.length === 0) return;

    productBlocks.forEach(block => {
        const cardInnerWrapper = block.querySelector('.card__inner--wrapper');
        const mediaLink = cardInnerWrapper.querySelector('.card__media');
        const showSmallLis = block.querySelectorAll('.show_small_li');

        cardInnerWrapper.addEventListener('mousemove', (event) => {
            const width = mediaLink.querySelector('.collection-hero__image').getBoundingClientRect().width; 
            const mouseX = event.clientX - cardInnerWrapper.getBoundingClientRect().left;
            
            showSmallLis.forEach(li => li.classList.remove('show_small_li_active'));
            
            if (mouseX < width / 3) {
                mediaLink.style.transform = `translate(-${width}px, 0)`;
                showSmallLis[0].classList.add('show_small_li_active');
            } else if (mouseX < (2 * width) / 3) {
                mediaLink.style.transform = `translate(-${2 * width}px, 0)`;
                showSmallLis[1].classList.add('show_small_li_active');
            } else {
                mediaLink.style.transform = `translate(-${3 * width}px, 0)`; 
                showSmallLis[2].classList.add('show_small_li_active');
            }
        });

        cardInnerWrapper.addEventListener('mouseleave', () => {
            mediaLink.style.transform = 'translate(0, 0)'; 
            showSmallLis.forEach(li => li.classList.remove('show_small_li_active'));
        });
    });
};

// 监听滚动事件
window.onscroll = () => {
    if (document.documentElement.scrollHeight - window.scrollY - window.innerHeight <= 1500) {
        handleProductBlocks();
    }
};
{{!-- 20250207搬迁自定义代码 --}}
// 隐藏无效的sku
  document.addEventListener("DOMContentLoaded", function() {
    function validCombo(inputValue, optionLevel) {
      for (let i = 0; i < productJson.length; i++) {
        if (optionLevel == 1) {
          if (productJson[i].option1 == selectedOptions[0] && productJson[i].option2 == inputValue) {
            return true;
          }
        } else if (optionLevel == 2) {
          if (productJson[i].option1 == selectedOptions[0] && productJson[i].option2 == selectedOptions[1] && productJson[i].option3 == inputValue) {
            return true;
          }
        } else if (optionLevel == 3) {
          if (productJson[i].option1 == selectedOptions[0] && productJson[i].option2 == selectedOptions[1] && productJson[i].option3 == selectedOptions[2] && productJson[i].option4 == inputValue) {
            return true;
          }
        } else {
          if (productJson[i].option1 == selectedOptions[0] && productJson[i].option2 == selectedOptions[1] && productJson[i].option3 == selectedOptions[2] && productJson[i].option4 == selectedOptions[3] && productJson[i].option5 == inputValue) {
            return true;
          }
        }
      }
    }

    function rebuildOptions() {
      selectedOptions = fieldsets.map((fieldset) => {
        return (pickerType == 'radios') ? Array.from(fieldset.querySelectorAll('input')).find((radio) => radio.checked).value : Array.from(fieldset.querySelectorAll('select'), (select) => select.value);
      });
      for (let optionLevel = 1, n = fieldsets.length; optionLevel < n; optionLevel++) {
        const inputs = (pickerType == 'radios') ? fieldsets[optionLevel].querySelectorAll('input') : fieldsets[optionLevel].querySelectorAll('option');

        inputs.forEach(input => {
          input.disabled = (validCombo(input.value, optionLevel)) ? false : true;
          if (pickerType == 'radios') {
            const label = fieldsets[optionLevel].querySelector(`label[for="${input.id}"]`);

            label.style.display = (input.disabled) ? "none" : "";
          } else {
            input.hidden = (validCombo(input.value, optionLevel)) ? false : true;
          }
        });
      }
      for (let optionLevel = 1, fieldsetsLength = fieldsets.length, change = false; optionLevel < fieldsetsLength && !change; optionLevel++) {
        if (pickerType == 'radios') {
          if (fieldsets[optionLevel].querySelector('input:checked').disabled === true) {
            change = (fieldsets[optionLevel].querySelector('input:not(:disabled)').checked = true);
          }
        } else {
          if (fieldsets[optionLevel].querySelector('option:checked').disabled === true) {
            change = (fieldsets[optionLevel].querySelector('option:not(:disabled)').selected = "selected");
          }
            }
            if (change) {
                variant_Selects.dispatchEvent(new Event('change', {
                    bubbles: true
                }));
            }
        }
    }
    const variant_Selects = (document.querySelector('variant-selects')) ? document.querySelector('variant-selects') : document.querySelector('variant-radios');
    const pickerType = (variant_Selects.querySelectorAll('fieldset').length > 0) ? 'radios' : 'selects';
    const fieldsets = (pickerType == 'radios') ? Array.from(variant_Selects.querySelectorAll('fieldset')) : Array.from(variant_Selects.querySelectorAll('.product-form__input--dropdown'));
    const productJson = JSON.parse(variant_Selects.querySelector('[type="application/json"]').textContent);
    let selectedOptions = [];
    variant_Selects.addEventListener('change', rebuildOptions);
    rebuildOptions();
  })

</script>
  <script type="application/ld+json">
    {
      "@context": "http://schema.org/",
      "@type": "Product",
      "name": "{{ product.title }}",
      "url": "{{{ append request.origin product.url }}}",
      {{#if seo_media}}
        "image": [
          "{{{ seo_media.preview_image.src }}}"
        ],
      {{/if}}
      "description": {{{ json (strip_html product.description) }}},
      {{#if product.selected_or_first_available_variant.sku}}
        "sku": "{{ product.selected_or_first_available_variant.sku }}",
      {{/if}}
      "brand": {
        "@type": "Brand",
        "name": "{{ product.vendor }}"
      },
      "offers": [
        {{#for product.variants as |variant|}}
          {
            "@type" : "Offer",
            {{#if sku}}
              "sku": "{{ variant.sku }}",
            {{/if}}
            {{#if (size variant.barcode) == 12}}
              "gtin12": "{{ variant.barcode }}",
            {{/if}}
            {{#if (size variant.barcode) == 13}}
              "gtin13": "{{ variant.barcode }}",
            {{/if}}
            {{#if (size variant.barcode) == 14}}
              "gtin14": "{{ variant.barcode }}",
            {{/if}}
            "availability" : "http://schema.org/{{#if variant.available}}InStock{{else}}OutOfStock{{/if}}",
            "price" : "{{ divide variant.price 100 }}",
            "priceCurrency" : "{{ cart.currency.iso_code }}",
            "url": "{{{ append request.origin url }}}"
          }{{#unless forloop.last}},{{/unless}}
        {{/for}}
      ]
    }
  </script>
</section>

{{#schema}}
{
  "name": "t:sections.main-product.name",
  "max_blocks": 16,
  "class": "section",
  "settings": [
    {
      "type": "switch",
      "id": "product_info_sticky",
      "label": "t:sections.main-product.settings.product_info_sticky.label",
      "default": true
    },
    {
      "type": "select",
      "id": "product_image_pc_show_style",
      "label": "t:sections.main-product.settings.product_image_pc_show_style.label",
      "options": [
        {
          "value": "flatten",
          "label": "t:sections.main-product.settings.product_image_pc_show_style.options__0.label"
        },
        {
          "value": "columns",
          "label": "t:sections.main-product.settings.product_image_pc_show_style.options__1.label"
        },
        {
          "value": "thumbnail_flatten",
          "label": "t:sections.main-product.settings.product_image_pc_show_style.options__2.label"
        },
        {
          "value": "carousel",
          "label": "t:sections.main-product.settings.product_image_pc_show_style.options__3.label"
        }
      ],
      "default": "columns"
    },
    {
      "type": "select",
      "id": "product_image_size",
      "label": "t:sections.main-product.settings.product_image_size.label",
      "info": "t:sections.main-product.settings.product_image_size.info",
      "options": [
        {
          "value": "large",
          "label": "t:sections.main-product.settings.product_image_size.options__0.label"
        },
        {
          "value": "medium",
          "label": "t:sections.main-product.settings.product_image_size.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.main-product.settings.product_image_size.options__2.label"
        }
      ],
      "default": "large"
    },
    {
      "type": "select",
      "id": "product_image_fill_type",
      "label": "t:sections.main-product.settings.product_image_fill_type.label",
      "options": [
        {
          "value": "contain",
          "label": "t:sections.main-product.settings.product_image_fill_type.options__0.label"
        },
        {
          "value": "cover",
          "label": "t:sections.main-product.settings.product_image_fill_type.options__1.label"
        }
      ],
      "default": "contain"
    },
    {
      "type": "select",
      "id": "product_image_ratio",
      "label": "t:sections.main-product.settings.product_image_ratio.label",
      "options": [
        {
          "value": "original",
          "label": "t:sections.main-product.settings.product_image_ratio.options__0.label"
        },
        {
          "value": "adapt_first_image",
          "label": "t:sections.main-product.settings.product_image_ratio.options__1.label"
        },
        {
          "value": "100",
          "label": "t:sections.main-product.settings.product_image_ratio.options__2.label"
        },
        {
          "value": "133.33",
          "label": "3:4"
        },
        {
          "value": "75",
          "label": "t:sections.main-product.settings.product_image_ratio.options__4.label"
        }
      ],
      "default": "original"
    },
    {
      "type": "select",
      "id": "image_quality",
      "label": "t:sections.main-product.settings.image_quality.label",
      "info": "t:sections.main-product.settings.image_quality.info",
      "default": 80,
      "options": [
        {
          "label": "t:sections.main-product.settings.image_quality.options__0.label",
          "value": 100
        },
        {
          "label": "t:sections.main-product.settings.image_quality.options__1.label",
          "value": 90
        },
        {
          "label": "t:sections.main-product.settings.image_quality.options__2.label",
          "value": 80
        },
        {
          "label": "t:sections.main-product.settings.image_quality.options__3.label",
          "value": 70
        }
      ]
    },
    {
      "type": "select",
      "id": "product_image_pc_thumbnail_postion",
      "label": "t:sections.main-product.settings.product_image_pc_thumbnail_postion.label",
      "options": [
        {
          "value": "beside",
          "label": "t:sections.main-product.settings.product_image_pc_thumbnail_postion.options__0.label"
        },
        {
          "value": "bottom",
          "label": "t:sections.main-product.settings.product_image_pc_thumbnail_postion.options__1.label"
        }
      ],
      "default": "beside"
    },
    {
      "type": "select",
      "id": "product_thumbnail_image_size",
      "label": "t:sections.main-product.settings.product_thumbnail_image_size.label",
      "options": [
        {
          "value": "large",
          "label": "t:sections.main-product.settings.product_thumbnail_image_size.options__0.label"
        },
        {
          "value": "medium",
          "label": "t:sections.main-product.settings.product_thumbnail_image_size.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.main-product.settings.product_thumbnail_image_size.options__2.label"
        }
      ],
      "default": "large"
    },
    {
      "type": "switch",
      "id": "video_loop",
      "label": "t:sections.main-product.settings.video_loop.label",
      "default": false
    },
    {
      "type": "switch",
      "id": "video_autoplay",
      "label": "t:sections.main-product.settings.video_autoplay.label",
      "info": "t:sections.main-product.settings.video_autoplay.info",
      "default": false
    },
    {
      "type": "switch",
      "id": "youtube_simple_style",
      "label": "t:sections.main-product.settings.youtube_simple_style.label",
      "default": false
    },
    {
      "type": "select",
      "id": "pc_magnifier_type",
      "label": "t:sections.main-product.settings.pc_magnifier_type.label",
      "default": "click",
      "options": [
        {
          "value": "click",
          "label": "t:sections.main-product.settings.pc_magnifier_type.options__0.label"
        },
        {
          "value": "hover",
          "label": "t:sections.main-product.settings.pc_magnifier_type.options__1.label"
        }
      ]
    },
    {
      "type": "select",
      "id": "magnifier_interactive_type",
      "label": "t:sections.main-product.settings.magnifier_interactive_type.label",
      "default": "mode_1",
      "options": [
        {
          "value": "mode_1",
          "label": "t:sections.main-product.settings.magnifier_interactive_type.options__0.label"
        },
        {
          "value": "mode_2",
          "label": "t:sections.main-product.settings.magnifier_interactive_type.options__1.label"
        }
      ]
    },
    {
      "id": "default_selected_variant",
      "type": "switch",
      "label": "t:sections.main-product.settings.default_selected_variant.label",
      "default": true
    },
    {
      "id": "hide_variants",
      "type": "switch",
      "label": "t:sections.main-product.settings.hide_variants.label",
      "default": false
    },
    {
      "type": "group_header",
      "label": "t:sections.main-product.settings.group_header__0.label"
    },
    {
      "type": "select",
      "id": "product_mobile_thumbnail_image_hide",
      "label": "t:sections.main-product.settings.product_mobile_thumbnail_image_hide.label",
      "options": [
        {
          "value": "columns",
          "label": "t:sections.main-product.settings.product_mobile_thumbnail_image_hide.options__0.label"
        },
        {
          "value": "hide",
          "label": "t:sections.main-product.settings.product_mobile_thumbnail_image_hide.options__1.label"
        },
        {
          "value": "show",
          "label": "t:sections.main-product.settings.product_mobile_thumbnail_image_hide.options__2.label"
        }
      ],
      "default": "hide"
    },
    {
      "type": "select",
      "id": "product_mobile_image_fill_type",
      "label": "t:sections.main-product.settings.product_mobile_image_fill_type.label",
      "options": [
        {
          "value": "contain",
          "label": "t:sections.main-product.settings.product_mobile_image_fill_type.options__0.label"
        },
        {
          "value": "cover",
          "label": "t:sections.main-product.settings.product_mobile_image_fill_type.options__1.label"
        }
      ],
      "default": "contain"
    },
    {
      "type": "select",
      "id": "product_mobile_image_ratio",
      "label": "t:sections.main-product.settings.product_mobile_image_ratio.label",
      "options": [
        {
          "value": "original",
          "label": "t:sections.main-product.settings.product_mobile_image_ratio.options__0.label"
        },
        {
          "value": "adapt_first_image",
          "label": "t:sections.main-product.settings.product_mobile_image_ratio.options__1.label"
        },
        {
          "value": "100",
          "label": "t:sections.main-product.settings.product_mobile_image_ratio.options__2.label"
        },
        {
          "value": "133.33",
          "label": "3:4"
        },
        {
          "value": "75",
          "label": "t:sections.main-product.settings.product_mobile_image_ratio.options__4.label"
        }
      ],
      "default": "original"
    },
    {
      "type": "select",
      "id": "product_mobile_thumbnail_image_size",
      "label": "t:sections.main-product.settings.product_mobile_thumbnail_image_size.label",
      "options": [
        {
          "value": "large",
          "label": "t:sections.main-product.settings.product_mobile_thumbnail_image_size.options__0.label"
        },
        {
          "value": "medium",
          "label": "t:sections.main-product.settings.product_mobile_thumbnail_image_size.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.main-product.settings.product_mobile_thumbnail_image_size.options__2.label"
        }
      ],
      "default": "large"
    },
    {
      "type": "group_header",
      "label": "t:sections.main-product.settings.group_header__1.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.main-product.settings.padding_top.label",
      "default": 0,
      "max": 100,
      "min": 0,
      "step": 2,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.main-product.settings.padding_bottom.label",
      "default": 0,
      "max": 100,
      "min": 0,
      "step": 2,
      "unit": "px"
    }
  ],
  "default": {
    "settings": {
      "product_info_sticky": true,
      "product_image_pc_show_style": "columns",
      "product_image_pc_thumbnail_postion": "beside",
      "product_image_size": "large",
      "product_mobile_thumbnail_image_hide": "hide",
      "pc_magnifier_type": "click",
      "video_loop": false,
      "default_selected_variant": true,
      "hide_variants": false,
      "padding_top": 60,
      "padding_bottom": 60,
      "product_thumbnail_image_size": "large",
      "product_mobile_thumbnail_image_size": "large",
      "product_image_fill_type": "contain",
      "product_image_ratio": "original",
      "product_mobile_image_fill_type": "contain",
      "product_mobile_image_ratio": "original"
    },
    "blocks": [
      {
        "type": "title",
        "settings": {}
      },
      {
        "type": "price",
        "settings": {}
      },
      {
        "type": "dividing_line",
        "settings": {
          "dividing_line_color": "#000000",
          "dividing_line_height": 1,
          "dividing_line_style": "0"
        }
      },
      {
        "type": "variant_picker",
        "settings": {
          "picker_type": "flatten",
          "sizes": "medium",
          "layout_direction": "row"
        }
      },
      {
        "type": "quantity_selector",
        "settings": {}
      },
      {
        "type": "buy_buttons",
        "settings": {
          "button_layout": "embed"
        }
      },
      {
        "type": "description",
        "settings": {
          "is_fold": false
        }
      },
      {
        "type": "product_additional",
        "settings": {
          "title": "Example title"
        }
      },
      {
        "type": "product_additional",
        "settings": {
          "title": "Example title"
        }
      },
      {
        "type": "product_additional",
        "settings": {
          "title": "Example title"
        }
      },
      {
        "type": "product_additional",
        "settings": {
          "title": "Example title"
        }
      },
      {
        "type": "share",
        "settings": {}
      },
      {
        "type": "icon",
        "settings": {
          "icon1": "customer",
          "title1": "Chat With Us",
          "sub_title1": "We offer 24-hour chat support",
          "icon2": "customer",
          "title2": "Chat With Us",
          "sub_title2": "We offer 24-hour chat support",
          "icon3": "customer",
          "title3": "Chat With Us",
          "sub_title3": "We offer 24-hour chat support"
        }
      },
      {
        "type": "highlight",
        "settings": {}
      }
    ]
  },
  "blocks": [
    {
      "type": "@app"
    },
    {
      "type": "variant_sku",
      "icon": "normal",
      "name": "t:sections.main-product.blocks.variant_sku.name",
      "limit": 1,
      "settings": []
    },
    {
      "type": "title",
      "icon": "title",
      "name": "t:sections.main-product.blocks.title.name",
      "limit": 1,
      "settings": []
    },
    {
      "type": "dividing_line",
      "icon": "normal",
      "name": "t:sections.main-product.blocks.dividing_line.name",
      "settings": [
        {
          "type": "switch",
          "id": "show_pc_line",
          "label": "t:sections.main-product.blocks.dividing_line.settings.show_pc_line.label",
          "default": true
        },
        {
          "type": "switch",
          "id": "show_mobile_line",
          "label": "t:sections.main-product.blocks.dividing_line.settings.show_mobile_line.label",
          "default": true
        },
        {
          "type": "color",
          "id": "dividing_line_color",
          "label": "t:sections.main-product.blocks.dividing_line.settings.dividing_line_color.label",
          "default": "#000000"
        },
        {
          "type": "range",
          "id": "desktop_dividing_line_height",
          "label": "t:sections.main-product.blocks.dividing_line.settings.desktop_dividing_line_height.label",
          "default": 1,
          "min": 1,
          "max": 12,
          "step": 1,
          "unit": "px"
        },
        {
          "id": "dividing_line_style",
          "type": "select",
          "options": [
            {
              "value": "0",
              "label": "t:sections.main-product.blocks.dividing_line.settings.dividing_line_style.options__0.label"
            },
            {
              "value": "-20px",
              "label": "t:sections.main-product.blocks.dividing_line.settings.dividing_line_style.options__1.label"
            }
          ],
          "label": "t:sections.main-product.blocks.dividing_line.settings.dividing_line_style.label",
          "default": "0"
        },
        {
          "type": "range",
          "id": "dividing_line_height",
          "label": "t:sections.main-product.blocks.dividing_line.settings.dividing_line_height.label",
          "default": 1,
          "min": 1,
          "max": 12,
          "step": 1,
          "unit": "px"
        }
      ]
    },
    {
      "type": "price",
      "icon": "normal",
      "name": "t:sections.main-product.blocks.price.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "show_order",
          "label": "t:sections.main-product.blocks.price.settings.show_order.label",
          "options": [
            {
              "value": "sale_origin",
              "label": "t:sections.main-product.blocks.price.settings.show_order.options__0.label"
            },
            {
              "value": "origin_sale",
              "label": "t:sections.main-product.blocks.price.settings.show_order.options__1.label"
            },
            {
              "value": "save_sale_origin",
              "label": "t:sections.main-product.blocks.price.settings.show_order.options__2.label"
            },
            {
              "value": "sale_origin_save",
              "label": "t:sections.main-product.blocks.price.settings.show_order.options__3.label"
            },
            {
              "value": "sale_save",
              "label": "t:sections.main-product.blocks.price.settings.show_order.options__4.label"
            },
            {
              "value": "save_sale",
              "label": "t:sections.main-product.blocks.price.settings.show_order.options__5.label"
            }
          ],
          "default": "sale_origin"
        },
        {
          "type": "select",
          "id": "sale_font_size",
          "label": "t:sections.main-product.blocks.price.settings.sale_font_size.label",
          "options": [
            {
              "value": "font_size_small",
              "label": "t:sections.main-product.blocks.price.settings.sale_font_size.options__0.label"
            },
            {
              "value": "font_size_medium",
              "label": "t:sections.main-product.blocks.price.settings.sale_font_size.options__1.label"
            },
            {
              "value": "font_size_big",
              "label": "t:sections.main-product.blocks.price.settings.sale_font_size.options__2.label"
            },
            {
              "value": "font_size_huge",
              "label": "t:sections.main-product.blocks.price.settings.sale_font_size.options__3.label"
            }
          ],
          "default": "font_size_small"
        },
        {
          "type": "select",
          "id": "regular_font_size",
          "label": "t:sections.main-product.blocks.price.settings.regular_font_size.label",
          "options": [
            {
              "value": "font_size_small",
              "label": "t:sections.main-product.blocks.price.settings.regular_font_size.options__0.label"
            },
            {
              "value": "font_size_medium",
              "label": "t:sections.main-product.blocks.price.settings.regular_font_size.options__1.label"
            },
            {
              "value": "font_size_big",
              "label": "t:sections.main-product.blocks.price.settings.regular_font_size.options__2.label"
            },
            {
              "value": "font_size_huge",
              "label": "t:sections.main-product.blocks.price.settings.regular_font_size.options__3.label"
            }
          ],
          "default": "font_size_small"
        },
        {
          "type": "select",
          "id": "save_font_size",
          "label": "t:sections.main-product.blocks.price.settings.save_font_size.label",
          "options": [
            {
              "value": "font_size_small",
              "label": "t:sections.main-product.blocks.price.settings.save_font_size.options__0.label"
            },
            {
              "value": "font_size_medium",
              "label": "t:sections.main-product.blocks.price.settings.save_font_size.options__1.label"
            },
            {
              "value": "font_size_big",
              "label": "t:sections.main-product.blocks.price.settings.save_font_size.options__2.label"
            },
            {
              "value": "font_size_huge",
              "label": "t:sections.main-product.blocks.price.settings.save_font_size.options__3.label"
            }
          ],
          "default": "font_size_small"
        },
        {
          "type": "select",
          "id": "discount_style",
          "label": "t:sections.main-product.blocks.price.settings.discount_style.label",
          "options": [
            {
              "value": "ratio",
              "label": "t:sections.main-product.blocks.price.settings.discount_style.options__0.label"
            },
            {
              "value": "number",
              "label": "t:sections.main-product.blocks.price.settings.discount_style.options__1.label"
            }
          ],
          "default": "number"
        },
        {
          "type": "select",
          "id": "save_style",
          "label": "t:sections.main-product.blocks.price.settings.save_style.label",
          "info": "t:sections.main-product.blocks.price.settings.save_style.info",
          "options": [
            {
              "value": "button",
              "label": "t:sections.main-product.blocks.price.settings.save_style.options__0.label"
            },
            {
              "value": "text",
              "label": "t:sections.main-product.blocks.price.settings.save_style.options__1.label"
            }
          ],
          "default": "text"
        },
        {
          "type": "switch",
          "id": "font_size_flexible",
          "label": "t:sections.main-product.blocks.price.settings.font_size_flexible.label",
          "info": "t:sections.main-product.blocks.price.settings.font_size_flexible.info",
          "default": false
        },
        {
          "type": "range",
          "id": "sale_price_pc_font_size",
          "label": "t:sections.main-product.blocks.price.settings.sale_price_pc_font_size.label",
          "default": 20,
          "min": 12,
          "max": 56,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "sale_price_mobile_font_size",
          "label": "t:sections.main-product.blocks.price.settings.sale_price_mobile_font_size.label",
          "default": 18,
          "min": 12,
          "max": 48,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "regular_price_pc_font_size",
          "label": "t:sections.main-product.blocks.price.settings.regular_price_pc_font_size.label",
          "default": 20,
          "min": 12,
          "max": 56,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "regular_price_mobile_font_size",
          "label": "t:sections.main-product.blocks.price.settings.regular_price_mobile_font_size.label",
          "default": 16,
          "min": 12,
          "max": 48,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "save_price_pc_font_size",
          "label": "t:sections.main-product.blocks.price.settings.save_price_pc_font_size.label",
          "default": 20,
          "min": 12,
          "max": 56,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "range",
          "id": "save_price_mobile_font_size",
          "label": "t:sections.main-product.blocks.price.settings.save_price_mobile_font_size.label",
          "default": 16,
          "min": 12,
          "max": 48,
          "step": 1,
          "unit": "px"
        },
        {
          "type": "switch",
          "id": "sale_font_bold",
          "label": "t:sections.main-product.blocks.price.settings.sale_font_bold.label",
          "default": true
        }
      ]
    },
    {
      "type": "variant_picker",
      "icon": "normal",
      "name": "t:sections.main-product.blocks.variant_picker.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "picker_type",
          "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.label",
          "default": "select",
          "options": [
            {
              "value": "flatten",
              "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__0.label"
            },
            {
              "value": "select",
              "label": "t:sections.main-product.blocks.variant_picker.settings.picker_type.options__1.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "sizes",
          "label": "t:sections.main-product.blocks.variant_picker.settings.sizes.label",
          "default": "medium",
          "options": [
            {
              "value": "small",
              "label": "t:sections.main-product.blocks.variant_picker.settings.sizes.options__0.label"
            },
            {
              "value": "medium",
              "label": "t:sections.main-product.blocks.variant_picker.settings.sizes.options__1.label"
            },
            {
              "value": "large",
              "label": "t:sections.main-product.blocks.variant_picker.settings.sizes.options__2.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "layout_direction",
          "label": "t:sections.main-product.blocks.variant_picker.settings.layout_direction.label",
          "default": "column",
          "options": [
            {
              "value": "column",
              "label": "t:sections.main-product.blocks.variant_picker.settings.layout_direction.options__0.label"
            },
            {
              "value": "row",
              "label": "t:sections.main-product.blocks.variant_picker.settings.layout_direction.options__1.label"
            }
          ]
        },
        {
          "type": "switch",
          "id": "enabled_color_swatch",
          "label": "t:sections.main-product.blocks.variant_picker.settings.enabled_color_swatch.label",
          "info": "t:sections.main-product.blocks.variant_picker.settings.enabled_color_swatch.info",
          "default": true
        },
        {
          "type": "select",
          "id": "color_swatch_type",
          "label": "t:sections.main-product.blocks.variant_picker.settings.color_swatch_type.label",
          "default": "square",
          "options": [
            {
              "value": "square",
              "label": "t:sections.main-product.blocks.variant_picker.settings.color_swatch_type.options__0.label"
            },
            {
              "value": "circle",
              "label": "t:sections.main-product.blocks.variant_picker.settings.color_swatch_type.options__1.label"
            },
            {
              "value": "round_corner",
              "label": "t:sections.main-product.blocks.variant_picker.settings.color_swatch_type.options__2.label"
            }
          ]
        }
      ]
    },
    {
      "type": "quantity_selector",
      "icon": "normal",
      "name": "t:sections.main-product.blocks.quantity_selector.name",
      "limit": 1,
      "settings": [
        {
          "type": "select",
          "id": "width",
          "label": "t:sections.main-product.blocks.quantity_selector.settings.width.label",
          "default": "full",
          "options": [
            {
              "value": "half",
              "label": "t:sections.main-product.blocks.quantity_selector.settings.width.options__0.label"
            },
            {
              "value": "full",
              "label": "t:sections.main-product.blocks.quantity_selector.settings.width.options__1.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "layout_direction",
          "label": "t:sections.main-product.blocks.quantity_selector.settings.layout_direction.label",
          "default": "row",
          "options": [
            {
              "value": "column",
              "label": "t:sections.main-product.blocks.quantity_selector.settings.layout_direction.options__0.label"
            },
            {
              "value": "row",
              "label": "t:sections.main-product.blocks.quantity_selector.settings.layout_direction.options__1.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "border_style",
          "label": "t:sections.main-product.blocks.quantity_selector.settings.border_style.label",
          "default": "outline",
          "options": [
            {
              "value": "line",
              "label": "t:sections.main-product.blocks.quantity_selector.settings.border_style.options__0.label"
            },
            {
              "value": "outline",
              "label": "t:sections.main-product.blocks.quantity_selector.settings.border_style.options__1.label"
            },
            {
              "value": "none",
              "label": "t:sections.main-product.blocks.quantity_selector.settings.border_style.options__2.label"
            }
          ]
        }
      ]
    },
    {
      "type": "inventory",
      "name": "t:sections.main-product.blocks.inventory.name",
      "limit": 1,
      "settings": [
        {
          "type": "range",
          "id": "inventory_threshold",
          "label": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.label",
          "min": 0,
          "max": 100,
          "step": 1,
          "info": "t:sections.main-product.blocks.inventory.settings.inventory_threshold.info",
          "default": 10
        },
        {
          "type": "switch",
          "id": "show_inventory_quantity",
          "label": "t:sections.main-product.blocks.inventory.settings.show_inventory_quantity.label",
          "default": true
        }
      ]
    },
    {
      "type": "buy_buttons",
      "icon": "button",
      "name": "t:sections.main-product.blocks.buy_buttons.name",
      "limit": 1,
      "settings": [
        {
          "id": "button_layout",
          "label": "t:sections.main-product.blocks.buy_buttons.settings.button_layout.label",
          "type": "select",
          "options": [
            {
              "value": "embed",
              "label": "t:sections.main-product.blocks.buy_buttons.settings.button_layout.options__0.label"
            },
            {
              "value": "float",
              "label": "t:sections.main-product.blocks.buy_buttons.settings.button_layout.options__1.label"
            },
            {
              "value": "both",
              "label": "t:sections.main-product.blocks.buy_buttons.settings.button_layout.options__2.label"
            }
          ],
          "default": "embed"
        }
      ]
    },
    {
      "type": "description",
      "icon": "paragraph",
      "name": "t:sections.main-product.blocks.description.name",
      "limit": 1,
      "settings": [
        {
          "id": "location",
          "label": "t:sections.main-product.blocks.description.settings.location.label",
          "type": "select",
          "options": [
            {
              "value": "image_right",
              "label": "t:sections.main-product.blocks.description.settings.location.options__0.label"
            },
            {
              "value": "image_bottom",
              "label": "t:sections.main-product.blocks.description.settings.location.options__1.label"
            }
          ],
          "default": "image_right"
        },
        {
          "type": "switch",
          "id": "is_fold",
          "label": "t:sections.main-product.blocks.description.settings.is_fold.label",
          "default": false
        }
      ]
    },
    {
      "type": "description_accordion",
      "icon": "paragraph",
      "name": "t:sections.main-product.blocks.description_accordion.name",
      "limit": 1,
      "settings": [
        {
          "type": "textarea",
          "id": "title",
          "label": "t:sections.main-product.blocks.description_accordion.settings.title.label",
          "default": "Product Description",
          "limit": 100
        },
        {
          "type": "switch",
          "id": "fold",
          "label": "t:sections.main-product.blocks.description_accordion.settings.fold.label",
          "default": true
        }
      ]
    },
    {
      "type": "share",
      "icon": "normal",
      "name": "t:sections.main-product.blocks.share.name",
      "limit": 1,
      "settings": [
        {
          "type": "group_header",
          "label": "t:sections.main-product.blocks.share.settings.group_header__0.label"
        }
      ]
    },
    {
      "type": "product_additional",
      "icon": "normal",
      "name": "t:sections.main-product.blocks.product_additional.name",
      "settings": [
        {
          "id": "title",
          "type": "text",
          "label": "t:sections.main-product.blocks.product_additional.settings.title.label",
          "default": "Example title"
        },
        {
          "id": "icon",
          "type": "select",
          "label": "t:sections.main-product.blocks.product_additional.settings.icon.label",
          "default": "none",
          "options": [
            {
              "label": "t:sections.main-product.blocks.product_additional.settings.icon.options__0.label",
              "value": "none"
            },
            {
              "label": "t:sections.main-product.blocks.product_additional.settings.icon.options__1.label",
              "value": "pay"
            },
            {
              "label": "t:sections.main-product.blocks.product_additional.settings.icon.options__2.label",
              "value": "package"
            },
            {
              "label": "t:sections.main-product.blocks.product_additional.settings.icon.options__3.label",
              "value": "email"
            },
            {
              "label": "t:sections.main-product.blocks.product_additional.settings.icon.options__4.label",
              "value": "position"
            },
            {
              "label": "t:sections.main-product.blocks.product_additional.settings.icon.options__5.label",
              "value": "customer"
            },
            {
              "label": "t:sections.main-product.blocks.product_additional.settings.icon.options__6.label",
              "value": "chat"
            },
            {
              "label": "t:sections.main-product.blocks.product_additional.settings.icon.options__7.label",
              "value": "gift"
            },
            {
              "label": "t:sections.main-product.blocks.product_additional.settings.icon.options__8.label",
              "value": "phone"
            },
            {
              "label": "t:sections.main-product.blocks.product_additional.settings.icon.options__9.label",
              "value": "faq"
            },
            {
              "label": "t:sections.main-product.blocks.product_additional.settings.icon.options__10.label",
              "value": "logistics"
            },
            {
              "label": "t:sections.main-product.blocks.product_additional.settings.icon.options__11.label",
              "value": "discount"
            }
          ]
        },
        {
          "id": "description",
          "type": "richtext",
          "label": "t:sections.main-product.blocks.product_additional.settings.description.label",
          "default": ""
        },
        {
          "id": "custom_page",
          "type": "page_picker",
          "label": "t:sections.main-product.blocks.product_additional.settings.custom_page.label",
          "default": ""
        }
      ]
    },
    {
      "type": "html",
      "icon": "normal",
      "name": "t:sections.main-product.blocks.html.name",
      "settings": [
        {
          "type": "textarea",
          "id": "html",
          "limit": 0,
          "label": "t:sections.main-product.blocks.html.settings.html.label"
        }
      ]
    },
    {
      "type": "icon",
      "icon": "normal",
      "name": "t:sections.main-product.blocks.icon.name",
      "settings": [
        {
          "label": "t:sections.main-product.blocks.icon.settings.icon1.label",
          "id": "icon1",
          "type": "select",
          "default": "customer",
          "options": [
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__0.label",
              "value": "none"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__1.label",
              "value": "pay"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__2.label",
              "value": "package"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__3.label",
              "value": "email"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__4.label",
              "value": "position"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__5.label",
              "value": "customer"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__6.label",
              "value": "chat"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__7.label",
              "value": "gift"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__8.label",
              "value": "phone"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__9.label",
              "value": "faq"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__10.label",
              "value": "logistics"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__11.label",
              "value": "discount"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__12.label",
              "value": "medal"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon1.options__13.label",
              "value": "environment"
            }
          ]
        },
        {
          "label": "t:sections.main-product.blocks.icon.settings.image1.label",
          "id": "image1",
          "type": "image_picker"
        },
        {
          "label": "t:sections.main-product.blocks.icon.settings.title1.label",
          "id": "title1",
          "type": "text",
          "default": "Chat With Us"
        },
        {
          "label": "t:sections.main-product.blocks.icon.settings.sub_title1.label",
          "id": "sub_title1",
          "type": "text",
          "default": "We offer 24-hour chat support"
        },
        {
          "label": "t:sections.main-product.blocks.icon.settings.icon2.label",
          "id": "icon2",
          "type": "select",
          "default": "customer",
          "options": [
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__0.label",
              "value": "none"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__1.label",
              "value": "pay"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__2.label",
              "value": "package"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__3.label",
              "value": "email"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__4.label",
              "value": "position"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__5.label",
              "value": "customer"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__6.label",
              "value": "chat"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__7.label",
              "value": "gift"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__8.label",
              "value": "phone"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__9.label",
              "value": "faq"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__10.label",
              "value": "logistics"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__11.label",
              "value": "discount"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__12.label",
              "value": "medal"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon2.options__13.label",
              "value": "environment"
            }
          ]
        },
        {
          "label": "t:sections.main-product.blocks.icon.settings.image2.label",
          "id": "image2",
          "type": "image_picker"
        },
        {
          "label": "t:sections.main-product.blocks.icon.settings.title2.label",
          "id": "title2",
          "type": "text",
          "default": "Chat With Us"
        },
        {
          "label": "t:sections.main-product.blocks.icon.settings.sub_title2.label",
          "id": "sub_title2",
          "type": "text",
          "default": "We offer 24-hour chat support"
        },
        {
          "label": "t:sections.main-product.blocks.icon.settings.icon3.label",
          "id": "icon3",
          "type": "select",
          "default": "customer",
          "options": [
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__0.label",
              "value": "none"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__1.label",
              "value": "pay"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__2.label",
              "value": "package"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__3.label",
              "value": "email"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__4.label",
              "value": "position"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__5.label",
              "value": "customer"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__6.label",
              "value": "chat"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__7.label",
              "value": "gift"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__8.label",
              "value": "phone"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__9.label",
              "value": "faq"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__10.label",
              "value": "logistics"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__11.label",
              "value": "discount"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__12.label",
              "value": "medal"
            },
            {
              "label": "t:sections.main-product.blocks.icon.settings.icon3.options__13.label",
              "value": "environment"
            }
          ]
        },
        {
          "label": "t:sections.main-product.blocks.icon.settings.image3.label",
          "id": "image3",
          "type": "image_picker"
        },
        {
          "label": "t:sections.main-product.blocks.icon.settings.title3.label",
          "id": "title3",
          "type": "text",
          "default": "Chat With Us"
        },
        {
          "label": "t:sections.main-product.blocks.icon.settings.sub_title3.label",
          "id": "sub_title3",
          "type": "text",
          "default": "We offer 24-hour chat support"
        }
      ]
    },
    {
      "type": "highlight",
      "icon": "normal",
      "name": "t:sections.main-product.blocks.highlight.name",
      "limit": 1,
      "settings": [
        {
          "type": "group_header",
          "label": "t:sections.main-product.blocks.highlight.settings.group_header__0.label"
        }
      ]
    },
    {
      "type": "text",
      "icon": "normal",
      "name": "t:sections.main-product.blocks.text.name",
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "limit": 0,
          "label": "t:sections.main-product.blocks.text.settings.text.label"
        },
        {
          "type": "select",
          "id": "text_style",
          "limit": 0,
          "label": "t:sections.main-product.blocks.text.settings.text_style.label",
          "options": [
            {
              "value": "body",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__0.label"
            },
            {
              "value": "subtitle",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__1.label"
            },
            {
              "value": "uppercase",
              "label": "t:sections.main-product.blocks.text.settings.text_style.options__2.label"
            }
          ],
          "default": "uppercase"
        }
      ]
    }
  ]
}
{{/schema}}