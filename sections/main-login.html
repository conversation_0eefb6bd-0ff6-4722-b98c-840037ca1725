{{snippet "stylesheet" href=(asset_url "section-customer.css")}}
<script src="{{asset_url 'section-customer-lib-jquery.js'}}" defer></script>
<script src="{{asset_url 'section-main-login.js'}}" defer></script>
<script src="{{asset_url 'component-phone-input.js'}}" defer></script>

<script>
  window.__I18N__ = window.__I18N__ || {};
  window.__I18N__['customer'] = window.__I18N__['customer'] || {};
  window.__I18N__['customer']['general'] = window.__I18N__['customer']['general'] || {{{ json (t 'customer.general') }}};
</script>

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}

<div class="customer login section-padding">
  {{#form "customer_login" id="login-customer-form" thirdLogin="true"}}
    {{assign "activation_tag" form.register_config.activation_tag}}
    {{assign "support_type_list" (split form.register_config.types ",")}}
    {{assign "has_switch" (if (inArray support_type_list "email") and (inArray support_type_list "mobile"))}}
    {{assign
      "has_third_login"
      (if
        (inArray support_type_list "line") or (inArray support_type_list "facebook") or (inArray support_type_list "google")
      )
    }}

    <h1 id="customer-login-title" class="title5 text-center customer-form-title">
      {{t "customer.general.sign_in"}}
    </h1>

    {{#if activation_tag}}
      <p
        id="customer-activation-hint"
        class="body6 text-center customer-form-hint display-none"
        data-show="activation"
      ></p>
    {{/if}}

    {{#if has_switch}}
      <div class="tab" id="login-customer-tab" data-show="normal">
        <a class="body3 active" data-type="email">
          {{t "customer.account.email"}}
        </a>
        <a class="body3" data-type="mobile">
          {{t "customer.account.phone"}}
        </a>
      </div>
    {{/if}}

    {{#for support_type_list as |type|}}
      {{#if type == "email"}}
        <div class="field" data-type="email">
          {{snippet
            "input-email"
            input_class="field__input"
            input_id="RegisterEmail"
            input_name="customer[email]"
            input_required=true
            input_placeholder=(t "customer.general.email")
          }}
          <label for="RegisterEmail" class="field__label body3">
            {{t "customer.general.email"}}
          </label>
        </div>
      {{/if}}

      {{#if type == "mobile"}}
        <phone-input class="field {{#if has_switch}}display-none{{/if}}" data-type="mobile">
          {{snippet
            "input-phone"
            all_country_dialing_code=all_country_dialing_code
            input_name="customer[phone]"
            input_id="RegisterPhone"
            code_name="customer[code]"
            input_required=true
            country_iso_code=localization.country.iso_code
          }}
        </phone-input>
      {{/if}}
    {{/for}}

    <div class="field">
      <div class="field__container">
        <input
          type="password"
          class="field__input"
          id="Password"
          name="customer[password]"
          required
          placeholder="{{t 'customer.general.password'}}"
        />
        <label for="Password" class="field__label body3">
          {{t "customer.general.password"}}
        </label>
      </div>
      <div class="field__suffix">
        <button type="button" class="button button--link" id="toggle-password">
          {{snippet "icon-eye-invisible" id="pwd--show"}}
          {{snippet "icon-eye" class="display-none" id="pwd--hiden"}}
        </button>
      </div>
    </div>

    {{#if activation_tag}}
      <div class="field display-none" data-type="verifycode" data-show="activation">
        <div class="field__container">
          <input
            type="text"
            class="field__input"
            id="RegisterVerifyCode"
            name="customer[verifycode]"
            disabled
            placeholder="{{t 'customer.general.verification_code'}}"
          />
          <label for="RegisterVerifyCode" class="field__label body3">
            {{t "customer.general.verification_code"}}
          </label>
        </div>
        <div class="field__suffix">
          <button class="button button--link verifycode-button" id="customer-login-activate-send-btn">
            {{t "customer.general.send"}}
          </button>
        </div>
      </div>
    {{/if}}

    <p id="customer-error-message" class="body6"></p>

    <div>
      <button class="button submit" type="submit">
        {{t "customer.general.sign_in"}}
        {{snippet "loading-overlay-spinner"}}
      </button>
    </div>

    <div class="actions body4">
      <a href="{{routes.account_recover_url}}">{{t "customer.forget_password.forget_password_tips"}}</a>
      <a href="{{routes.account_register_url}}">{{t "customer.general.register"}}</a>
    </div>

    {{#if has_third_login}}
      <div class="customer-third-login">
        <div class="customer-third-login__desc">
          <span class="body4">{{t "customer.general.login_method"}}</span>
        </div>
        <div class="customer-third-login__btns">
          <div id="third-login-container"></div>
        </div>
      </div>
    {{/if}}

  {{/form}}
</div>

{{#schema}}
{
  "name": "t:sections.main-login.name",
  "class": "section",
  "default": {
    "setting": {
      "padding_top": 80,
      "padding_bottom": 80
    }
  },
  "settings": [
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.main-login.settings.padding_top.label",
      "default": 80
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.main-login.settings.padding_bottom.label",
      "default": 80
    }
  ]
}
{{/schema}}