{{snippet "stylesheet" href=(asset_url "section-main-password-header.css")}}
{{snippet "stylesheet" href=(asset_url "snippet-tips-card.css")}}
<script src="{{asset_url 'section-main-password.js'}}" defer></script>

{{assign "pc_logo_width" settings.desktop_logo_width}}
{{assign "pc_logo_height" settings.desktop_logo_height}}
{{assign "mobile_logo_width" settings.mobile_logo_width}}
{{assign "mobile_logo_height" settings.mobile_logo_height}}
<div class="password-header">
  <h1
    class="title5"
    style="
    --header-logo-pc-width: {{pc_logo_width}}px;
    --header-logo-pc-height: {{pc_logo_height}}px;
    --header-logo-mobile-width: {{mobile_logo_width}}px;
    --header-logo-mobile-heigth: {{mobile_logo_height}}px;
    "
    href="{{routes.root_url}}"
  >
    {{#if settings.logo}}
      {{snippet
        "image"
        class="password-header__heading-logo"
        data=settings.logo
        pc_size=(append pc_logo_width "px")
        mobile_size=(append mobile_logo_width "px")
        alt=(default settings.logo.alt shop.name)
      }}
    {{else}}
      <span>{{shop.name}}</span>
    {{/if}}
  </h1>
  <password-modal>
    <details class="password-modal modal">
      <summary class="modal__toggle">
        <div class="modal__toggle-open link body4">
          {{snippet "icon-lock"}}
          {{t "general.password.use_password_enter"}}
        </div>
      </summary>
      <div class="modal__content">
        <button name="close" class="modal__close-button">
          {{snippet "icon-close"}}
        </button>
        <div class="password-modal__content">
          <h2 class="password-modal__content-heading title5">
            {{shop.name}}
          </h2>
          {{#if shop.password_message}}
            <div class="password-modal__content-desc body3">
              {{shop.password_message}}
            </div>
          {{/if}}
          <div class="password-modal__content-tip body3">
            {{t "general.password.use_password_enter"}}
          </div>
          {{#form "storefront_password" class="password-form" return_to=routes.root_url}}
            <div class="password-field field">
              <input
                type="password"
                name="password"
                id="Password"
                class="field__input"
                autocomplete="current-password"
                {{#if form.errors.messages}}
                  data-error="true"
                {{/if}}
                placeholder="{{t 'customer.general.password'}}"
              />
              <label class="field__label" for="Password">
                {{t "customer.general.password"}}
              </label>
            </div>
            {{#if form.errors.messages}}
              {{snippet "tips-card" type="error" text=form.errors.messages}}
            {{/if}}
            <button name="commit" class="password-button button button--full-width">
              {{t "general.password.entry_store"}}
            </button>
          {{/form}}
          <div class="footer__tip body3">
            {{t "general.password.you_are_admin"}}
            {{#capture "admin_url"}}https://{{shop.permanent_domain}}/admin{{/capture}}
            <a class="footer__jump link" href="{{admin_url}}" target="_blank" class="password-to-login">
              {{t "general.password.log_in_here"}}
            </a>
          </div>
        </div>
      </div>
    </details>
  </password-modal>
</div>

{{#schema}}
{
  "name": "t:sections.main-password-header.name"
}
{{/schema}}