{{assign "show_announcement" false}}

{{#for section.blocks as |block|}}
  {{#if (trim block.settings.notice_link_text)}} {{assign "show_announcement" true}} {{/if}}
{{/for}}

{{assign "should_display_with_slider" section.settings.enable_autoplay}}

{{snippet "stylesheet" href=(asset_url "section-announcement-bar.css")}}

{{#if show_announcement and section.settings.enable_sticky}}
  <script src="{{asset_url 'section-announcement-bar-sticky-top.js'}}" defer></script>
{{/if}}
{{#if should_display_with_slider}}
  {{snippet "stylesheet" href=(asset_url "lib-splide.min.css")}}
  <script src="{{asset_url 'lib-splide.min.js'}}" defer></script>
  <script src="{{asset_url 'section-announcement-bar-slider.js'}}" defer></script>
{{/if}}

{{#if request.design_mode}}
  <script src="{{asset_url 'theme-editor.js'}}" defer="defer"></script>
{{/if}}

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}

{{#if show_announcement}}
  {{#if should_display_with_slider}}<announcement-bar-slider data-speed="{{section.settings.autoplay_speed}}">{{/if}}
  {{#if section.settings.enable_sticky}}<announcement-bar-sticky-top
      data-sticky-mode="{{section.settings.sticky_mode}}"
    >{{/if}}
  <div class="announcement-bar--container {{#if should_display_with_slider}}splide{{/if}}">
    {{#if should_display_with_slider}}<div class="splide__track">{{/if}}
    <div class="announcement-bar--list {{#if should_display_with_slider}}splide__list{{/if}}">
      {{#for section.blocks as |block|}}
        {{~#if (isTruthy block.settings.notice_link_text)}}
          <div
            class="announcement-bar--item section-padding
              {{#if should_display_with_slider}}splide__slide{{/if}}
              {{#if block.settings.announcement_division_bottom}}division_bottom{{/if}}
              {{#if section.settings.show_social_media}}social-media--show{{/if}}"
            style="--notice-text-color: {{block.settings.notice_text_color.red}}, {{block.settings.notice_text_color.green}}, {{block.settings.notice_text_color.blue}}; --notice-bg-color: {{block.settings.notice_bg_color}};"
            {{{block.shopline_attributes}}}
          >
            {{#if section.settings.show_social_media}}
              <div class="announcement-social-media display-none-tablet">
                {{snippet "social-media" settings=settings}}
              </div>
            {{/if}}
            <div class="body4 announcement--text">
              {{#if block.settings.notice_link}}
                <a class="announcement__link" href="{{block.settings.notice_link}}"></a>
              {{/if}}
              <div class="rte">
                <div class="display-none-tablet">{{{block.settings.notice_link_text}}}</div>
                <div class="display-none-desktop">
                  {{{default block.settings.notice_link_mb_text block.settings.notice_link_text}}}
                </div>
              </div>
            </div>
            {{#if section.settings.show_social_media}}
              <div class="announcement-social-media announcement-social-media--placeholder display-none-tablet">
                {{snippet "social-media" settings=settings}}
              </div>
            {{/if}}
          </div>
        {{~/if}}
      {{/for}}
    </div>
    {{#if should_display_with_slider}}</div>{{/if}}
          {{!-- 移动端下载渠道 --}}
     <!--<div class="download-phone-box">
        {{!-- 左边 --}}
        <div class="download-left-phone-box">
            <div class="download-left-phone-small">
                <span class="download-close-box">
                <svg width="9" height="9" viewBox="0 0 27 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M25.5 1.06934L1.5 25.0659" stroke="#242833" stroke-width="2.57143"></path>
                <path d="M1.5 1.06942L25.5 25.066" stroke="#242833" stroke-width="2.57143"></path>
                </svg>
            </span>
            </div>
        </div>
        <div class="download-right-phone-box">
            <a href="https://onelink.onecommerce.io/9LwlnydE" class="download-button-phone-box">ダウンロード</a>
        </div>
      </div>-->
  </div>
  {{#if section.settings.enable_sticky}}</announcement-bar-sticky-top>{{/if}}
  {{#if should_display_with_slider}}</announcement-bar-slider>{{/if}}
{{/if}}
<style>
    .download-phone-box{
    width: 100%;
    /* border: 1px solid #000; */
    padding: 16px 0;
    background-color:rgb(236,246,245);
    display:none;
    justify-content: space-between;
    background-image: url(https://img.myshopline.com/image/store/1726464192427/announcebar-app.jpeg?w=1125&h=184);
    background-size: cover;
}
.download-left-phone-box{
    /* border: 1px solid #000; */
    width:70%;
    display:flex;
    align-items:center;
    justify-content: start;
    gap: 10px;
    padding:0 0 0 10px;
}

.download-left-phone-box>.download-left-phone-small > img{
    width:45px;
    height:45px;
    border-radius:10px;
}
.download-right-phone-box{
    width: 30%;
    display: flex;
    justify-content: end;
    align-items: center;
    padding: 0 10px;
}
.download-button-phone-box{
    background-color: #000;
    color: #fff !important;
    text-align: center;
    border-radius: 50px;
    padding: 7px 10px;
    font-size: 11px;
    cursor: pointer;
}
.download-text-box{
    display:flex;
    flex-wrap:wrap;
    overflow: hidden;
    flex:1;
    /* border: 1px solid #000; */
}
.download-text:nth-child(1){
    font-weight:800;
}
.download-text{
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    width: 100%;
}
.download-left-phone-small{
    width: 15px;
    /* border: 1px solid #000; */
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 10px;
}
/* @media (max-width: 959px) {
    .download-phone-box {
        display: flex;
    }
} */
</style>
<script>
const closeButton = document.querySelector('.download-close-box');
const downloadBox = document.querySelector('.download-phone-box');
if(closeButton && downloadBox){
closeButton.addEventListener('click', () => {
    downloadBox.style.display = 'none';
    const currentTime = new Date().getTime();
    localStorage.setItem('downloadBoxClosed', true);
    localStorage.setItem('closeTime', currentTime);
});

window.addEventListener('load', () => {
    const closed = localStorage.getItem('downloadBoxClosed');
    const closeTime = localStorage.getItem('closeTime');
    const screenWidth = window.innerWidth;
    if (screenWidth < 960) {
        if (closed === 'true' && closeTime) {
        const currentTime = new Date().getTime();
        const timeDifference = currentTime - parseInt(closeTime);
        if (timeDifference < 600000) {
            downloadBox.style.display = 'none'; 
        } else {
            localStorage.removeItem('downloadBoxClosed');
            localStorage.removeItem('closeTime');
            downloadBox.style.display = 'flex';
        }
    } else {
        downloadBox.style.display = 'flex';
    }
    }
});
}
</script>
{{#schema}}
{
  "name": "t:sections.announcement-bar.name",
  "max_blocks": 12,
  "settings": [
    {
      "type": "switch",
      "id": "enable_sticky",
      "default": false,
      "label": "t:sections.announcement-bar.settings.enable_sticky.label"
    },
    {
      "id": "sticky_mode",
      "type": "select",
      "label": "t:sections.announcement-bar.settings.sticky_mode.label",
      "default": "always",
      "options": [
        {
          "value": "always",
          "label": "t:sections.announcement-bar.settings.sticky_mode.options__0.label"
        },
        {
          "value": "pc",
          "label": "t:sections.announcement-bar.settings.sticky_mode.options__1.label"
        },
        {
          "value": "mobile",
          "label": "t:sections.announcement-bar.settings.sticky_mode.options__2.label"
        }
      ]
    },
    {
      "type": "switch",
      "id": "enable_autoplay",
      "default": true,
      "label": "t:sections.announcement-bar.settings.enable_autoplay.label"
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "default": 5,
      "max": 10,
      "min": 3,
      "step": 1,
      "label": "t:sections.announcement-bar.settings.autoplay_speed.label",
      "unit": "s"
    },
    {
      "type": "switch",
      "id": "show_social_media",
      "label": "t:sections.announcement-bar.settings.show_social_media.label",
      "default": false,
      "info": "t:sections.announcement-bar.settings.show_social_media.info"
    },
    {
      "type": "range",
      "id": "padding_top",
      "label": "t:sections.announcement-bar.settings.padding_top.label",
      "default": 10,
      "max": 36,
      "min": 2,
      "step": 2,
      "unit": "px"
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "label": "t:sections.announcement-bar.settings.padding_bottom.label",
      "default": 10,
      "max": 36,
      "min": 2,
      "step": 2,
      "unit": "px"
    }
  ],
  "blocks": [
    {
      "type": "item",
      "icon": "normal",
      "name": "t:sections.announcement-bar.blocks.item.name",
      "settings": [
        {
          "id": "notice_link_text",
          "type": "richtext",
          "label": "t:sections.announcement-bar.blocks.item.settings.notice_link_text.label",
          "default": "30-day postage paid returns"
        },
        {
          "id": "notice_link_mb_text",
          "type": "richtext",
          "label": "t:sections.announcement-bar.blocks.item.settings.notice_link_mb_text.label",
          "info": "t:sections.announcement-bar.blocks.item.settings.notice_link_mb_text.info"
        },
        {
          "type": "url",
          "id": "notice_link",
          "label": "t:sections.announcement-bar.blocks.item.settings.notice_link.label"
        },
        {
          "id": "announcement_division_bottom",
          "type": "switch",
          "label": "t:sections.announcement-bar.blocks.item.settings.announcement_division_bottom.label",
          "default": false
        },
        {
          "type": "color",
          "id": "notice_text_color",
          "label": "t:sections.announcement-bar.blocks.item.settings.notice_text_color.label",
          "default": "#FFFFFF"
        },
        {
          "type": "color",
          "id": "notice_bg_color",
          "label": "t:sections.announcement-bar.blocks.item.settings.notice_bg_color.label",
          "default": "#000000"
        }
      ]
    }
  ]
}
{{/schema}}