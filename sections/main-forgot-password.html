{{snippet "stylesheet" href=(asset_url "section-customer.css")}}
<script src="{{asset_url 'section-main-forgot-password.js'}}" defer></script>
<script src="{{asset_url 'component-phone-input.js'}}" defer></script>

<script>
  window.__I18N__ = window.__I18N__ || {};
  window.__I18N__['customer'] = window.__I18N__['customer'] || {};
  window.__I18N__['customer']['general'] = window.__I18N__['customer']['general'] || {{{ json (t 'customer.general') }}};
</script>

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}

<div class="customer forgot section-padding">
  <h1 class="title5 text-center customer-form-title">
    {{t "customer.forget_password.forget_password_tips"}}
  </h1>
  <p class="description body6" id="reset-customer-password-tip"></p>
  {{#form "reset_customer_password" id="reset_create-customer-form"}}
    {{assign "support_type_list" (split form.register_config.types ",")}}
    {{assign "has_switch" (if (inArray support_type_list "email") and (inArray support_type_list "mobile"))}}
    <div id="reset-customer-password-support-type" data-support-type-list="{{support_type_list}}"></div>
    {{#if has_switch}}
      <div class="tab" id="create-customer-tab">
        <a class="body3 active" data-type="email">
          {{t "customer.account.email"}}
        </a>
        <a class="body3" data-type="mobile">
          {{t "customer.account.phone"}}
        </a>
      </div>
    {{/if}}

    {{#for support_type_list as |type|}}
      {{#if type == "email"}}
        <div class="field" data-type="email">
          {{snippet
            "input-email"
            input_class="field__input"
            input_id="RegisterEmail"
            input_name="customer[email]"
            input_required=true
            input_placeholder=(t "customer.general.email")
          }}
          <label for="RegisterEmail" class="field__label body3">
            {{t "customer.general.email"}}
          </label>
        </div>
      {{/if}}

      {{#if type == "mobile"}}
        <phone-input class="field {{#if has_switch}}display-none{{/if}}" data-type="mobile">
          {{snippet
            "input-phone"
            all_country_dialing_code=all_country_dialing_code
            input_name="customer[phone]"
            input_id="RegisterPhone"
            input_required=true
            code_name="customer[code]"
            country_iso_code=localization.country.iso_code
          }}
        </phone-input>
      {{/if}}
    {{/for}}

    <div class="field">
      <div class="field__container">
        <input
          type="text"
          class="field__input"
          id="RegisterVerifyCode"
          name="customer[verifycode]"
          required
          placeholder="{{t 'customer.general.verification_code'}}"
        />
        <label for="RegisterVerifyCode" class="field__label body3">
          {{t "customer.general.verification_code"}}
        </label>
      </div>
      <div class="field__suffix">
        <button class="button button--link verifycode-button">
          {{t "customer.general.send"}}
        </button>
      </div>
    </div>

    <div class="field">
      <div class="field__container">
        <input
          type="password"
          class="field__input"
          id="password"
          name="customer[password]"
          required
          placeholder="{{t 'customer.general.password_empty_hint'}}"
        />
        <label for="password" class="field__label body3">
          {{t "customer.general.password_empty_hint"}}
        </label>
      </div>
      <div class="field__suffix">
        <button type="button" class="button button--link" id="toggle-password">
          {{snippet "icon-eye-invisible" class="pwd--show"}}
          {{snippet "icon-eye" class="display-none pwd--hiden"}}
        </button>
      </div>
    </div>

    <div class="field">
      <div class="field__container">
        <input
          type="password"
          class="field__input"
          id="passwordConfirm"
          name="customer[password_confirm]"
          required
          placeholder="{{t 'customer.general.send_verification_code_hint'}}"
        />
        <label for="passwordConfirm" class="field__label body3">
          {{t "customer.general.send_verification_code_hint"}}
        </label>
      </div>
      <div class="field__suffix">
        <button type="button" class="button button--link" id="re-toggle-password">
          {{snippet "icon-eye-invisible" id="re-pwd--show"}}
          {{snippet "icon-eye" class="display-none" id="re-pwd--hiden"}}
        </button>
      </div>
    </div>

    <p id="customer-error-message" class="body6"></p>

    <div>
      <button class="button submit" type="submit">
        {{t "customer.forget_password.reset_password"}}
        {{snippet "loading-overlay-spinner"}}
      </button>
    </div>
  {{/form}}
  <div class="backToSignIn">
    <a class="button button--link" href="{{routes.account_login_url}}">
      {{t "customer.login.member_login_common"}}
    </a>
  </div>
</div>

{{#schema}}
{
  "name": "t:sections.main-forgot-password.name",
  "class": "section",
  "default": {
    "setting": {
      "padding_top": 80,
      "padding_bottom": 80
    }
  },
  "settings": [
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.main-forgot-password.settings.padding_top.label",
      "default": 80
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.main-forgot-password.settings.padding_bottom.label",
      "default": 80
    }
  ]
}
{{/schema}}
