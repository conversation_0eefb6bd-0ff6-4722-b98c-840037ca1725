{{snippet "stylesheet" href=(asset_url "section-picture-floating.css")}}
{{snippet "stylesheet" href=(asset_url "lib-splide.min.css")}}
<script src="{{asset_url 'lib-splide.min.js'}}" defer></script>
<script src="{{asset_url 'section-picture-floating.js'}}" defer></script>

{{#if request.design_mode}}
  <script src="{{asset_url 'theme-editor.js'}}" defer="defer"></script>
{{/if}}

{{assign "title" section.settings.title}}
{{assign "title_font" section.settings.title_font}}
{{assign "title_size" section.settings.title_size}}
{{assign "description" section.settings.description}}
{{assign "title_split" section.settings.title_split}}
{{assign "image_height" section.settings.image_height}}
{{assign "mobile_slide_duration" section.settings.mobile_slide_duration}}
{{assign "is_fullscreen" section.settings.is_fullscreen}}
{{assign "color_scheme" section.settings.color_scheme}}

<style>
  {{ font_face title_font }}
  .picture-floating-{{section.id}} {
    --picture-floating-left: {{#if is_fullscreen}}20px{{else}}50px{{/if}};
    --picture-floating-height: {{image_height}}px;
  }
  @media screen and (max-width: 959px) {
    .picture-floating-{{section.id}} {
      --picture-floating-left: {{#if is_fullscreen}}20px{{else}}40px{{/if}}
    }
  }
</style>

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}

<div class="section-padding color-scheme-{{color_scheme}}">
  <picture-floating
    data-mobile-duration="{{mobile_slide_duration}}"
    class="picture-floating-{{section.id}}
      picture-floating__splide-container
      {{#if (isFalsey is_fullscreen)}}page-width{{/if}}"
    style="--title-font: {{title_font.family}};--title-font-style: {{title_font.style}};--title-font-weight: {{title_font.weight}}"
  >
    <div
      class="picture-floating__title
        picture-floating__title--{{title_size}}
        {{#if title_split}}picture-floating__title--split{{/if}}"
    >{{title}}</div>
    <div class="splide">
      <div class="splide__track">
        <ul class="splide__list">
          {{#for section.blocks as |block|}}
            <li class="splide__slide splide__slide-image" {{{block.shopline_attributes}}}>
              {{assign "image" block.settings.image}}
              {{assign "link" block.settings.link}}
              <a href="{{#if link}}{{link}}{{else}}javascript:;{{/if}}">
                {{#if image}}
                  {{snippet "image" class="picture-floating__image" data=image}}
                {{else}}
                  {{placeholder_svg_tag "image" "placeholder"}}
                {{/if}}
              </a>
            </li>
          {{/for}}
        </ul>
      </div>
    </div>
    <div class="picture-floating__desc body2">{{{description}}}</div>
  </picture-floating>
</div>

{{#schema}}
{
  "name": "t:sections.picture-floating.name",
  "class": "section",
  "settings": [
    {
      "type": "text",
      "id": "title",
      "label": "t:sections.picture-floating.settings.title.label",
      "default": "Inspir"
    },
    {
      "type": "font",
      "id": "title_font",
      "label": "t:sections.picture-floating.settings.title_font.label",
      "default": "Montserrat:600"
    },
    {
      "type": "select",
      "id": "title_size",
      "label": "t:sections.picture-floating.settings.title_size.label",
      "options": [
        {
          "value": "small",
          "label": "t:sections.picture-floating.settings.title_size.options__0.label"
        },
        {
          "value": "medium",
          "label": "t:sections.picture-floating.settings.title_size.options__1.label"
        },
        {
          "value": "large",
          "label": "t:sections.picture-floating.settings.title_size.options__2.label"
        }
      ],
      "default": "medium"
    },
    {
      "type": "switch",
      "id": "title_split",
      "label": "t:sections.picture-floating.settings.title_split.label",
      "default": true
    },
    {
      "type": "richtext",
      "id": "description",
      "label": "t:sections.picture-floating.settings.description.label",
      "default": ""
    },
    {
      "type": "range",
      "id": "image_height",
      "label": "t:sections.picture-floating.settings.image_height.label",
      "min": 200,
      "max": 800,
      "step": 10,
      "unit": "px",
      "default": 800
    },
    {
      "type": "range",
      "id": "mobile_slide_duration",
      "label": "t:sections.picture-floating.settings.mobile_slide_duration.label",
      "min": 2,
      "max": 8,
      "step": 1,
      "unit": "t:sections.picture-floating.settings.mobile_slide_duration.unit",
      "default": 3
    },
    {
      "type": "switch",
      "id": "is_fullscreen",
      "label": "t:sections.picture-floating.settings.is_fullscreen.label",
      "default": false
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "t:sections.picture-floating.settings.color_scheme.label",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "t:sections.picture-floating.settings.color_scheme.options__0.label"
        },
        {
          "value": "1",
          "label": "t:sections.picture-floating.settings.color_scheme.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.picture-floating.settings.color_scheme.options__2.label"
        },
        {
          "value": "3",
          "label": "t:sections.picture-floating.settings.color_scheme.options__3.label"
        }
      ]
    },
    {
      "type": "group_header",
      "label": "t:sections.picture-floating.settings.group_header__0.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 100,
      "max": 420,
      "step": 2,
      "unit": "px",
      "label": "t:sections.picture-floating.settings.padding_top.label",
      "default": 200
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 60,
      "max": 120,
      "step": 2,
      "unit": "px",
      "label": "t:sections.picture-floating.settings.padding_bottom.label",
      "default": 60
    }
  ],
  "max_blocks": 6,
  "blocks": [
    {
      "type": "image",
      "icon": "image",
      "name": "t:sections.picture-floating.blocks.image.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.picture-floating.blocks.image.settings.image.label",
          "info": "t:sections.picture-floating.blocks.image.settings.image.info"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.picture-floating.blocks.image.settings.link.label"
        }
      ]
    }
  ],
  "presets": [
    {
      "category_index": 1,
      "category": "t:sections.picture-floating.presets.presets__0.category",
      "name": "t:sections.picture-floating.presets.presets__0.name",
      "settings": {
        "title": "Inspir",
        "title_size": "medium",
        "title_split": true,
        "description": "",
        "image_height": 800,
        "mobile_slide_duration": 3,
        "is_fullscreen": false,
        "color_scheme": "none",
        "padding_top": 200,
        "padding_bottom": 60
      },
      "blocks": [
        {
          "type": "image",
          "settings": {
            "image": "",
            "link": ""
          }
        },
        {
          "type": "image",
          "settings": {
            "image": "",
            "link": ""
          }
        },
        {
          "type": "image",
          "settings": {
            "image": "",
            "link": ""
          }
        },
        {
          "type": "image",
          "settings": {
            "image": "",
            "link": ""
          }
        }
      ]
    }
  ]
}
{{/schema}}