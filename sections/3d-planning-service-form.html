{{snippet "stylesheet" href=(asset_url "component-contact-form.css")}}
<script src="{{asset_url 'component-contact-form.js'}}" defer></script>

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}

<div class="color-scheme-{{section.settings.color_scheme}}">
  <div class="contact page-width page-width--narrow section-padding">
    {{#if section.settings.heading}}
      <h2 class="title title-wrapper--no-top-margin {{section.settings.heading_size}}">
        {{section.settings.heading}}
      </h2>
    {{/if}}
    <contact-form>
      {{#form "contact" class="contact-form"}}
        {{#if form.posted_successfully}}
          <h2 class="field__info field__info--success">
            {{snippet "icon-success"}}
            {{t "general.contact_us.success_feedback"}}
          </h2>
        {{/if}}
        {{#if form.errors.messages}}
          <h2 class="field__info field__info--error">
            {{snippet "icon-error"}}
            {{form.errors.messages}}
          </h2>
        {{/if}}

        <!-- 基本情報 -->
        <div class="form-section">
          <h3 class="form-section-title">基本情報</h3>
          
          <div class="field required">
            <input
              class="field__input"
              type="text"
              id="ContactForm-name-kanji"
              name="contact[name_kanji]"
              required
              placeholder="お名前（漢字）全角"
            />
            <label class="field__label" for="ContactForm-name-kanji">お名前（漢字）全角 <span class="required-mark">*</span></label>
          </div>

          <div class="field required">
            <input
              class="field__input"
              type="text"
              id="ContactForm-name-kana"
              name="contact[name_kana]"
              required
              placeholder="お名前（フリガナ）全角"
            />
            <label class="field__label" for="ContactForm-name-kana">お名前（フリガナ）全角 <span class="required-mark">*</span></label>
          </div>

          <div class="field required">
            <input
              type="tel"
              id="ContactForm-phone"
              class="field__input"
              name="contact[phone]"
              required
              pattern="[0-9\-]*"
              placeholder="ご連絡先（電話番号）"
            />
            <label class="field__label" for="ContactForm-phone">ご連絡先（電話番号） <span class="required-mark">*</span></label>
          </div>

          <div class="field required">
            <input
              type="email"
              id="ContactForm-email"
              class="field__input"
              name="contact[email]"
              required
              placeholder="メールアドレス"
            />
            <label class="field__label" for="ContactForm-email">メールアドレス <span class="required-mark">*</span></label>
          </div>

          <div class="field required">
            <input
              type="email"
              id="ContactForm-email-confirm"
              class="field__input"
              name="contact[email_confirm]"
              required
              placeholder="メールアドレス（確認用）"
            />
            <label class="field__label" for="ContactForm-email-confirm">メールアドレス（確認用） <span class="required-mark">*</span></label>
          </div>

          <div class="field required">
            <input
              type="number"
              id="ContactForm-age"
              class="field__input"
              name="contact[age]"
              required
              placeholder="年齢"
            />
            <label class="field__label" for="ContactForm-age">年齢 <span class="required-mark">*</span></label>
          </div>

          <div class="field required">
            <input
              type="text"
              id="ContactForm-address"
              class="field__input"
              name="contact[address]"
              required
              placeholder="住所"
            />
            <label class="field__label" for="ContactForm-address">住所 <span class="required-mark">*</span></label>
          </div>
        </div>

        <!-- 家族構成 -->
        <div class="form-section">
          <h3 class="form-section-title">家族構成</h3>
          
          <div class="field required">
            <select id="ContactForm-adults" class="field__input" name="contact[adults]" required>
              <option value="">選択してください</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
              <option value="4">4</option>
              <option value="5">5</option>
              <option value="6">6</option>
            </select>
            <label class="field__label" for="ContactForm-adults">おとなの人数 <span class="required-mark">*</span></label>
          </div>

          <div class="field">
            <select id="ContactForm-children" class="field__input" name="contact[children]">
              <option value="">選択してください</option>
              <option value="1">1</option>
              <option value="2">2</option>
              <option value="3">3</option>
              <option value="4">4</option>
              <option value="5">5</option>
              <option value="6">6</option>
            </select>
            <label class="field__label" for="ContactForm-children">お子さまの人数</label>
          </div>

          <div class="field">
            <fieldset class="checkbox-group">
              <legend>暮らしている動物</legend>
              <div class="checkbox-items">
                <label><input type="checkbox" name="contact[pets][]" value="犬"> 犬</label>
                <label><input type="checkbox" name="contact[pets][]" value="猫"> 猫</label>
                <label>
                  <input type="checkbox" name="contact[pets][]" value="その他">
                  その他
                  <input type="text" class="field__input" name="contact[pets_other]" placeholder="その他の動物">
                </label>
              </div>
            </fieldset>
          </div>
        </div>

        <!-- 住居情報 -->
        <div class="form-section">
          <h3 class="form-section-title">住居情報</h3>

          <div class="field required">
            <fieldset class="radio-group">
              <legend>お住まいの形態 <span class="required-mark">*</span></legend>
              <div class="radio-items">
                <label><input type="radio" name="contact[housing_type]" value="持ち家" required> 持ち家</label>
                <label><input type="radio" name="contact[housing_type]" value="賃貸" required> 賃貸</label>
                <label><input type="radio" name="contact[housing_type]" value="戸建て" required> 戸建て</label>
                <label><input type="radio" name="contact[housing_type]" value="マンション" required> マンション</label>
                <label><input type="radio" name="contact[housing_type]" value="アパート" required> アパート</label>
              </div>
            </fieldset>
          </div>

          <div class="field required">
            <fieldset class="radio-group">
              <legend>家具をお探しの理由 <span class="required-mark">*</span></legend>
              <div class="radio-items">
                <label><input type="radio" name="contact[furniture_reason]" value="ご新築" required> ご新築</label>
                <label><input type="radio" name="contact[furniture_reason]" value="お引っ越し" required> お引っ越し</label>
                <label><input type="radio" name="contact[furniture_reason]" value="お買い換え" required> お買い換え</label>
                <label><input type="radio" name="contact[furniture_reason]" value="お買い足し" required> お買い足し</label>
              </div>
            </fieldset>
          </div>

          <div class="field required">
            <input
              type="text"
              id="ContactForm-new-address"
              class="field__input"
              name="contact[new_address]"
              required
              placeholder="搬家新住所"
            />
            <label class="field__label" for="ContactForm-new-address">搬家新住所 <span class="required-mark">*</span></label>
          </div>

          <div class="field required">
            <fieldset class="radio-group">
              <legend>お引越し時期 <span class="required-mark">*</span></legend>
              <div class="radio-items">
                <label><input type="radio" name="contact[moving_time]" value="１か月以内" required> １か月以内</label>
                <label><input type="radio" name="contact[moving_time]" value="３か月以内" required> ３か月以内</label>
                <label><input type="radio" name="contact[moving_time]" value="半年以内" required> 半年以内</label>
                <label><input type="radio" name="contact[moving_time]" value="１年以内" required> １年以内</label>
                <label><input type="radio" name="contact[moving_time]" value="１年以降" required> １年以降</label>
              </div>
            </fieldset>
          </div>
        </div>

        <!-- インテリアの好み -->
        <div class="form-section">
          <h3 class="form-section-title">インテリアの好み</h3>

          <div class="field required">
            <fieldset class="radio-group">
              <legend>ご希望のイメージ <span class="required-mark">*</span></legend>
              <div class="radio-items">
                <label><input type="radio" name="contact[desired_style]" value="01_NATURAL" required> 01_NATURAL</label>
                <label><input type="radio" name="contact[desired_style]" value="02_WALNUT" required> 02_WALNUT</label>
                <label><input type="radio" name="contact[desired_style]" value="03_MONOTONE" required> 03_MONOTONE</label>
                <label><input type="radio" name="contact[desired_style]" value="04_STEAL" required> 04_STEAL</label>
                <label><input type="radio" name="contact[desired_style]" value="05_LUXURY" required> 05_LUXURY</label>
                <label>
                  <input type="radio" name="contact[desired_style]" value="その他" required> その他のスタイル
                  <input type="text" class="field__input" name="contact[desired_style_other]" placeholder="その他のスタイル">
                </label>
              </div>
            </fieldset>
          </div>

          <div class="field required">
            <fieldset class="checkbox-group">
              <legend>部屋別 <span class="required-mark">*</span></legend>
              <div class="checkbox-items">
                <label><input type="checkbox" name="contact[rooms][]" value="リビング" required> リビング</label>
                <label><input type="checkbox" name="contact[rooms][]" value="ダイニング"> ダイニング</label>
                <label><input type="checkbox" name="contact[rooms][]" value="ベッドルーム"> ベッドルーム</label>
                <!-- Add other room options -->
                <label>
                  <input type="checkbox" name="contact[rooms][]" value="その他">
                  その他
                  <input type="text" class="field__input" name="contact[rooms_other]" placeholder="その他の部屋">
                </label>
              </div>
            </fieldset>
          </div>

          <div class="field required">
            <fieldset class="checkbox-group">
              <legend>購入検討の大型家具 <span class="required-mark">*</span></legend>
              <div class="checkbox-items">
                <label><input type="checkbox" name="contact[furniture][]" value="ソファー" required> ソファー</label>
                <label><input type="checkbox" name="contact[furniture][]" value="ダイニングテーブル"> ダイニングテーブル</label>
                <!-- Add other furniture options -->
                <label>
                  <input type="checkbox" name="contact[furniture][]" value="その他">
                  その他
                  <input type="text" class="field__input" name="contact[furniture_other]" placeholder="その他の家具">
                </label>
              </div>
            </fieldset>
          </div>
        </div>

        <!-- 添付ファイル -->
        <div class="form-section">
          <h3 class="form-section-title">添付ファイル</h3>

          <div class="field">
            <input
              type="file"
              id="ContactForm-room-photos"
              class="field__input"
              name="contact[room_photos]"
              accept="image/*"
            />
            <label class="field__label" for="ContactForm-room-photos">ルームの写真</label>
            <p class="field__note">※ファイルサイズは10MBまでとなります。</p>
          </div>

          <div class="field">
            <input
              type="file"
              id="ContactForm-floor-plan"
              class="field__input"
              name="contact[floor_plan]"
              accept=".pdf,.dwg,.dxf"
            />
            <label class="field__label" for="ContactForm-floor-plan">図面データ</label>
            <p class="field__note">※ファイルサイズは10MBまでとなります。</p>
          </div>
        </div>

        <!-- その他情報 -->
        <div class="form-section">
          <h3 class="form-section-title">その他情報</h3>

          <div class="field required">
            <fieldset class="radio-group">
              <legend>予算 <span class="required-mark">*</span></legend>
              <div class="radio-items">
                <label><input type="radio" name="contact[budget]" value="～５０万円" required> ～５０万円</label>
                <label><input type="radio" name="contact[budget]" value="～１００万円" required> ～１００万円</label>
                <label><input type="radio" name="contact[budget]" value="～２００万円" required> ～２００万円</label>
                <label><input type="radio" name="contact[budget]" value="２００万円以上" required> ２００万円以上</label>
              </div>
            </fieldset>
          </div>

          <div class="field">
            <fieldset class="checkbox-group">
              <legend>このサービスを知った方法</legend>
              <div class="checkbox-items">
                <label><input type="checkbox" name="contact[source][]" value="ホームページ"> ホームページ</label>
                <label><input type="checkbox" name="contact[source][]" value="CAGUUUアプリ"> CAGUUUアプリ</label>
                <label><input type="checkbox" name="contact[source][]" value="広告"> 広告</label>
                <label><input type="checkbox" name="contact[source][]" value="紹介"> 紹介</label>
                <label><input type="checkbox" name="contact[source][]" value="SNS"> SNS</label>
                <label>
                  <input type="checkbox" name="contact[source][]" value="その他">
                  その他
                  <input type="text" class="field__input" name="contact[source_other]" placeholder="その他の方法">
                </label>
              </div>
            </fieldset>
          </div>

          <div class="field">
            <textarea
              rows="10"
              id="ContactForm-message"
              class="field__input text-area"
              name="contact[message]"
              placeholder="その他にご希望の家具やインテリアのご要望、ご質問がございましたらご記入ください。"
            ></textarea>
            <label class="field__label" for="ContactForm-message">その他のご要望・ご質問</label>
          </div>

          <div class="field required">
            <div class="checkbox-single">
              <input
                type="checkbox"
                id="ContactForm-privacy"
                name="contact[privacy]"
                required
              />
              <label for="ContactForm-privacy">個人情報の取り扱いについて同意する <span class="required-mark">*</span></label>
            </div>
          </div>
        </div>

        <div class="contact__button">
          <button type="submit" class="button">
            送信する
          </button>
        </div>
      {{/form}}
    </contact-form>
  </div>
</div>

{{#schema}}
{
  "name": "t:sections.contact-form.name",
  "class": "section",
  "presets": [
    {
      "category_index": 3,
      "category": "t:sections.contact-form.presets.presets__0.category",
      "name": "t:sections.contact-form.presets.presets__0.name",
      "setting": {
        "heading": "Contact us",
        "heading_size": "title3",
        "color_scheme": "none",
        "padding_top": 80,
        "padding_bottom": 80
      }
    }
  ],
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "default": "Contact us",
      "label": "t:sections.contact-form.settings.heading.label"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "title5",
          "label": "t:sections.contact-form.settings.heading_size.options__0.label"
        },
        {
          "value": "title3",
          "label": "t:sections.contact-form.settings.heading_size.options__1.label"
        },
        {
          "value": "title2",
          "label": "t:sections.contact-form.settings.heading_size.options__2.label"
        }
      ],
      "default": "title3",
      "label": "t:sections.contact-form.settings.heading_size.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "t:sections.contact-form.settings.color_scheme.label",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "t:sections.contact-form.settings.color_scheme.options__0.label"
        },
        {
          "value": "1",
          "label": "t:sections.contact-form.settings.color_scheme.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.contact-form.settings.color_scheme.options__2.label"
        },
        {
          "value": "3",
          "label": "t:sections.contact-form.settings.color_scheme.options__3.label"
        }
      ]
    },
    {
      "type": "group_header",
      "label": "t:sections.contact-form.settings.group_header__0.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.contact-form.settings.padding_top.label",
      "default": 80
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.contact-form.settings.padding_bottom.label",
      "default": 80
    }
  ]
}
{{/schema}}