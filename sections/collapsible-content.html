{{snippet "stylesheet" href=(asset_url "component-accordion.css")}}
{{snippet "stylesheet" href=(asset_url "section-collapsible-content.css")}}

{{#if request.design_mode}}
  <script src="{{asset_url 'theme-editor.js'}}" defer="defer"></script>
{{/if}}

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}
<div class="color-scheme-{{section.settings.color_scheme}}">
  <div class="collapsible-content global-card-border-shadow">
    <div class="collapsible-content__wrapper section-padding">
      <div
        class="{{#if (image_url section.settings.image)}}page-width
          {{else}}collapsible-content-wrapper-narrow
          {{/if}}"
      >
        <div class="collapsible-content__header" style="text-align: {{section.settings.heading_alignment}};">
          {{#if section.settings.heading}}
            <h2 class="collapsible-content__heading {{section.settings.heading_size}}">
              {{section.settings.heading}}
            </h2>
          {{/if}}
        </div>
        <div
          class="grid
            collapsible-content__layout-{{section.settings.layout_style}}
            {{#if section.settings.image}}grid-cols-2{{else}}grid-cols-1{{/if}}
            collapsible-content__grid
            {{#if section.settings.layout_style == 'block'}}global-content-border-shadow{{/if}}
            {{#if section.settings.desktop_layout == 'image_second'}} collapsible-content__grid--reverse{{/if}}"
          style="--box-background-color: {{#if section.settings.background_color == "rgba(0,0,0,0)"}}{{ settings.color_text.red }}, {{ settings.color_text.green }}, {{settings.color_text.blue }}, 0.1{{else}}{{ section.settings.background_color.red }}, {{ section.settings.background_color.green }}, {{section.settings.background_color.blue }};{{/if}}"
        >
          {{#if section.settings.image}}
            <div class="grid__item collapsible-content__grid-item">
              <div
                class="collapsible-content__media
                  collapsible-content__media--{{section.settings.image_ratio}}
                  media global-media-border-shadow"
                {{#if section.settings.image_ratio == "adapt"}}
                  style="padding-bottom:{{divide 100 section.settings.image.aspect_ratio}}%;"
                {{/if}}
              >
                {{snippet "image" data=section.settings.image}}
              </div>
            </div>
          {{/if}}
          <div class="grid__item">
            {{#for section.blocks as |block|}}
              <div class="accordion {{#if section.settings.layout_style == 'inline'}}global-content-border-shadow{{/if}}" {{{block.shopline_attributes}}}>
                <details
                  id="Details-{{block.id}}-{{section.id}}"
                  {{#if section.settings.open_first_collapsible_row and forloop.index0 == 0}} open {{/if}}
                >
                  <summary id="Summary-{{block.id}}-{{section.id}}" style="--icon-width: {{block.settings.icon_width}}px;">
                    {{#if block.settings.upload_icon}}
                      <img
                        src="{{image_url block.settings.upload_icon width=block.settings.icon_width}}"
                        alt="{{block.settings.upload_icon.alt}}"
                        class="custom-icon-accordion"
                        loading="lazy"
                      />
                    {{else}}
                      {{snippet "icon-accordion" icon=block.settings.icon settings=block.settings}}
                    {{/if}}
                    <h3 class="accordion__title title6">
                      {{block.settings.heading}}
                    </h3>

                    <div class="icon-fold open">
                      {{snippet "icon-minus"}}
                    </div>
                    <div class="icon-fold close">
                      {{snippet "icon-plus"}}
                    </div>
                  </summary>
                  <div class="accordion__content rte body3" id="CollapsibleAccordion-{{block.id}}-{{section.id}}">
                    {{{block.settings.row_content}}}
                    {{{block.settings.page.content}}}
                  </div>
                </details>
              </div>
            {{/for}}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

{{#schema}}
{
  "name": "t:sections.collapsible-content.name",
  "max_blocks": 15,
  "class": "section",
  "presets": [
    {
      "category_index": 4,
      "category": "t:sections.collapsible-content.presets.presets__0.category",
      "settings": {
        "heading": "Collapsible content",
        "heading_size": "title3",
        "heading_alignment": "center",
        "layout_style": "none",
        "color_scheme": "none",
        "open_first_collapsible_row": false,
        "image": {},
        "image_ratio": "adapt",
        "desktop_layout": "image_second",
        "padding_top": 80,
        "padding_bottom": 80
      },
      "blocks": [
        {
          "type": "collapsible_row",
          "settings": {
            "heading": "Collapsible row",
            "icon": "check_mark",
            "row_content": "",
            "icon_width": 16,
            "page": ""
          }
        },
        {
          "type": "collapsible_row",
          "settings": {
            "heading": "Collapsible row",
            "icon": "check_mark",
            "row_content": "",
            "icon_width": 16,
            "page": ""
          }
        },
        {
          "type": "collapsible_row",
          "settings": {
            "heading": "Collapsible row",
            "icon": "check_mark",
            "row_content": "",
            "icon_width": 16,
            "page": ""
          }
        },
        {
          "type": "collapsible_row",
          "settings": {
            "heading": "Collapsible row",
            "icon": "check_mark",
            "row_content": "",
            "icon_width": 16,
            "page": ""
          }
        }
      ]
    }
  ],
  "settings": [
    {
      "type": "text",
      "id": "heading",
      "label": "t:sections.collapsible-content.settings.heading.label",
      "default": "Collapsible content"
    },
    {
      "type": "select",
      "id": "heading_size",
      "options": [
        {
          "value": "title5",
          "label": "t:sections.collapsible-content.settings.heading_size.options__0.label"
        },
        {
          "value": "title3",
          "label": "t:sections.collapsible-content.settings.heading_size.options__1.label"
        },
        {
          "value": "title2",
          "label": "t:sections.collapsible-content.settings.heading_size.options__2.label"
        }
      ],
      "default": "title3",
      "label": "t:sections.collapsible-content.settings.heading_size.label"
    },
    {
      "type": "select",
      "id": "heading_alignment",
      "label": "t:sections.collapsible-content.settings.heading_alignment.label",
      "options": [
        {
          "value": "left",
          "label": "t:sections.collapsible-content.settings.heading_alignment.options__0.label"
        },
        {
          "value": "center",
          "label": "t:sections.collapsible-content.settings.heading_alignment.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.collapsible-content.settings.heading_alignment.options__2.label"
        }
      ],
      "default": "center"
    },
    {
      "type": "select",
      "id": "layout_style",
      "label": "t:sections.collapsible-content.settings.layout_style.label",
      "options": [
        {
          "value": "none",
          "label": "t:sections.collapsible-content.settings.layout_style.options__0.label"
        },
        {
          "value": "inline",
          "label": "t:sections.collapsible-content.settings.layout_style.options__1.label"
        },
        {
          "value": "block",
          "label": "t:sections.collapsible-content.settings.layout_style.options__2.label"
        }
      ],
      "default": "none"
    },
    {
      "id": "background_color",
      "type": "color",
      "label": "t:sections.collapsible-content.settings.background_color.label",
      "default": "rgba(0,0,0,0)"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "t:sections.collapsible-content.settings.color_scheme.label",
      "default": "none",
      "options": [
        {
          "value": "none",
          "label": "t:sections.collapsible-content.settings.color_scheme.options__0.label"
        },
        {
          "value": "1",
          "label": "t:sections.collapsible-content.settings.color_scheme.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.collapsible-content.settings.color_scheme.options__2.label"
        },
        {
          "value": "3",
          "label": "t:sections.collapsible-content.settings.color_scheme.options__3.label"
        }
      ]
    },
    {
      "type": "switch",
      "id": "open_first_collapsible_row",
      "default": false,
      "label": "t:sections.collapsible-content.settings.open_first_collapsible_row.label"
    },
    {
      "type": "group_header",
      "label": "t:sections.collapsible-content.settings.group_header__0.label"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.collapsible-content.settings.image.label",
      "default": {}
    },
    {
      "type": "select",
      "id": "image_ratio",
      "options": [
        {
          "value": "adapt",
          "label": "t:sections.collapsible-content.settings.image_ratio.options__0.label"
        },
        {
          "value": "large",
          "label": "t:sections.collapsible-content.settings.image_ratio.options__1.label"
        },
        {
          "value": "small",
          "label": "t:sections.collapsible-content.settings.image_ratio.options__2.label"
        }
      ],
      "default": "adapt",
      "label": "t:sections.collapsible-content.settings.image_ratio.label"
    },
    {
      "type": "select",
      "id": "desktop_layout",
      "options": [
        {
          "value": "image_first",
          "label": "t:sections.collapsible-content.settings.desktop_layout.options__0.label"
        },
        {
          "value": "image_second",
          "label": "t:sections.collapsible-content.settings.desktop_layout.options__1.label"
        }
      ],
      "default": "image_second",
      "label": "t:sections.collapsible-content.settings.desktop_layout.label",
      "info": "t:sections.collapsible-content.settings.desktop_layout.info"
    },
    {
      "type": "group_header",
      "label": "t:sections.collapsible-content.settings.group_header__1.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.collapsible-content.settings.padding_top.label",
      "default": 30
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.collapsible-content.settings.padding_bottom.label",
      "default": 30
    }
  ],
  "blocks": [
    {
      "type": "collapsible_row",
      "name": "t:sections.collapsible-content.blocks.collapsible_row.name",
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Collapsible row",
          "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.heading.label"
        },
        {
          "type": "select",
          "id": "icon",
          "options": [
            {
              "value": "none",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__0.label"
            },
            {
              "value": "apple",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__1.label"
            },
            {
              "value": "banana",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__2.label"
            },
            {
              "value": "bottle",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__3.label"
            },
            {
              "value": "box",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__4.label"
            },
            {
              "value": "carrot",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__5.label"
            },
            {
              "value": "chat_bubble",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__6.label"
            },
            {
              "value": "check_mark",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__7.label"
            },
            {
              "value": "clipboard",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__8.label"
            },
            {
              "value": "dairy",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__9.label"
            },
            {
              "value": "dairy_free",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__10.label"
            },
            {
              "value": "dryer",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__11.label"
            },
            {
              "value": "eye",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__12.label"
            },
            {
              "value": "fire",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__13.label"
            },
            {
              "value": "gluten_free",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__14.label"
            },
            {
              "value": "heart",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__15.label"
            },
            {
              "value": "iron",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__16.label"
            },
            {
              "value": "leaf",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__17.label"
            },
            {
              "value": "leather",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__18.label"
            },
            {
              "value": "lightning_bolt",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__19.label"
            },
            {
              "value": "lipstick",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__20.label"
            },
            {
              "value": "lock",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__21.label"
            },
            {
              "value": "map_pin",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__22.label"
            },
            {
              "value": "nut_free",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__23.label"
            },
            {
              "value": "pants",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__24.label"
            },
            {
              "value": "paw_print",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__25.label"
            },
            {
              "value": "pepper",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__26.label"
            },
            {
              "value": "perfume",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__27.label"
            },
            {
              "value": "plane",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__28.label"
            },
            {
              "value": "plant",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__29.label"
            },
            {
              "value": "price_tag",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__30.label"
            },
            {
              "value": "question_mark",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__31.label"
            },
            {
              "value": "recycle",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__32.label"
            },
            {
              "value": "return",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__33.label"
            },
            {
              "value": "ruler",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__34.label"
            },
            {
              "value": "serving_dish",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__35.label"
            },
            {
              "value": "shirt",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__36.label"
            },
            {
              "value": "shoe",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__37.label"
            },
            {
              "value": "silhouette",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__38.label"
            },
            {
              "value": "snowflake",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__39.label"
            },
            {
              "value": "star",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__40.label"
            },
            {
              "value": "stopwatch",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__41.label"
            },
            {
              "value": "truck",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__42.label"
            },
            {
              "value": "washing",
              "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.options__43.label"
            }
          ],
          "default": "check_mark",
          "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon.label"
        },
        {
          "type": "image_picker",
          "id": "upload_icon",
          "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.upload_icon.label"
        },
        {
          "type": "range",
          "id": "icon_width",
          "min": 12,
          "max": 32,
          "step": 2,
          "unit": "px",
          "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.icon_width.label",
          "default": 16
        },
        {
          "type": "richtext",
          "id": "row_content",
          "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.row_content.label"
        },
        {
          "type": "page_picker",
          "id": "page",
          "label": "t:sections.collapsible-content.blocks.collapsible_row.settings.page.label"
        }
      ]
    }
  ]
}
{{/schema}}