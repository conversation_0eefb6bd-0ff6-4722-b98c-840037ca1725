{{snippet "stylesheet" href=(asset_url "section-main-password.css")}}
{{snippet "stylesheet" href=(asset_url "snippet-tips-card.css")}}
<style>
  #Password-{{ section.id }} .password__media::after {
    opacity: {{divide section.settings.image_overlay_opacity 100}};
  }

  {{#if section.settings.image_height == 'adapt-image' }}
   @media screen and (max-width: 959px) {
      #Password-{{ section.id }}:not(.password--mobile-bottom) .password__content::before {
        padding-bottom:{{divide 100 section.settings.image.aspect_ratio}}%;
        content: '';
        display: block;
      }
      #Password-{{ section.id }} .password__media::before {
        padding-bottom: {{divide 100 section.settings.image.aspect_ratio}}%;
        content: '';
        display: block;
      }
    }
     @media screen and (min-width: 959px) {
      #Password-{{ section.id }}::before,
      #Password-{{ section.id }} .password__media::before {
        padding-bottom: {{divide 100 section.settings.image.aspect_ratio}}%;
        content: '';
        display: block;
      }
    }
  {{/if}}
</style>

<div class="password-body">
  <div
    id="Password-{{section.id}}"
    class="password
      password--content-align-{{section.settings.desktop_content_alignment}}
      password--content-align-mobile-{{section.settings.mobile_content_alignment}}
      password--{{section.settings.image_height}}
      {{#if section.settings.image_height == 'adapt-image'}} password--adapt{{/if}}
      {{#if section.settings.show_text_below}} password--mobile-bottom{{/if}}
      {{#unless section.settings.show_text_box}} password--desktop-transparent{{/unless}}
      {{#unless section.settings.image}} password__box--no-image{{/unless}}
      "
  >

    {{#if section.settings.show_background_image}}
      <div class="password__media media">
        {{#if section.settings.image}}
          {{snippet "image" data=section.settings.image}}
        {{else}}
          <img src="{{asset_url 'image-password-login.png'}}" alt="background_img" />
        {{/if}}
      </div>
    {{/if}}
    <div class="password__content password__content--{{section.settings.desktop_content_position}} page-width">
      <div
        class="global-card-border-shadow password__wrapper
          color-scheme-{{section.settings.color_scheme}}
          content-container--full-width-mobile"
      >
        {{#for section.blocks as |block|}}
          {{#if block.type == "heading"}}
            <h2 class="password__heading {{block.settings.heading_size}}" {{{block.shopline_attributes}}}>
              {{block.settings.heading}}
            </h2>
          {{/if}}
          {{#if block.type == "paragraph"}}
            <div class="newsletter__subheading rte {{block.settings.text_style}}" {{{block.shopline_attributes}}}>
              {{{block.settings.text}}}
            </div>
          {{/if}}
          {{#if block.type == "email_form"}}
            <div {{{block.shopline_attributes}}}>
              {{#form "customer" class="newsletter-form"}}
                <div class="field">
                  <div class="field__container">
                    {{snippet
                      "input-email"
                      input_class="field__input"
                      input_id="NewsletterForm"
                      input_required=true
                      input_name="contact[email]"
                      input_placeholder=(t "products.product_details.enter_email_address")
                    }}
                    <label for="NewsletterForm" class="field__label body4">
                      {{t "products.product_details.enter_email_address"}}
                    </label>
                  </div>
                  <div class="field__suffix">
                    <button type="submit" class="button button--link">
                      {{snippet "icon-mail"}}
                    </button>
                  </div>
                </div>
                {{#if form.posted_successfully}}
                  {{snippet "tips-card" type="success" text=(t "general.footer.subscribe_success")}}
                {{/if}}
                {{#if form.errors.messages}}
                  {{snippet "tips-card" type="error" text=form.errors.messages}}
                {{/if}}
              {{/form}}

            </div>
          {{/if}}
        {{/for}}
      </div>
    </div>
  </div>
</div>

{{#schema}}
{
  "name": "t:sections.main-password.name",
  "settings": [
    {
      "type": "group_header",
      "label": "t:sections.main-password.settings.group_header__0.label"
    },
    {
      "type": "image_picker",
      "id": "image",
      "label": "t:sections.main-password.settings.image.label"
    },
    {
      "type": "range",
      "id": "image_overlay_opacity",
      "min": 0,
      "max": 100,
      "step": 1,
      "unit": "%",
      "label": "t:sections.main-password.settings.image_overlay_opacity.label",
      "default": 0
    },
    {
      "type": "switch",
      "id": "show_background_image",
      "label": "t:sections.main-password.settings.show_background_image.label",
      "default": true
    },
    {
      "type": "select",
      "id": "image_height",
      "options": [
        {
          "value": "adapt-image",
          "label": "t:sections.main-password.settings.image_height.options__0.label"
        },
        {
          "value": "small",
          "label": "t:sections.main-password.settings.image_height.options__1.label"
        },
        {
          "value": "medium",
          "label": "t:sections.main-password.settings.image_height.options__2.label"
        },
        {
          "value": "large",
          "label": "t:sections.main-password.settings.image_height.options__3.label"
        }
      ],
      "default": "medium",
      "label": "t:sections.main-password.settings.image_height.label",
      "info": "t:sections.main-password.settings.image_height.info"
    },
    {
      "type": "select",
      "id": "desktop_content_position",
      "options": [
        {
          "value": "middle-left",
          "label": "t:sections.main-password.settings.desktop_content_position.options__0.label"
        },
        {
          "value": "middle-center",
          "label": "t:sections.main-password.settings.desktop_content_position.options__1.label"
        },
        {
          "value": "middle-right",
          "label": "t:sections.main-password.settings.desktop_content_position.options__2.label"
        },
        {
          "value": "top-left",
          "label": "t:sections.main-password.settings.desktop_content_position.options__3.label"
        },
        {
          "value": "bottom-left",
          "label": "t:sections.main-password.settings.desktop_content_position.options__4.label"
        },
        {
          "value": "bottom-center",
          "label": "t:sections.main-password.settings.desktop_content_position.options__5.label"
        },
        {
          "value": "top-right",
          "label": "t:sections.main-password.settings.desktop_content_position.options__6.label"
        },
        {
          "value": "bottom-right",
          "label": "t:sections.main-password.settings.desktop_content_position.options__7.label"
        }
      ],
      "default": "middle-center",
      "label": "t:sections.main-password.settings.desktop_content_position.label"
    },
    {
      "type": "switch",
      "id": "show_text_box",
      "default": true,
      "label": "t:sections.main-password.settings.show_text_box.label"
    },
    {
      "type": "select",
      "id": "desktop_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.main-password.settings.desktop_content_alignment.options__0.label"
        },
        {
          "value": "center",
          "label": "t:sections.main-password.settings.desktop_content_alignment.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.main-password.settings.desktop_content_alignment.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.main-password.settings.desktop_content_alignment.label"
    },
    {
      "type": "select",
      "id": "color_scheme",
      "label": "t:sections.main-password.settings.color_scheme.label",
      "options": [
        {
          "value": "none",
          "label": "t:sections.main-password.settings.color_scheme.options__0.label"
        },
        {
          "value": "1",
          "label": "t:sections.main-password.settings.color_scheme.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.main-password.settings.color_scheme.options__2.label"
        },
        {
          "value": "3",
          "label": "t:sections.main-password.settings.color_scheme.options__3.label"
        }
      ],
      "default": "none"
    },
    {
      "type": "group_header",
      "label": "t:sections.main-password.settings.group_header__1.label"
    },
    {
      "type": "select",
      "id": "mobile_content_alignment",
      "options": [
        {
          "value": "left",
          "label": "t:sections.main-password.settings.mobile_content_alignment.options__0.label"
        },
        {
          "value": "center",
          "label": "t:sections.main-password.settings.mobile_content_alignment.options__1.label"
        },
        {
          "value": "right",
          "label": "t:sections.main-password.settings.mobile_content_alignment.options__2.label"
        }
      ],
      "default": "center",
      "label": "t:sections.main-password.settings.mobile_content_alignment.label"
    },
    {
      "type": "switch",
      "id": "show_text_below",
      "default": true,
      "label": "t:sections.main-password.settings.show_text_below.label"
    }
  ],
  "max_blocks": 3,
  "blocks": [
    {
      "type": "heading",
      "name": "t:sections.main-password.blocks.heading.name",
      "limit": 1,
      "settings": [
        {
          "type": "text",
          "id": "heading",
          "default": "Opening soon",
          "label": "t:sections.main-password.blocks.heading.settings.heading.label"
        },
        {
          "type": "select",
          "id": "heading_size",
          "options": [
            {
              "value": "title5",
              "label": "t:sections.main-password.blocks.heading.settings.heading_size.options__0.label"
            },
            {
              "value": "title3",
              "label": "t:sections.main-password.blocks.heading.settings.heading_size.options__1.label"
            },
            {
              "value": "title2",
              "label": "t:sections.main-password.blocks.heading.settings.heading_size.options__2.label"
            }
          ],
          "default": "title3",
          "label": "t:sections.main-password.blocks.heading.settings.heading_size.label"
        }
      ]
    },
    {
      "type": "paragraph",
      "name": "t:sections.main-password.blocks.paragraph.name",
      "limit": 1,
      "settings": [
        {
          "type": "richtext",
          "id": "text",
          "default": "<p>Be the first to know when we launch.</p>",
          "label": "t:sections.main-password.blocks.paragraph.settings.text.label"
        }
      ]
    },
    {
      "type": "email_form",
      "name": "t:sections.main-password.blocks.email_form.name",
      "limit": 1,
      "settings": []
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "heading",
        "settings": {}
      },
      {
        "type": "paragraph",
        "settings": {}
      },
      {
        "type": "email_form",
        "settings": {}
      }
    ],
    "block_order": [
      "heading",
      "paragraph",
      "email_form"
    ]
  }
}
{{/schema}}