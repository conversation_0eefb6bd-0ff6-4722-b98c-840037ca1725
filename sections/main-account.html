{{snippet "stylesheet" href=(asset_url "section-customer-card-container.css")}}
{{snippet "stylesheet" href=(asset_url "section-main-account.css")}}


{{snippet "stylesheet" href=(asset_url "section-customer-datepicker.css")}}
<script src="{{asset_url 'section-customer-lib-air-datepicker.js'}}" defer></script>
<script src="{{asset_url 'section-customer-lib-rolldate.js'}}" defer></script>
<script src="{{asset_url 'section-main-account.js'}}" defer="defer"></script>

<script>
  window.__I18N__ = window.__I18N__ || {};
  window.__I18N__['customer'] = {{{ json (t 'customer') }}};
  window.__I18N__['general'] = {{{ json (t 'general') }}};
</script>

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}

{{#snippet "user-center-container"}}
  <div class="account-section">
    <div>
      {{snippet "account-account"}}
      {{snippet "account-subscription"}}
      {{snippet "account-personal"}}
    </div>
    <div>
      {{snippet "account-address"}}
    </div>
  </div>
{{/snippet}}

{{#schema}}
{
  "name": "t:sections.main-account.name",
  "class": "section",
  "settings": [
    {
      "type": "group_header",
      "label": "t:sections.main-account.settings.group_header__0.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.main-account.settings.padding_top.label",
      "default": 80
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.main-account.settings.padding_bottom.label",
      "default": 80
    }
  ]
}
{{/schema}}