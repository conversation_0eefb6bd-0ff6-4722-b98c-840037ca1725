{{snippet "stylesheet" href=(asset_url "section-main-cart-items.css")}}


{{#if settings.cart_type != "drawer"}}
  {{snippet "stylesheet" href=(asset_url "snippet-cart-item.css")}}
  {{snippet "stylesheet" href=(asset_url "component-cart.css")}}
  <script src="{{asset_url 'component-quantity-input.js'}}" defer></script>
  <script src="{{asset_url 'component-cart.js'}}" defer></script>
{{/if}}

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}
<cart-items class="page-width section-padding {{#if cart.empty}}cart-empty{{/if}}">
  <div
    class="cart__items"
    id="main-cart-items"
    data-id="{{section.id}}"
    data-show-fixed-checkout="{{if (isFalsey cart.empty) and cart.total_discount > 0}}"
    data-enable-discount="{{cart.discount_enable_cart}}"
  >
    <div class="cart-title-wrapper">
      <h1 class="title4 text-uppercase">{{t "general.header.cart"}}</h1>
      <a href="{{routes.all_products_collection_url}}" class="body3 continue-shopping">
        {{t "cart.checkout_proceeding.continue_shopping"}}
      </a>
    </div>
    <div class="cart__warnings">
      <div class="cart__empty-text-wrapper">
        <h1 class="cart__empty-text body3">{{t "cart.item.empty_cart"}}</h1>
        <a href="{{routes.all_products_collection_url}}" class="button body3 fw-bold">
          {{t "cart.checkout_proceeding.continue_shopping"}}
        </a>
      </div>
      {{snippet "cart-empty-recommend-product" grid_class="grid grid-cols-2 grid-cols-4-desktop" title_class="title4"}}
    </div>
    <div class="main-cart-items-container">
      <div class="main-cart-items-header">
        <div class="cart__header-item">{{t "products.product_list.products"}}</div>
        <div class="cart__header-quantity">{{t "products.product_details.amount"}}</div>
        <div class="cart__header-total">{{t "cart.payment.total_sum"}}</div>
      </div>
      <div class="main-cart-items-wrapper">
        {{#for cart.items as |item|}}
          {{snippet "cart-item" item=item}}
        {{/for}}
      </div>
    </div>
  </div>
</cart-items>
<style>
    /* 解决购物车弹窗bug */
    .cart-fixed-checkout.cart-footer__fixed-checkout.display-none-desktop.invisible {
    display: none;
}
</style>
{{#schema}}
{
  "name": "t:sections.main-cart-items.name",
  "class": "section",
  "settings": [
    {
      "type": "group_header",
      "label": "t:sections.main-cart-items.settings.group_header__0.label"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.main-cart-items.settings.padding_top.label",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.main-cart-items.settings.padding_bottom.label",
      "default": 60
    }
  ]
}
{{/schema}}