{{snippet "stylesheet" href=(asset_url "section-slideshow.css")}}
<script src="{{asset_url 'component-slider.js'}}" defer></script>

{{#if request.design_mode}}
  <script src="{{asset_url 'theme-editor.js'}}" defer="defer"></script>
{{/if}}

{{assign "pc_height" section.settings.section_height}}
{{assign "mobile_height" section.settings.mobile_height}}


{{#if (length section.blocks)}}
  {{assign "first_block" (first section.blocks)}}
  {{#if pc_height == "natural" and (not first_block.settings.image)}}
    {{assign "pc_height" "500px"}}
  {{/if}}

  {{assign
    "mobile_image"
    (default
      (ternary first_block.settings.image_mobile first_block.settings.image_mobile null)
      (ternary first_block.settings.image first_block.settings.image null)
    )
  }}
  {{#if mobile_height == "natural" and (not mobile_image)}}
    {{assign "mobile_height" "450px"}}
  {{/if}}
{{/if}}

<slider-component
  class="slideshow
    slideshow-layout--text-{{section.settings.mobile_content_layout}}
    {{#if mobile_height == '100vh'}}slideshow--mobile-fullscreen{{/if}}
    {{#unless section.settings.full_screen}}page-width section--padding{{/unless}}"
  style="
    --slideshow-speed: {{section.settings.autoplay_speed}}s;
    --slideshow-pc-height: {{pc_height}};
    --slideshow-mobile-height: {{mobile_height}};
    {{~#for section.blocks as |block|~}}
    {{~#if block.settings.text_color != "rgba(0,0,0,0)"~}}
      --slideshow-text-color-{{plus forloop.index0 1}}: {{block.settings.text_color.red}}, {{block.settings.text_color.green}}, {{block.settings.text_color.blue}};
    {{~else if block.settings.pc_text_color != "rgba(0,0,0,0)"~}}
      --slideshow-text-color-{{plus forloop.index0 1}}: {{block.settings.pc_text_color.red}}, {{block.settings.pc_text_color.green}}, {{block.settings.pc_text_color.blue}};
    {{~/if~}}
  {{~/for~}}
  "
  loop="true"
  autoplay="{{section.settings.autoplay}}"
  speed="{{section.settings.autoplay_speed}}"
>
  <div id="Slider-{{section.id}}" class="slideshow__slider slider">
    {{#for section.blocks as |block|}}
      {{assign "first_link_in_image" (if block.settings.link and (if (length block.settings.link_text) == 0))}}
      {{assign "ele" (ternary first_link_in_image "a" "div")}}
      <div
        {{{block.shopline_attributes}}}
        id="Slide-{{block.id}}"
        class="slideshow__slide slider__slide {{#if forloop.index0 > 0}}slideshow__slide--adapt{{/if}}"
        style="
            --slideshow-overlay-opacity: {{block.settings.overlay_opacity}};
            --slideshow-title-size: {{block.settings.title_size}}px;
            --slideshow-pc-text-position-vertical: {{first (split block.settings.pc_text_position ' ')}};
            --slideshow-pc-text-position-horizontal: {{last (split block.settings.pc_text_position ' ')}};
            --slideshow-mb-text-position-vertical: {{first (split block.settings.mb_text_position ' ')}};
            --slideshow-mb-text-position-horizontal: {{last (split block.settings.mb_text_position ' ')}};
            --slideshow-pc-text-align: {{block.settings.pc_text_align}};
            --slideshow-pc-text-color: {{block.settings.pc_text_color}};
            --slideshow-mobile-text-align: {{block.settings.mobile_text_align}};
            --slideshow-text-color--mb: {{#if block.settings.text_color != 'rgba(0,0,0,0)'}}{{block.settings.text_color}}{{else if block.settings.pc_text_color != "rgba(0,0,0,0)"}}{{block.settings.pc_text_color}}{{else}}{{settings.color_text}}{{/if}};
            --slideshow-text-area-bg-color: {{~#if block.settings.text_area_background_color != 'rgba(0,0,0,0)'~}}
          {{block.settings.text_area_background_color}}
        {{~else~}}
          rgb({{times settings.color_image_background.red 0.66}}, {{times settings.color_image_background.green 0.66}}, {{times settings.color_image_background.blue 0.66}})
        {{~/if~}};
          "
      >
        {{#capture "image_show_position"}}
          {{#case block.settings.image_show_position}}
            {{#when "left_top"}}
              20% 0
            {{/when}}
            {{#when "top_center"}}
              top center
            {{/when}}
            {{#when "right_top"}}
              80% 0
            {{/when}}
            {{#when "left_left"}}
              20% 50%
            {{/when}}
            {{#when "center_center"}}
              center center
            {{/when}}
            {{#when "right_right"}}
              80% 50%
            {{/when}}
            {{#when "right_bottom"}}
              20% 100%
            {{/when}}
            {{#when "bottom_center"}}
              bottom center
            {{/when}}
            {{#when "right_center"}}
              80% 100%
            {{/when}}
          {{/case}}
        {{/capture}}

        
        <{{ele}}
          {{#if first_link_in_image}}href="{{block.settings.link}}"{{/if}}
          class="slideshow-slide__media"
          style="--image-show-position: {{trim image_show_position}};"
        >
          
          {{#if block.settings.image}}
            {{snippet
              "image"
              class="slideshow-slide__image display-none display-block-desktop"
              data=block.settings.image
            }}
          {{else}}
            <div class="slideshow-slide__image-placeholder display-none display-block-desktop">
              {{placeholder_svg_tag "lifestyle-1"}}
            </div>
          {{/if}}

          
          {{assign
            "mobileImage"
            (default
              (ternary block.settings.image_mobile block.settings.image_mobile null)
              (ternary block.settings.image block.settings.image null)
            )
          }}
          {{#if mobileImage}}
            {{snippet "image" class="slideshow-slide__image display-none-desktop" data=mobileImage}}
          {{else}}
            <div class="slideshow-slide__image-placeholder display-none-desktop">
              {{placeholder_svg_tag "lifestyle-1"}}
            </div>
          {{/if}}
        </{{ele}}>

        {{~#capture "pc_content_padding"~}}
          {{#case block.settings.pc_content_width}}
            {{#when "400px" "600px"}}
              15%
            {{/when}}
            {{#when "800px"}}
              8%
            {{/when}}
            {{#when "1200px"}}
              5%
            {{/when}}
          {{/case}}
        {{~/capture~}}

        <div
          class="slideshow-slide__text-wrapper page-width
            {{#if block.settings.pc_content_width != 'auto'}}slideshow-slide__text-max-width{{/if}}"
          {{#if block.settings.pc_content_width != "auto"}}style="--pc-content-width:
            {{block.settings.pc_content_width}}; --pc-content-padding:
            {{trim pc_content_padding}};"{{/if}}
        >
          <div class="slideshow-slide__text">
            {{#if block.settings.text_mask}}
              {{#if block.settings.text_mask_color == "deep"}}
                <div class="featured-slideshow-slide__text-mask mask-deep"></div>
              {{/if}}
              {{#if block.settings.text_mask_color == "light"}}
                <div class="featured-slideshow-slide__text-mask mask-light"></div>
              {{/if}}
            {{/if}}

            {{#if block.settings.sub_title}}
              <p class="slideshow-slide__sub-title body3" data-animation>{{block.settings.sub_title}}</p>
            {{/if}}
            {{#if block.settings.title}}
              <h2 class="slideshow-slide__title title1" data-animation>{{newline_to_br block.settings.title}}</h2>
            {{/if}}
            {{#if block.settings.subheading}}
              <p class="slideshow-slide__desc body3" data-animation>{{block.settings.subheading}}</p>
            {{/if}}
            <div class="slideshow-slide__buttons">
              {{#if block.settings.link_text}}
                <a
                  class="button slideshow-slide__button {{#if block.settings.is_profile_link}}button--secondary{{/if}}"
                  style="
                    {{#if block.settings.is_profile_link}}background-color: transparent;{{/if}}
                    {{#if block.settings.btn_text_color != 'rgba(0,0,0,0)'}}
                    --color-button-text: {{block.settings.btn_text_color.red}}, {{block.settings.btn_text_color.green}}, {{block.settings.btn_text_color.blue}};
                  {{/if}}
                    {{#if block.settings.btn_bg_color != 'rgba(0,0,0,0)'}}
                    --color-button-background: {{block.settings.btn_bg_color.red}}, {{block.settings.btn_bg_color.green}}, {{block.settings.btn_bg_color.blue}};
                  {{/if}}
                    {{#if block.settings.btn_border_color != 'rgba(0,0,0,0)'}}
                    --slideshow-btn-border-color: {{block.settings.btn_border_color.red}}, {{block.settings.btn_border_color.green}}, {{block.settings.btn_border_color.blue}};
                  {{/if}}"
                  title="{{block.settings.link_text}}"
                  href="{{#if block.settings.link}}{{block.settings.link}}{{else}}javascript:;{{/if}}"
                >
                  {{block.settings.link_text}}
                </a>
              {{/if}}
              {{#if block.settings.link_text_2}}
                <a
                  class="button slideshow-slide__button {{#if block.settings.is_profile_link2}}button--secondary{{/if}}"
                  style="
                    {{#if block.settings.is_profile_link2}}background-color: transparent;{{/if}}
                    {{#if block.settings.btn_text_color != 'rgba(0,0,0,0)'}}
                    --color-button-text: {{block.settings.btn_text_color.red}}, {{block.settings.btn_text_color.green}}, {{block.settings.btn_text_color.blue}};
                  {{/if}}
                    {{#if block.settings.btn_bg_color != 'rgba(0,0,0,0)'}}
                    --color-button-background: {{block.settings.btn_bg_color.red}}, {{block.settings.btn_bg_color.green}}, {{block.settings.btn_bg_color.blue}};
                  {{/if}}
                    {{#if block.settings.btn_border_color != 'rgba(0,0,0,0)'}}
                    --slideshow-btn-border-color: {{block.settings.btn_border_color.red}}, {{block.settings.btn_border_color.green}}, {{block.settings.btn_border_color.blue}};
                  {{/if}}"
                  title="{{block.settings.link_text_2}}"
                  href="{{#if block.settings.link_2}}{{block.settings.link_2}}{{else}}javascript:;{{/if}}"
                >
                  {{block.settings.link_text_2}}
                </a>
              {{/if}}
            </div>
          </div>
        </div>
      </div>
    {{/for}}
  </div>

  {{#if section.blocks.length > 1}}
    <div class="slideshow__control slideshow__control--{{section.settings.style}}">
      {{#if section.settings.style == "arrows"}}
        <div class="control__arrow-buttons">
          <button class="control__arrow-button" name="previous">
            {{snippet "icon-arrow"}}
          </button>
          <button class="control__arrow-button" name="next">
            {{snippet "icon-arrow"}}
          </button>
        </div>
      {{else if section.settings.style == "bars"}}
        <div class="control__bars">
          {{#for section.blocks}}
            <button class="slider-counter__link control__bar" name="pager" data-index="{{forloop.index0}}"></button>
          {{/for}}
        </div>
      {{else if section.settings.style == "dots"}}
        <div class="control__dots">
          {{#for section.blocks}}
            <button class="slider-counter__link control__dot" name="pager" data-index="{{forloop.index0}}"></button>
          {{/for}}
        </div>
      {{/if}}
    </div>
  {{/if}}
</slider-component>

{{#schema}}
{
  "name": "t:sections.slideshow.name",
  "class": "section index-section--hero",
  "block_info": "t:sections.slideshow.block_info",
  "max_blocks": 5,
  "settings": [
    {
      "type": "select",
      "id": "section_height",
      "label": "t:sections.slideshow.settings.section_height.label",
      "default": "natural",
      "options": [
        {
          "label": "t:sections.slideshow.settings.section_height.options__0.label",
          "value": "natural"
        },
        {
          "label": "t:sections.slideshow.settings.section_height.options__1.label",
          "value": "450px"
        },
        {
          "label": "t:sections.slideshow.settings.section_height.options__2.label",
          "value": "550px"
        },
        {
          "label": "t:sections.slideshow.settings.section_height.options__3.label",
          "value": "650px"
        },
        {
          "label": "t:sections.slideshow.settings.section_height.options__4.label",
          "value": "750px"
        },
        {
          "label": "t:sections.slideshow.settings.section_height.options__5.label",
          "value": "100vh"
        }
      ]
    },
    {
      "type": "select",
      "id": "mobile_height",
      "label": "t:sections.slideshow.settings.mobile_height.label",
      "default": "natural",
      "options": [
        {
          "label": "t:sections.slideshow.settings.mobile_height.options__0.label",
          "value": "natural"
        },
        {
          "label": "t:sections.slideshow.settings.mobile_height.options__1.label",
          "value": "250px"
        },
        {
          "label": "t:sections.slideshow.settings.mobile_height.options__2.label",
          "value": "300px"
        },
        {
          "label": "t:sections.slideshow.settings.mobile_height.options__3.label",
          "value": "400px"
        },
        {
          "label": "t:sections.slideshow.settings.mobile_height.options__4.label",
          "value": "500px"
        },
        {
          "label": "t:sections.slideshow.settings.mobile_height.options__5.label",
          "value": "100vh"
        }
      ]
    },
    {
      "type": "switch",
      "id": "full_screen",
      "label": "t:sections.slideshow.settings.full_screen.label",
      "default": true
    },
    {
      "type": "select",
      "id": "style",
      "label": "t:sections.slideshow.settings.style.label",
      "default": "dots",
      "options": [
        {
          "value": "arrows",
          "label": "t:sections.slideshow.settings.style.options__0.label"
        },
        {
          "value": "bars",
          "label": "t:sections.slideshow.settings.style.options__1.label"
        },
        {
          "value": "dots",
          "label": "t:sections.slideshow.settings.style.options__2.label"
        }
      ]
    },
    {
      "type": "switch",
      "id": "autoplay",
      "label": "t:sections.slideshow.settings.autoplay.label",
      "default": true
    },
    {
      "type": "range",
      "id": "autoplay_speed",
      "label": "t:sections.slideshow.settings.autoplay_speed.label",
      "default": 7,
      "min": 3,
      "max": 10,
      "step": 1,
      "unit": "t:sections.slideshow.settings.autoplay_speed.unit"
    },
    {
      "type": "select",
      "id": "mobile_content_layout",
      "label": "t:sections.slideshow.settings.mobile_content_layout.label",
      "default": "inside",
      "options": [
        {
          "value": "inside",
          "label": "t:sections.slideshow.settings.mobile_content_layout.options__0.label"
        },
        {
          "value": "bottom",
          "label": "t:sections.slideshow.settings.mobile_content_layout.options__1.label"
        },
        {
          "value": "overlap",
          "label": "t:sections.slideshow.settings.mobile_content_layout.options__2.label"
        }
      ]
    }
  ],
  "blocks": [
    {
      "type": "image",
      "icon": "image",
      "name": "t:sections.slideshow.blocks.image.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.slideshow.blocks.image.settings.image.label"
        },
        {
          "type": "image_picker",
          "id": "image_mobile",
          "label": "t:sections.slideshow.blocks.image.settings.image_mobile.label"
        },
        {
          "type": "select",
          "id": "image_show_position",
          "label": "t:sections.slideshow.blocks.image.settings.image_show_position.label",
          "default": "center_center",
          "options": [
            {
              "value": "left_top",
              "label": "t:sections.slideshow.blocks.image.settings.image_show_position.options__0.label"
            },
            {
              "value": "top_center",
              "label": "t:sections.slideshow.blocks.image.settings.image_show_position.options__1.label"
            },
            {
              "value": "right_top",
              "label": "t:sections.slideshow.blocks.image.settings.image_show_position.options__2.label"
            },
            {
              "value": "left_left",
              "label": "t:sections.slideshow.blocks.image.settings.image_show_position.options__3.label"
            },
            {
              "value": "center_center",
              "label": "t:sections.slideshow.blocks.image.settings.image_show_position.options__4.label"
            },
            {
              "value": "right_right",
              "label": "t:sections.slideshow.blocks.image.settings.image_show_position.options__5.label"
            },
            {
              "value": "right_bottom",
              "label": "t:sections.slideshow.blocks.image.settings.image_show_position.options__6.label"
            },
            {
              "value": "bottom_center",
              "label": "t:sections.slideshow.blocks.image.settings.image_show_position.options__7.label"
            },
            {
              "value": "right_center",
              "label": "t:sections.slideshow.blocks.image.settings.image_show_position.options__8.label"
            }
          ]
        },
        {
          "type": "range",
          "id": "overlay_opacity",
          "label": "t:sections.slideshow.blocks.image.settings.overlay_opacity.label",
          "default": 0,
          "min": 0,
          "max": 100,
          "step": 2,
          "unit": "%"
        },
        {
          "type": "switch",
          "label": "t:sections.slideshow.blocks.image.settings.text_mask.label",
          "id": "text_mask",
          "default": false
        },
        {
          "type": "select",
          "label": "t:sections.slideshow.blocks.image.settings.text_mask_color.label",
          "id": "text_mask_color",
          "default": "deep",
          "options": [
            {
              "label": "t:sections.slideshow.blocks.image.settings.text_mask_color.options__0.label",
              "value": "deep"
            },
            {
              "label": "t:sections.slideshow.blocks.image.settings.text_mask_color.options__1.label",
              "value": "light"
            }
          ]
        },
        {
          "type": "select",
          "id": "pc_text_position",
          "label": "t:sections.slideshow.blocks.image.settings.pc_text_position.label",
          "default": "center flex-end",
          "options": [
            {
              "value": "flex-start flex-start",
              "label": "t:sections.slideshow.blocks.image.settings.pc_text_position.options__0.label"
            },
            {
              "value": "flex-start center",
              "label": "t:sections.slideshow.blocks.image.settings.pc_text_position.options__1.label"
            },
            {
              "value": "flex-start flex-end",
              "label": "t:sections.slideshow.blocks.image.settings.pc_text_position.options__2.label"
            },
            {
              "value": "center flex-start",
              "label": "t:sections.slideshow.blocks.image.settings.pc_text_position.options__3.label"
            },
            {
              "value": "center center",
              "label": "t:sections.slideshow.blocks.image.settings.pc_text_position.options__4.label"
            },
            {
              "value": "center flex-end",
              "label": "t:sections.slideshow.blocks.image.settings.pc_text_position.options__5.label"
            },
            {
              "value": "flex-end flex-start",
              "label": "t:sections.slideshow.blocks.image.settings.pc_text_position.options__6.label"
            },
            {
              "value": "flex-end center",
              "label": "t:sections.slideshow.blocks.image.settings.pc_text_position.options__7.label"
            },
            {
              "value": "flex-end flex-end",
              "label": "t:sections.slideshow.blocks.image.settings.pc_text_position.options__8.label"
            }
          ]
        },
        {
          "type": "select",
          "id": "mb_text_position",
          "label": "t:sections.slideshow.blocks.image.settings.mb_text_position.label",
          "default": "center center",
          "options": [
            {
              "value": "flex-start flex-start",
              "label": "t:sections.slideshow.blocks.image.settings.mb_text_position.options__0.label"
            },
            {
              "value": "flex-start center",
              "label": "t:sections.slideshow.blocks.image.settings.mb_text_position.options__1.label"
            },
            {
              "value": "flex-start flex-end",
              "label": "t:sections.slideshow.blocks.image.settings.mb_text_position.options__2.label"
            },
            {
              "value": "center flex-start",
              "label": "t:sections.slideshow.blocks.image.settings.mb_text_position.options__3.label"
            },
            {
              "value": "center center",
              "label": "t:sections.slideshow.blocks.image.settings.mb_text_position.options__4.label"
            },
            {
              "value": "center flex-end",
              "label": "t:sections.slideshow.blocks.image.settings.mb_text_position.options__5.label"
            },
            {
              "value": "flex-end flex-start",
              "label": "t:sections.slideshow.blocks.image.settings.mb_text_position.options__6.label"
            },
            {
              "value": "flex-end center",
              "label": "t:sections.slideshow.blocks.image.settings.mb_text_position.options__7.label"
            },
            {
              "value": "flex-end flex-end",
              "label": "t:sections.slideshow.blocks.image.settings.mb_text_position.options__8.label"
            }
          ]
        },
        {
          "type": "color",
          "id": "pc_text_color",
          "label": "t:sections.slideshow.blocks.image.settings.pc_text_color.label",
          "default": "rgba(0,0,0,1)"
        },
        {
          "label": "t:sections.slideshow.blocks.image.settings.btn_bg_color.label",
          "type": "color",
          "id": "btn_bg_color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "label": "t:sections.slideshow.blocks.image.settings.btn_text_color.label",
          "type": "color",
          "id": "btn_text_color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "label": "t:sections.slideshow.blocks.image.settings.btn_border_color.label",
          "type": "color",
          "id": "btn_border_color",
          "default": "rgba(0,0,0,0)"
        },
        {
          "id": "pc_text_align",
          "type": "select",
          "label": "t:sections.slideshow.blocks.image.settings.pc_text_align.label",
          "default": "center",
          "options": [
            {
              "value": "left",
              "label": "t:sections.slideshow.blocks.image.settings.pc_text_align.options__0.label"
            },
            {
              "value": "center",
              "label": "t:sections.slideshow.blocks.image.settings.pc_text_align.options__1.label"
            },
            {
              "value": "right",
              "label": "t:sections.slideshow.blocks.image.settings.pc_text_align.options__2.label"
            }
          ]
        },
        {
          "id": "mobile_text_align",
          "type": "select",
          "label": "t:sections.slideshow.blocks.image.settings.mobile_text_align.label",
          "default": "center",
          "options": [
            {
              "value": "left",
              "label": "t:sections.slideshow.blocks.image.settings.mobile_text_align.options__0.label"
            },
            {
              "value": "center",
              "label": "t:sections.slideshow.blocks.image.settings.mobile_text_align.options__1.label"
            },
            {
              "value": "right",
              "label": "t:sections.slideshow.blocks.image.settings.mobile_text_align.options__2.label"
            }
          ]
        },
        {
          "type": "group_header",
          "label": "t:sections.slideshow.blocks.image.settings.group_header__0.label"
        },
        {
          "type": "text",
          "id": "sub_title",
          "label": "t:sections.slideshow.blocks.image.settings.sub_title.label"
        },
        {
          "type": "textarea",
          "id": "title",
          "label": "t:sections.slideshow.blocks.image.settings.title.label",
          "default": "Two Line\nTitle Slide",
          "limit": 500
        },
        {
          "type": "range",
          "id": "title_size",
          "label": "t:sections.slideshow.blocks.image.settings.title_size.label",
          "default": 70,
          "min": 40,
          "max": 100,
          "unit": "px"
        },
        {
          "type": "text",
          "id": "subheading",
          "label": "t:sections.slideshow.blocks.image.settings.subheading.label",
          "default": "And optional subtext"
        },
        {
          "type": "select",
          "id": "pc_content_width",
          "label": "t:sections.slideshow.blocks.image.settings.pc_content_width.label",
          "default": "auto",
          "options": [
            {
              "value": "auto",
              "label": "t:sections.slideshow.blocks.image.settings.pc_content_width.options__0.label"
            },
            {
              "value": "400px",
              "label": "400"
            },
            {
              "value": "600px",
              "label": "600"
            },
            {
              "value": "800px",
              "label": "800"
            },
            {
              "value": "1200px",
              "label": "1200"
            }
          ]
        },
        {
          "type": "color",
          "id": "text_color",
          "label": "t:sections.slideshow.blocks.image.settings.text_color.label",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "color",
          "id": "text_area_background_color",
          "label": "t:sections.slideshow.blocks.image.settings.text_area_background_color.label",
          "default": "rgba(0,0,0,0)"
        },
        {
          "type": "group_header",
          "label": "t:sections.slideshow.blocks.image.settings.group_header__1.label",
          "info": "t:sections.slideshow.blocks.image.settings.group_header__1.info"
        },
        {
          "type": "text",
          "id": "link_text",
          "label": "t:sections.slideshow.blocks.image.settings.link_text.label",
          "default": "Shop this"
        },
        {
          "type": "url",
          "id": "link",
          "label": "t:sections.slideshow.blocks.image.settings.link.label"
        },
        {
          "type": "switch",
          "id": "is_profile_link",
          "label": "t:sections.slideshow.blocks.image.settings.is_profile_link.label",
          "default": false
        },
        {
          "type": "text",
          "id": "link_text_2",
          "label": "t:sections.slideshow.blocks.image.settings.link_text_2.label",
          "default": "Shop all"
        },
        {
          "type": "url",
          "id": "link_2",
          "label": "t:sections.slideshow.blocks.image.settings.link_2.label"
        },
        {
          "type": "switch",
          "id": "is_profile_link2",
          "label": "t:sections.slideshow.blocks.image.settings.is_profile_link2.label",
          "default": false
        }
      ]
    }
  ],
  "presets": [
    {
      "category_index": 1,
      "category": "t:sections.slideshow.presets.presets__0.category",
      "name": "t:sections.slideshow.presets.presets__0.name",
      "settings": {
        "section_height": "natural",
        "mobile_height": "natural",
        "full_screen": true,
        "style": "dots",
        "autoplay": true,
        "autoplay_speed": 7
      },
      "blocks": [
        {
          "type": "image",
          "icon": "image",
          "settings": {
            "image": {},
            "image_mobile": {},
            "image_show_position": "center_center",
            "overlay_opacity": 0,
            "pc_text_position": "center flex-end",
            "mb_text_position": "center center",
            "pc_text_align": "center",
            "mobile_text_align": "center",
            "title": "Two Line\nTitle Slide",
            "title_size": 70,
            "sub_title": "",
            "subheading": "And optional subtext",
            "pc_content_width": "auto",
            "link_text": "Shop this",
            "is_profile_link": false,
            "link_text_2": "Shop all",
            "is_profile_link2": false,
            "pc_text_color": "rgba(0,0,0)",
            "text_color": "rgba(0,0,0,0)",
            "text_area_background_color": "rgba(0,0,0,0)"
          }
        },
        {
          "type": "image",
          "icon": "image",
          "settings": {
            "image": {},
            "image_mobile": {},
            "image_show_position": "center_center",
            "overlay_opacity": 0,
            "pc_text_position": "center flex-end",
            "mb_text_position": "center center",
            "pc_text_align": "center",
            "mobile_text_align": "center",
            "title": "Two Line\nTitle Slide",
            "title_size": 70,
            "sub_title": "",
            "subheading": "And optional subtext",
            "pc_content_width": "auto",
            "link_text": "Shop this",
            "is_profile_link": false,
            "link_text_2": "Shop all",
            "is_profile_link2": false,
            "pc_text_color": "rgba(0,0,0)",
            "text_color": "rgba(0,0,0,0)",
            "text_area_background_color": "rgba(0,0,0,0)"
          }
        }
      ]
    }
  ]
}
{{/schema}}