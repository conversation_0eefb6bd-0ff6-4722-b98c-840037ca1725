{{snippet "stylesheet" href=(asset_url "section-text-with-image.css")}}
{{snippet "stylesheet" href=(asset_url "component-price.css")}}

{{snippet
  "section-padding-creator"
  section_id=section.id
  padding_top=section.settings.padding_top
  padding_bottom=section.settings.padding_bottom
}}

{{assign "product_1" section.settings.product_1}}
{{assign "product_2" section.settings.product_2}}

<div class="section-padding page-width">
  
  <div class="text-with-image__image__container1">
    
    <div class="text-with-image__label1 animation-delay-show-container display-none-desktop">
      {{#if section.settings.layout == "title_left"}}
        <div class="rte text-with-image__desc body3">{{{section.settings.content}}}</div>
      {{else}}
        <div class="text-with-image__title {{section.settings.title_font_size}}">{{section.settings.title}}</div>
        <a
          href="{{#if section.settings.button_link}}{{section.settings.button_link}}{{else}}javascript:;{{/if}}"
          class="text-with-image__button button--link body3 text-uppercase"
        >{{section.settings.button_text}}</a>
      {{/if}}
    </div>

    <div class="text-with-image__image text-with-image__image-{{section.settings.image_height}}">
      {{#if section.settings.image_1}}
        <a
          class="animation-delay-show-container"
          href="{{#if section.settings.url_1}}{{section.settings.url_1}}{{else}}javascript:;{{/if}}"
        >
          {{snippet "image" data=section.settings.image_1}}</a>
      {{else}}
        {{placeholder_svg_tag "image" "placeholder"}}
      {{/if}}

      {{#if section.settings.show_image_line}}
        <div class="text-with-image__image-line"></div>
      {{/if}}

      
      {{#if product_1}}
        <a
          class="animation-delay-show-container text-with-image__product-container"
          href="{{#if product_1.url}}{{product_1.url}}{{else}}javascript:;{{/if}}"
        >
          <div class="text-with-image__product-label display-none display-block-desktop">
            <div class="body3 text-with-image__product-label-name">{{default
                product_1.title
                (t "onboarding.product_title")
              }}</div>
            <div class="body2 fw-bold text-with-image__product-label-price">{{snippet
                "price"
                product=product_1
                use_variant=true
              }}</div>
          </div>

          <div class="text-with-image__product-image">
            {{snippet "image" data=product_1.featured_media}}
          </div>
        </a>
      {{/if}}
    </div>

    
    <div class="text-with-image__label1 animation-delay-show-container display-none display-block-desktop">
      {{#if section.settings.layout == "title_left"}}
        <div class="rte text-with-image__desc body3">{{{section.settings.content}}}</div>
      {{else}}
        <div class="text-with-image__title {{section.settings.title_font_size}}">{{section.settings.title}}</div>
        {{snippet
          "link"
          classes="text-with-image__button button--link body3 text-uppercase"
          href=section.settings.button_link
          text=section.settings.button_text
        }}
      {{/if}}
    </div>
  </div>

  
  <div class="text-with-image__image__container2">
    
    <div class="text-with-image__label2 animation-delay-show-container display-none display-block-desktop">
      {{#if section.settings.layout == "title_left"}}
        <div class="text-with-image__title {{section.settings.title_font_size}}">{{section.settings.title}}</div>
        {{snippet
          "link"
          classes="text-with-image__button button--link body3 text-uppercase"
          href=section.settings.button_link
          text=section.settings.button_text
        }}
      {{else}}
        <div class="rte text-with-image__desc body3">{{{section.settings.content}}}</div>
      {{/if}}
    </div>

    <div
      class="text-with-image__image2
        text-with-image__image-{{section.settings.image_height}}
        animation-delay-show-container"
    >
      {{#if section.settings.image_2}}
        <a href="{{#if section.settings.url_2}}{{section.settings.url_2}}{{else}}javascript:;{{/if}}">
          {{snippet "image" data=section.settings.image_2}}
        </a>
      {{else}}
        {{placeholder_svg_tag "image" "placeholder"}}
      {{/if}}

      
      {{#if product_2}}
        <a
          class="text-with-image__product-container2 animation-delay-show-container"
          href="{{#if product_2.url}}{{product_2.url}}{{else}}javascript:;{{/if}}"
        >
          <div class="text-with-image__product-label display-none display-block-desktop">
            <div class="body3 text-with-image__product-label-name">{{default
                product_2.title
                (t "onboarding.product_title")
              }}</div>
            <div class="body2 fw-bold text-with-image__product-label-price">{{snippet
                "price"
                product=product_2
                use_variant=true
              }}</div>
          </div>

          <div class="text-with-image__product-image">
            {{snippet "image" data=product_2.featured_media}}
          </div>
        </a>
      {{/if}}
    </div>

    
    <div class="text-with-image__label2 animation-delay-show-container display-none-desktop">
      {{#if section.settings.layout == "title_left"}}
        <div class="text-with-image__title {{section.settings.title_font_size}}">{{section.settings.title}}</div>
        <a
          href="{{#if section.settings.button_link}}{{section.settings.button_link}}{{else}}javascript:;{{/if}}"
          class="text-with-image__button button--link text-uppercase body3"
        >{{section.settings.button_text}}</a>
      {{else}}
        <div class="rte text-with-image__desc body3">{{{section.settings.content}}}</div>
      {{/if}}
    </div>
  </div>
</div>

{{#schema}}
{
  "blocks": [],
  "name": "t:sections.text-with-image.name",
  "settings": [
    {
      "default": "title_right",
      "id": "layout",
      "info": "",
      "label": "t:sections.text-with-image.settings.layout.label",
      "options": [
        {
          "label": "t:sections.text-with-image.settings.layout.options__0.label",
          "value": "title_left"
        },
        {
          "label": "t:sections.text-with-image.settings.layout.options__1.label",
          "value": "title_right"
        }
      ],
      "type": "select"
    },
    {
      "label": "t:sections.text-with-image.settings.group_header__0.label",
      "type": "group_header"
    },
    {
      "default": "Image with text",
      "id": "title",
      "info": "",
      "label": "t:sections.text-with-image.settings.title.label",
      "type": "textarea"
    },
    {
      "default": "title3",
      "id": "title_font_size",
      "info": "",
      "label": "t:sections.text-with-image.settings.title_font_size.label",
      "options": [
        {
          "label": "t:sections.text-with-image.settings.title_font_size.options__0.label",
          "value": "title5"
        },
        {
          "label": "t:sections.text-with-image.settings.title_font_size.options__1.label",
          "value": "title3"
        },
        {
          "label": "t:sections.text-with-image.settings.title_font_size.options__2.label",
          "value": "title2"
        }
      ],
      "type": "select"
    },
    {
      "default": "Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.",
      "id": "content",
      "info": "",
      "label": "t:sections.text-with-image.settings.content.label",
      "type": "richtext"
    },
    {
      "default": "Optional button",
      "id": "button_text",
      "info": "",
      "label": "t:sections.text-with-image.settings.button_text.label",
      "type": "text"
    },
    {
      "default": "",
      "id": "button_link",
      "info": "",
      "label": "t:sections.text-with-image.settings.button_link.label",
      "type": "url"
    },
    {
      "label": "t:sections.text-with-image.settings.group_header__1.label",
      "type": "group_header"
    },
    {
      "id": "image_1",
      "info": "t:sections.text-with-image.settings.image_1.info",
      "label": "t:sections.text-with-image.settings.image_1.label",
      "type": "image_picker"
    },
    {
      "default": "",
      "id": "url_1",
      "info": "",
      "label": "t:sections.text-with-image.settings.url_1.label",
      "type": "url"
    },
    {
      "default": "",
      "id": "product_1",
      "info": "",
      "label": "t:sections.text-with-image.settings.product_1.label",
      "type": "product_picker"
    },
    {
      "id": "image_2",
      "info": "t:sections.text-with-image.settings.image_2.info",
      "label": "t:sections.text-with-image.settings.image_2.label",
      "type": "image_picker"
    },
    {
      "default": "",
      "id": "url_2",
      "info": "",
      "label": "t:sections.text-with-image.settings.url_2.label",
      "type": "url"
    },
    {
      "default": "",
      "id": "product_2",
      "info": "",
      "label": "t:sections.text-with-image.settings.product_2.label",
      "type": "product_picker"
    },
    {
      "default": true,
      "id": "show_image_line",
      "info": "",
      "label": "t:sections.text-with-image.settings.show_image_line.label",
      "type": "switch"
    },
    {
      "default": "lg",
      "id": "image_height",
      "info": "",
      "label": "t:sections.text-with-image.settings.image_height.label",
      "options": [
        {
          "label": "t:sections.text-with-image.settings.image_height.options__0.label",
          "value": "adapt"
        },
        {
          "label": "t:sections.text-with-image.settings.image_height.options__1.label",
          "value": "lg"
        },
        {
          "label": "t:sections.text-with-image.settings.image_height.options__2.label",
          "value": "sm"
        }
      ],
      "type": "select"
    },
    {
      "info": "",
      "label": "t:sections.text-with-image.settings.group_header__2.label",
      "type": "group_header"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.text-with-image.settings.padding_top.label",
      "default": 60
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.text-with-image.settings.padding_bottom.label",
      "default": 60
    }
  ],
  "presets": [
    {
      "category_index": 1,
      "category": "t:sections.text-with-image.presets.presets__0.category",
      "name": "t:sections.text-with-image.presets.presets__0.name",
      "settings": {
        "title": "Image with text",
        "title_font_size": "title3",
        "content": "Pair text with an image to focus on your chosen product, collection, or blog post. Add details on availability, style, or even provide a review.",
        "button_text": "Optional button",
        "show_image_line": true,
        "image_height": "lg",
        "layout": "title_right",
        "padding_top": 60,
        "padding_bottom": 60
      }
    }
  ]
}
{{/schema}}