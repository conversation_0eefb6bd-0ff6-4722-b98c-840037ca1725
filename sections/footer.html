{{snippet "stylesheet" href=(asset_url "component-dropdown-menu.css")}}
{{snippet "stylesheet" href=(asset_url "section-footer.css")}}
{{snippet "stylesheet" href=(asset_url "snippet-tips-card.css")}}
<script src="{{asset_url 'component-localization-form.js'}}" defer></script>
<script src="{{asset_url 'component-dropdown-menu.js'}}" defer></script>
<script src="{{asset_url 'section-footer.js'}}" defer></script>

{{assign "theme_settings" settings}}
{{assign "settings" section.settings}}
{{assign "show_block_len" (length section.blocks)}}

{{#if request.design_mode}}
  <script src="{{asset_url 'theme-editor.js'}}" defer="defer"></script>
{{/if}}

{{#style}}
  .section-{{ section.id }}-padding {
    margin-top: var(--section-vertical-gap);
    padding-top: {{ settings.padding_top }}px;
    padding-bottom: {{ settings.padding_bottom }}px;
  }
{{/style}}

<footer class="footer section-{{section.id}}-padding color-scheme-{{settings.color_scheme}}">
  <div class="footer__content-top page-width">
    {{assign "footer_grid_class" (append "grid-cols-" show_block_len)}}
    {{#if show_block_len > 4}}
      {{assign "footer_grid_class" "grid-cols-4"}}
    {{/if}}
    <div class="footer__blocks-wrapper grid {{footer_grid_class}} grid-cols-1-tablet">
      {{#for section.blocks as |block|}}
        {{#if block.type == "menu"}}
          <footer-menu
            class="footer-block grid__item footer-block--list"
            {{{block.shopline_attributes}}}
            {{#if block.settings.default_fold}}open{{/if}}
            style="width:{{block.settings.span}}%"
          >
            {{#if block.settings.title}}
              <div class="footer-block__heading body3">
                {{block.settings.title}}
                {{snippet "icon-arrow"}}
                {{snippet "icon-minus"}}
              </div>
            {{/if}}
            {{assign "link" block.settings.menu}}
            <ul class="footer-block__details-content list-unstyled">
              {{#for link.links as |link|}}
                <li class="body5">
                  <a
                    href="{{default link.url 'javascript:;'}}"
                    class="link link--text list-menu__item list-menu__item--link
                      {{#if link.active}}list-menu__item--active{{/if}}"
                  >
                    {{link.title}}
                  </a>
                </li>
              {{/for}}
            </ul>
          </footer-menu>
        {{/if}}
        {{!-- 邮件左边的空盒子 --}}
        {{#if block.type == "custom"}}
          <div class="footer-block grid__item" {{{block.shopline_attributes}}} style="width:{{block.settings.span}}%">
            {{#if block.settings.title}}
              <div class="footer-block__heading body3">{{block.settings.title}}</div>
            {{/if}}
            {{#if block.settings.content}}
              <div class="footer-block__details-content rte body5">
                {{{block.settings.content}}}
              </div>
            {{/if}}
          </div>
        {{/if}}
        {{!-- 邮件右边的空盒子 --}}
        {{#if block.type == "image"}}
          <div class="footer-block grid__item {{#if block.settings.image}}{{else}}download_box{{/if}}" {{{block.shopline_attributes}}} style="width:{{block.settings.span}}%">
            {{#capture "img_style"}}width:{{block.settings.image_width}}px;height:{{block.settings.image_width}}px;{{/capture}}
            {{#if block.settings.image}}
            <div class="footer-block__details-content footer-block-image">
            {{snippet "image" data=block.settings.image style=img_style}}
            </div>
            {{else}}
            {{!-- 下载图标 --}}
            <div class="download_main_box">
                <h1 style="font-size:19px;margin-left:10px;margin-bottom:15px;">おしゃれな家具を買うならCAGUUU</h1>
                {{!-- 下载二维码 --}}
                <div class="download_code_box">
                <div class="download_code_title">CAGUUU公式アプリ今すぐダウンロード！</div>
                <div class="download_code_img_box"><img src="{{asset_url 'caguuu.png'}}" decoding="async" alt="下载图标" class="download_code_img"/></div>
                </div>
                <div class="download_buttons_container">
                <img src="https://img.myshopline.com/image/store/1726464192427/230x0w.webp?w=230&h=230" 
                decoding="async" alt="下载图标" loading="lazy" class="download_img">
                <div class="store-buttons">
                <a href="https://onelink.onecommerce.io/9LwlnydE" class="store-button">
                <img src="https://img.myshopline.com/image/store/1726464192427/icon-app-store.png?w=264&h=78" alt="App Store" class="store-button-img">
                </a>
                <a href="https://onelink.onecommerce.io/9LwlnydE" class="store-button">
                <img src="https://img.myshopline.com/image/store/1726464192427/icon-google-play.png?w=265&h=79" alt="Google Play" class="store-button-img">
                </a>
                </div>
                </div>
            </div>
            {{/if}}
          </div>
        {{/if}}
        {{!-- 邮件表单 --}}
        {{#if block.type == "newsletter"}}
          <div class="footer-block grid__item" {{{block.shopline_attributes}}} style="width:{{block.settings.span}}%">
            {{#if block.settings.title}}
              <div class="footer-block__heading body3 rte fw-bold">{{{block.settings.title}}}</div>
            {{/if}}

            {{#if block.settings.desc}}
              <div class="footer-block__desc body4 rte">{{{block.settings.desc}}}</div>
            {{/if}}

            {{#form "customer"}}
              <div class="field footer-block__details-newsletter-form">
                <div class="field__container">
                  <input
                    class="field__input"
                    type="email"
                    id="NewsletterForm"
                    required
                    name="contact[email]"
                    placeholder="{{block.settings.subscribe_letter_placeholder}}"
                  />
                  <label for="NewsletterForm" class="field__label body3">
                    {{block.settings.subscribe_letter_placeholder}}
                  </label>
                </div>
                <div class="field__suffix">
                  <button type="submit" class="button button--link newsletter-form__button">
                    {{snippet "icon-mail"}}
                  </button>
                </div>
              </div>
              {{#if form.posted_successfully}}
                {{snippet "tips-card" type="success" text=(t "general.footer.subscribe_success")}}
              {{/if}}
              {{#if form.errors.messages}}
                {{snippet "tips-card" type="error" text=form.errors.messages}}
              {{/if}}
            {{/form}}
          </div>
        {{/if}}
        {{!-- 跳转媒体 --}}
        {{#if block.type == "social_media"}}
          <div class="footer-block grid__item" {{{block.shopline_attributes}}} style="width:16%">
            <div class="body3 fw-bold">
              {{t "general.contact_us.contact_us"}}
            </div>
            {{#with theme_settings}}
              <ul class="footer-block__social_media-container">
                {{#if social_facebook_link}}
                  <li>
                    <a href="{{social_facebook_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-fb"}}
                      {{else}}
                        {{snippet "icon-social-fb"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
                {{#if social_twitter_link}}
                  <li>
                    <a href="{{social_twitter_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-twitter"}}
                      {{else}}
                        {{snippet "icon-social-twitter"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
                {{#if social_pinterest_link}}
                  <li>
                    <a href="{{social_pinterest_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-pin"}}
                      {{else}}
                        {{snippet "icon-social-pin"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
                {{#if social_instagram_link}}
                  <li>
                    <a href="{{social_instagram_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-ig" id="page-footer"}}
                      {{else}}
                        {{snippet "icon-social-ig"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
                {{#if social_snapchat_link}}
                  <li>
                    <a href="{{social_snapchat_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-snapchat"}}
                      {{else}}
                        {{snippet "icon-social-snapchat"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
                {{#if social_tiktok_link}}
                  <li>
                    <a href="{{social_tiktok_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-tiktok"}}
                      {{else}}
                        {{snippet "icon-social-tiktok"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
                {{#if social_youtube_link}}
                  <li>
                    <a href="{{social_youtube_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-youtube"}}
                      {{else}}
                        {{snippet "icon-social-youtube"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
                {{#if social_vimeo_link}}
                  <li>
                    <a href="{{social_vimeo_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-vimeo"}}
                      {{else}}
                        {{snippet "icon-social-vimeo"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
                {{#if social_tumblr_link}}
                  <li>
                    <a href="{{social_tumblr_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-tumblr"}}
                      {{else}}
                        {{snippet "icon-social-tumblr"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
                {{#if social_linkedin_link}}
                  <li>
                    <a href="{{social_linkedin_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-linkedin"}}
                      {{else}}
                        {{snippet "icon-social-linkedin"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
                {{#if social_whatsapp_link}}
                  <li>
                    <a href="{{social_whatsapp_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-whatsapp"}}
                      {{else}}
                        {{snippet "icon-social-whatsapp"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
                {{#if social_line_link}}
                  <li>
                    <a href="{{social_line_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-line"}}
                      {{else}}
                        {{snippet "icon-social-line"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
                {{#if social_kakao_link}}
                  <li>
                    <a href="{{social_kakao_link}}" target="_blank">
                      {{#if show_official_icon}}
                        {{snippet "icon-official-kakao"}}
                      {{else}}
                        {{snippet "icon-social-kakao"}}
                      {{/if}}
                    </a>
                  </li>
                {{/if}}
              </ul>
            {{/with}}

          </div>
        {{/if}}
      {{/for}}
    </div>
  </div>
  <div class="footer__content-bottom">
    <div class="footer__content-bottom-wrapper page-width">
      <div class="footer__column footer__localization">
        <localization-form>
          {{#form "localization" id="localization-form" enctype="multipart/form-data" accept-charset="UTF-8"}}
            {{#if settings.show_locale_selector and (gt (length localization.available_languages) 1)}}
              <dropdown-menu>
                <div class="dropdown-menu">
                  <input type="hidden" name="locale_code" value="{{localization.language.iso_code}}" />
                  <button class="button button--secondary dropdown-menu__button" type="button">
                    <span data-label>{{localization.language.endonym_name}}</span>
                    {{snippet "icon-arrow"}}
                  </button>
                  <div class="dropdown-menu__list-wrapper top global-modal-border-shadow body4" hidden>
                    <ul class="dropdown-menu__list list-unstyled">
                      {{#for localization.available_languages as |language|}}
                        <li>
                          <a href="javascript:;" data-value="{{language.iso_code}}">{{language.endonym_name}}</a>
                        </li>
                      {{/for}}
                    </ul>
                  </div>
                </div>
              </dropdown-menu>
            {{/if}}
            {{#if settings.show_country_selector and (gt (length localization.available_countries) 1)}}
              <dropdown-menu>
                <div class="dropdown-menu">
                  <input type="hidden" name="country_code" value="{{localization.country.iso_code}}" />
                  <button class="button button--secondary dropdown-menu__button" type="button">
                    <span data-label>{{localization.country.name}}（{{localization.country.currency.iso_code}}
                      {{localization.country.currency.symbol}}）</span>
                    {{snippet "icon-arrow"}}
                  </button>
                  <div class="dropdown-menu__list-wrapper top global-modal-border-shadow body4" hidden>
                    <ul class="dropdown-menu__list list-unstyled">
                      {{#for localization.available_countries as |country|}}
                        <li>
                          <a href="javascript:;" data-value="{{country.iso_code}}">
                            {{country.name}}（{{country.currency.iso_code}}
                            {{country.currency.symbol}}）
                          </a>
                        </li>
                      {{/for}}
                    </ul>
                  </div>
                </div>
              </dropdown-menu>
            {{/if}}
          {{/form}}
        </localization-form>
      </div>
      <div class="footer__column footer__column--info">
        {{#if settings.show_pay_channel.show}}
          <div class="footer__payment">
            {{#for settings.show_pay_channel.pay_channel_list as |pay_channel|}}
              {{payment_type_svg_tag pay_channel.type}}
            {{/for}}
          </div>
        {{/if}}
      </div>
    </div>
  </div>

  <div class="footer__copyright body6">
    {{#capture "storeName"}}
      <a href="{{routes.root_url}}">{{shop.name}}</a>
    {{/capture}}
    <small class="copyright__content">{{{t
        "general.footer.copyright"
        year=(date "now" "%Y")
        storeName=storeName
      }}}</small>
  </div>
</footer>

<style>
/* 底部邮件去除边框 */
.button.button--link:not(.display-none){
box-shadow: 0px 0px 0px 0 !important;
}
.download_box{
width:30%;
/* border:1px solid #fff; */
position:relative;
padding:0;
}
@media screen and (max-width: 959px) {
    .download_box{
        width:100%;
    }
}
.download_main_box{
width:100%;
position:relative;
/* display:flex;
flex-wrap: nowrap;
justify-content:space-between; */
}
.download_buttons_container {
display: flex;
align-items: center;
gap: 15px;
}
.store-buttons {
display: flex;
flex-direction: row;
gap: 5px;
margin-top: 7px;
}
.store-button {
display: block;
transition: transform 0.2s ease;
}
.store-button:hover {
transform: scale(1.05);
}
.store-button-img {
width: 120px;
height: auto;
}
.download_img{
width:50px;
height:50px;
border-radius:10px;
margin-left: 10px;
cursor: pointer;
}
.download_img:hover + .download_code_box {
display: flex; /* 移动到 .download_img 时显示 */
}
.download_code_box{
width:140px;
height:180px;
border-radius:5px;
background-color:rgb(229,240,232);
display: flex;
justify-content: center;
align-content: center;
gap: 10px;
flex-wrap: wrap;
color:#000;
position: absolute;
bottom: 0;
opacity: 0;
left:-150px;
transition: opacity 0.5s ease;
}
.download_code_img_box{
padding: 5px 5px 0 5px;
background-color: #fff;
box-shadow: 1px 1px 5px #0000007d;
border-radius: 10px;
}
.download_code_img{
width:100px;
height:100px;
}
.download_code_title {
padding: 0 10px;
font-size: 12px;
}
</style>
<script>
    const downloadImg = document.querySelector('.download_img');
    const downloadCodeBox = document.querySelector('.download_code_box');
    const otherPlatformsUl = document.querySelector('.other-platforms-ul');

    downloadImg.addEventListener('mouseenter', () => {
    downloadCodeBox.style.opacity = '1'; // 鼠标进入时透明度为 1
    downloadCodeBox.style.pointerEvents = 'auto'; // 允许鼠标事件
    otherPlatformsUl.style.zIndex = '0'; // 设置 z-index 为 0
});

    downloadImg.addEventListener('mouseleave', () => {
    downloadCodeBox.style.opacity = '0'; // 鼠标离开时透明度为 0
    downloadCodeBox.style.pointerEvents = 'none'; // 防止鼠标事件
    otherPlatformsUl.style.zIndex = '10'; // 恢复 z-index 为 10
});
</script>
{{#schema}}
{
  "name": "t:sections.footer.name",
  "max_blocks": 16,
  "settings": [
    {
      "type": "select",
      "id": "color_scheme",
      "default": "1",
      "label": "t:sections.footer.settings.color_scheme.label",
      "options": [
        {
          "value": "none",
          "label": "t:sections.footer.settings.color_scheme.options__0.label"
        },
        {
          "value": "1",
          "label": "t:sections.footer.settings.color_scheme.options__1.label"
        },
        {
          "value": "2",
          "label": "t:sections.footer.settings.color_scheme.options__2.label"
        },
        {
          "value": "3",
          "label": "t:sections.footer.settings.color_scheme.options__3.label"
        }
      ]
    },
    {
      "type": "choose_payment_icons",
      "id": "show_pay_channel",
      "label": "t:sections.footer.settings.show_pay_channel.label",
      "default": false
    },
    {
      "type": "switch",
      "id": "show_country_selector",
      "label": "t:sections.footer.settings.show_country_selector.label",
      "info": "t:sections.footer.settings.show_country_selector.info",
      "default": false
    },
    {
      "type": "switch",
      "id": "show_locale_selector",
      "label": "t:sections.footer.settings.show_locale_selector.label",
      "info": "t:sections.footer.settings.show_locale_selector.info",
      "default": false
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.footer.settings.padding_top.label",
      "default": 40
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 2,
      "unit": "px",
      "label": "t:sections.footer.settings.padding_bottom.label",
      "default": 40
    }
  ],
  "blocks": [
    {
      "type": "menu",
      "icon": "normal",
      "name": "t:sections.footer.blocks.menu.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "t:sections.footer.blocks.menu.settings.title.label"
        },
        {
          "type": "menu_picker",
          "id": "menu",
          "label": "t:sections.footer.blocks.menu.settings.menu.label",
          "default": "footer"
        },
        {
          "type": "range",
          "id": "span",
          "label": "t:sections.footer.blocks.menu.settings.span.label",
          "default": 20,
          "min": 5,
          "max": 95,
          "step": 1,
          "unit": "%"
        },
        {
          "type": "switch",
          "id": "default_fold",
          "label": "t:sections.footer.blocks.menu.settings.default_fold.label",
          "default": false
        }
      ]
    },
    {
      "type": "custom",
      "icon": "paragraph",
      "name": "t:sections.footer.blocks.custom.name",
      "settings": [
        {
          "type": "text",
          "id": "title",
          "label": "t:sections.footer.blocks.custom.settings.title.label",
          "default": "Custom text"
        },
        {
          "type": "richtext",
          "id": "content",
          "label": "t:sections.footer.blocks.custom.settings.content.label",
          "default": "Add your own custom text here."
        },
        {
          "type": "range",
          "id": "span",
          "label": "t:sections.footer.blocks.custom.settings.span.label",
          "default": 20,
          "min": 5,
          "max": 95,
          "step": 1,
          "unit": "%"
        }
      ]
    },
    {
      "type": "image",
      "icon": "image",
      "name": "t:sections.footer.blocks.image.name",
      "settings": [
        {
          "type": "image_picker",
          "id": "image",
          "label": "t:sections.footer.blocks.image.settings.image.label"
        },
        {
          "id": "image_width",
          "type": "range",
          "label": "t:sections.footer.blocks.image.settings.image_width.label",
          "min": 50,
          "max": 200,
          "step": 10,
          "unit": "px",
          "default": 100
        },
        {
          "type": "range",
          "id": "span",
          "label": "t:sections.footer.blocks.image.settings.span.label",
          "default": 20,
          "min": 5,
          "max": 95,
          "step": 1,
          "unit": "%"
        }
      ]
    },
    {
      "type": "newsletter",
      "icon": "email",
      "name": "t:sections.footer.blocks.newsletter.name",
      "settings": [
        {
          "type": "richtext",
          "id": "title",
          "label": "t:sections.footer.blocks.newsletter.settings.title.label",
          "default": "Subscribe"
        },
        {
          "type": "richtext",
          "id": "desc",
          "label": "t:sections.footer.blocks.newsletter.settings.desc.label",
          "default": "Subscribe today and get 10% off your first purchase"
        },
        {
          "type": "text",
          "id": "subscribe_letter_placeholder",
          "label": "t:sections.footer.blocks.newsletter.settings.subscribe_letter_placeholder.label",
          "default": "Enter your email"
        },
        {
          "type": "range",
          "id": "span",
          "label": "t:sections.footer.blocks.newsletter.settings.span.label",
          "default": 20,
          "min": 5,
          "max": 95,
          "step": 1,
          "unit": "%"
        }
      ]
    },
    {
      "type": "social_media",
      "icon": "button",
      "name": "t:sections.footer.blocks.social_media.name",
      "settings": [
        {
          "type": "range",
          "id": "span",
          "label": "t:sections.footer.blocks.social_media.settings.span.label",
          "default": 20,
          "min": 5,
          "max": 95,
          "step": 1,
          "unit": "%"
        }
      ]
    }
  ],
  "default": {
    "blocks": [
      {
        "type": "menu",
        "settings": {
          "menu": "footer",
          "default_fold": false,
          "title": "Menu title"
        }
      },
      {
        "type": "custom",
        "settings": {
          "title": "Custom text",
          "content": "Add your own custom text here."
        }
      },
      {
        "type": "image",
        "settings": {
          "image": {}
        }
      }
    ],
    "settings": {
      "show_locale_selector": false,
      "show_currency_selector": false,
      "show_pay_channel": {
        "show": false,
        "pay_channel_list": [
          {
            "type": "visa",
            "show": true
          },
          {
            "type": "master-card",
            "show": true
          },
          {
            "type": "master-card2",
            "show": true
          },
          {
            "type": "visa-electron",
            "show": true
          },
          {
            "type": "jcb",
            "show": true
          },
          {
            "type": "american-express",
            "show": true
          },
          {
            "type": "diners-club",
            "show": true
          },
          {
            "type": "discover",
            "show": true
          },
          {
            "type": "paypal",
            "show": true
          },
          {
            "type": "union-pay",
            "show": true
          }
        ]
      },
      "color_scheme": "1",
      "padding_top": 40,
      "padding_bottom": 40
    }
  }
}
{{/schema}}