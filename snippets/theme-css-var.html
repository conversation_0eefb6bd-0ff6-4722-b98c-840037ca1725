<style>
{{ font_face (font_modify settings.title_font_family 'weight' '400') }}
{{ font_face (font_modify settings.title_font_family 'weight' '500') }}
{{ font_face (font_modify settings.title_font_family 'weight' '600') }}
{{ font_face (font_modify settings.title_font_family 'weight' '700') }}
{{ font_face settings.title_font_family }}
{{ font_face (font_modify settings.body_font_family 'weight' '400') }}
{{ font_face (font_modify settings.body_font_family 'weight' '500') }}
{{ font_face (font_modify settings.body_font_family 'weight' '600') }}
{{ font_face (font_modify settings.body_font_family 'weight' '700') }}
{{ font_face settings.body_font_family }}

:root {
    --title-font: "{{ settings.title_font_family.family }}", "{{ settings.title_font_family.fallback_families }}";
    --title-font-weight: {{ settings.title_font_family.weight }};
    --title-font-style: {{ settings.title_font_family.style }};
    --title-letter-spacing: {{#if settings.title_letter_spacing == 0}}0.00000001em{{else}}{{ divide settings.title_letter_spacing 1000 }}em{{/if}};
    --title-font-size: {{ settings.title_font_size }}px;
    --title-line-height: {{ settings.title_line_height }};
    --title-text-transform: {{#if settings.title_uppercase}}uppercase{{else}}unset{{/if}};
    --body-font: "{{ settings.body_font_family.family }}", "{{ settings.body_font_family.fallback_families }}";
    {{assign 'bodyFontWeight' settings.body_font_family.weight}}
    --body-font-weight: {{ bodyFontWeight }};
    {{assign 'bodyBoldFontWeight' (add bodyFontWeight 200)}}
    --body-bold-font-weight: {{#gte bodyBoldFontWeight 1000}}1000{{else}}{{ bodyBoldFontWeight }}{{/gte}};
    --body-font-style: {{ settings.body_font_family.style }};
    --body-letter-spacing: {{#if settings.body_letter_spacing == 0}}0.00000001em{{else}}{{ divide settings.body_letter_spacing 1000 }}em{{/if}};
    --body-font-size: {{ settings.body_font_size }}px;
    --body-line-height: {{ settings.body_line_height }};

    --color-page-background: {{ settings.color_page_background.red }}, {{ settings.color_page_background.green }}, {{settings.color_page_background.blue }};
    --color-text: {{ settings.color_text.red }}, {{ settings.color_text.green }}, {{settings.color_text.blue }};
    --color-light-text: {{ settings.color_light_text.red }}, {{ settings.color_light_text.green }}, {{settings.color_light_text.blue }};
    --color-sale: {{ settings.color_sale.red }}, {{ settings.color_sale.green }}, {{settings.color_sale.blue }};
    --color-discount: {{ settings.color_discount.red }}, {{ settings.color_discount.green }}, {{settings.color_discount.blue }};
    --color-entry-line: {{ settings.color_entry_line.red }}, {{ settings.color_entry_line.green }}, {{settings.color_entry_line.blue }};
    --color-button-background: {{ settings.color_button_background.red }}, {{ settings.color_button_background.green }}, {{settings.color_button_background.blue }};
    --color-button-text: {{ settings.color_button_text.red }}, {{ settings.color_button_text.green }}, {{settings.color_button_text.blue }};
    --color-button-secondary-background: {{ settings.color_button_secondary_background.red }}, {{ settings.color_button_secondary_background.green }}, {{settings.color_button_secondary_background.blue }};
    --color-button-secondary-text: {{ settings.color_button_secondary_text.red }}, {{ settings.color_button_secondary_text.green }}, {{settings.color_button_secondary_text.blue }};
    --color-button-secondary-border: {{ settings.color_button_secondary_border.red }}, {{ settings.color_button_secondary_border.green }}, {{settings.color_button_secondary_border.blue }};
    --color-discount-tag-background: {{ settings.color_discount_tag_background.red }}, {{ settings.color_discount_tag_background.green }}, {{settings.color_discount_tag_background.blue }};
    --color-discount-tag-text: {{ settings.color_discount_tag_text.red }}, {{ settings.color_discount_tag_text.green }}, {{settings.color_discount_tag_text.blue }};
    --color-cart-dot: {{ settings.color_cart_dot.red }}, {{ settings.color_cart_dot.green }}, {{settings.color_cart_dot.blue }};
    --color-cart-dot-text: {{ settings.color_cart_dot_text.red }}, {{ settings.color_cart_dot_text.green }}, {{settings.color_cart_dot_text.blue }};
    --color-image-background: {{ settings.color_image_background.red }}, {{ settings.color_image_background.green }}, {{settings.color_image_background.blue }};
    --color-image-loading-background: {{ settings.color_image_loading_background.red }}, {{ settings.color_image_loading_background.green }}, {{settings.color_image_loading_background.blue }};
    --color-mask: {{ settings.color_mask.red }}, {{ settings.color_mask.green }}, {{settings.color_mask.blue }};
    --color-shadow: {{#if settings.color_shadow}}{{ settings.color_shadow.red }}, {{ settings.color_shadow.green }}, {{settings.color_shadow.blue }}{{else}}var(--color-text){{/if}};
    --color-scheme-1-bg: {{ settings.color_scheme_1_bg.red }}, {{ settings.color_scheme_1_bg.green }}, {{settings.color_scheme_1_bg.blue }};
    --color-scheme-1-text: {{ settings.color_scheme_1_text.red }}, {{ settings.color_scheme_1_text.green }}, {{settings.color_scheme_1_text.blue }};
    --color-scheme-2-bg: {{ settings.color_scheme_2_bg.red }}, {{ settings.color_scheme_2_bg.green }}, {{settings.color_scheme_2_bg.blue }};
    --color-scheme-2-text: {{ settings.color_scheme_2_text.red }}, {{ settings.color_scheme_2_text.green }}, {{settings.color_scheme_2_text.blue }};
    --color-scheme-3-bg: {{ settings.color_scheme_3_bg.red }}, {{ settings.color_scheme_3_bg.green }}, {{settings.color_scheme_3_bg.blue }};
    --color-scheme-3-text: {{ settings.color_scheme_3_text.red }}, {{ settings.color_scheme_3_text.green }}, {{settings.color_scheme_3_text.blue }};

    --page-width: {{ settings.page_width }}px;
    --section-vertical-gap: {{ settings.section_vertical_gap }}px;
    --grid-horizontal-space: {{ settings.grid_horizontal_space }}px;
    --grid-vertical-space: {{ settings.grid_vertical_space }}px;
    --grid-mobile-horizontal-space: {{ divide settings.grid_horizontal_space 2 }}px;
    --grid-mobile-vertical-space: {{ divide settings.grid_vertical_space 2 }}px;

    --btn-border-thickness: {{ settings.btn_border_thickness }}px;
    --btn-border-opacity: {{ settings.btn_border_opacity }}%;
    --btn-border-radius: {{ settings.btn_border_radius }}px;
    --btn-border-radius-outset: {{ ternary (lte settings.btn_border_radius 0) 0 (add settings.btn_border_radius settings.btn_border_thickness) }}px;
    --btn-shadow-opacity: {{ settings.btn_shadow_opacity }}%;
    --btn-shadow-offset-x: {{ settings.btn_shadow_offset_x }}px;
    --btn-shadow-offset-y: {{ settings.btn_shadow_offset_y }}px;
    --btn-shadow-blur: {{ settings.btn_shadow_blur }}px;

    --sku-selector-border-thickness: {{ settings.sku_selector_border_thickness }}px;
    --sku-selector-border-opacity: {{ settings.sku_selector_border_opacity }}%;
    --sku-selector-border-radius: {{ settings.sku_selector_border_radius }}px;
    --sku-selector-border-radius-outset: {{ ternary (lte settings.sku_selector_border_radius 0) 0 (add settings.sku_selector_border_radius settings.sku_selector_border_thickness) }}px;

    --input-border-thickness: {{ settings.input_border_thickness }}px;
    --input-border-opacity: {{ settings.input_border_opacity }}%;
    --input-border-radius: {{ settings.input_border_radius }}px;
    --input-border-radius-outset: {{ ternary (lte settings.input_border_radius 0) 0 (add settings.input_border_radius settings.input_border_thickness) }}px;
    --input-shadow-opacity: {{ settings.input_shadow_opacity }}%;
    --input-shadow-offset-x: {{ settings.input_shadow_offset_x }}px;
    --input-shadow-offset-y: {{ settings.input_shadow_offset_y }}px;
    --input-shadow-blur: {{ settings.input_shadow_blur }}px;

    --card-border-thickness: {{ settings.card_border_thickness }}px;
    --card-border-opacity: {{ settings.card_border_opacity }}%;
    --card-border-radius: {{ settings.card_border_radius }}px;
    --card-border-radius-outset: {{ ternary (lte settings.card_border_radius 0) 0 (add settings.card_border_radius settings.card_border_thickness) }}px;
    --card-shadow-opacity: {{ settings.card_shadow_opacity }}%;
    --card-shadow-offset-x: {{ settings.card_shadow_offset_x }}px;
    --card-shadow-offset-y: {{ settings.card_shadow_offset_y }}px;
    --card-shadow-blur: {{ settings.card_shadow_blur }}px;

    --product-card-border-thickness: {{ settings.product_card_border_thickness }}px;
    --product-card-border-opacity: {{ settings.product_card_border_opacity }}%;
    --product-card-border-radius: {{ settings.product_card_border_radius }}px;
    --product-card-border-radius-outset: {{ ternary (lte settings.product_card_border_radius 0) 0 (add settings.product_card_border_radius settings.product_card_border_thickness) }}px;
    --product-card-shadow-opacity: {{ settings.product_card_shadow_opacity }}%;
    --product-card-shadow-offset-x: {{ settings.product_card_shadow_offset_x }}px;
    --product-card-shadow-offset-y: {{ settings.product_card_shadow_offset_y }}px;
    --product-card-shadow-blur: {{ settings.product_card_shadow_blur }}px;

    --collection-card-border-thickness: {{ settings.collection_card_border_thickness }}px;
    --collection-card-border-opacity: {{ settings.collection_card_border_opacity }}%;
    --collection-card-border-radius: {{ settings.collection_card_border_radius }}px;
    --collection-card-border-radius-outset: {{ ternary (lte settings.collection_card_border_radius 0) 0 (add settings.collection_card_border_radius settings.collection_card_border_thickness) }}px;
    --collection-card-shadow-opacity: {{ settings.collection_card_shadow_opacity }}%;
    --collection-card-shadow-offset-x: {{ settings.collection_card_shadow_offset_x }}px;
    --collection-card-shadow-offset-y: {{ settings.collection_card_shadow_offset_y }}px;
    --collection-card-shadow-blur: {{ settings.collection_card_shadow_blur }}px;

    --blog-card-border-thickness: {{ settings.blog_card_border_thickness }}px;
    --blog-card-border-opacity: {{ settings.blog_card_border_opacity }}%;
    --blog-card-border-radius: {{ settings.blog_card_border_radius }}px;
    --blog-card-border-radius-outset: {{ ternary (lte settings.blog_card_border_radius 0) 0 (add settings.blog_card_border_radius settings.blog_card_border_thickness) }}px;
    --blog-card-shadow-opacity: {{ settings.blog_card_shadow_opacity }}%;
    --blog-card-shadow-offset-x: {{ settings.blog_card_shadow_offset_x }}px;
    --blog-card-shadow-offset-y: {{ settings.blog_card_shadow_offset_y }}px;
    --blog-card-shadow-blur: {{ settings.blog_card_shadow_blur }}px;

    --content-border-thickness: {{ settings.content_border_thickness }}px;
    --content-border-opacity: {{ settings.content_border_opacity }}%;
    --content-border-radius: {{ settings.content_border_radius }}px;
    --content-border-radius-outset: {{ ternary (lte settings.content_border_radius 0) 0 (add settings.content_border_radius settings.content_border_thickness) }}px;
    --content-shadow-opacity: {{ settings.content_shadow_opacity }}%;
    --content-shadow-offset-x: {{ settings.content_shadow_offset_x }}px;
    --content-shadow-offset-y: {{ settings.content_shadow_offset_y }}px;
    --content-shadow-blur: {{ settings.content_shadow_blur }}px;

    --media-border-thickness: {{ settings.media_border_thickness }}px;
    --media-border-opacity: {{ settings.media_border_opacity }}%;
    --media-border-radius: {{ settings.media_border_radius }}px;
    --media-border-radius-outset: {{ ternary (lte settings.media_border_radius 0) 0 (add settings.media_border_radius settings.media_border_thickness) }}px;
    --media-shadow-opacity: {{ settings.media_shadow_opacity }}%;
    --media-shadow-offset-x: {{ settings.media_shadow_offset_x }}px;
    --media-shadow-offset-left: {{ ternary (if settings.media_shadow_offset_x < 0) (add (minus 0 settings.media_shadow_offset_x) 4) 0 }}px;
    --media-shadow-offset-right: {{ ternary (if settings.media_shadow_offset_x > 0) (add settings.media_shadow_offset_x 4) 0 }}px;
    --media-shadow-offset-y: {{ settings.media_shadow_offset_y }}px;
    --media-shadow-offset-top: {{ ternary (if settings.media_shadow_offset_y < 0) (add (minus 0 settings.media_shadow_offset_y) 4) 0 }}px;
    --media-shadow-offset-bottom: {{ ternary (if settings.media_shadow_offset_y > 0) (add settings.media_shadow_offset_y 4) 0 }}px;
    --media-shadow-blur: {{ settings.media_shadow_blur }}px;

    --menu-modal-border-thickness: {{ settings.menu_modal_border_thickness }}px;
    --menu-modal-border-opacity: {{ settings.menu_modal_border_opacity }}%;
    --menu-modal-border-radius: {{ settings.menu_modal_border_radius }}px;
    --menu-modal-border-radius-outset: {{ ternary (lte settings.menu_modal_border_radius 0) 0 (add settings.menu_modal_border_radius settings.menu_modal_border_thickness) }}px;
    --menu-modal-shadow-opacity: {{ settings.menu_modal_shadow_opacity }}%;
    --menu-modal-shadow-offset-x: {{ settings.menu_modal_shadow_offset_x }}px;
    --menu-modal-shadow-offset-y: {{ settings.menu_modal_shadow_offset_y }}px;
    --menu-modal-shadow-blur: {{ settings.menu_modal_shadow_blur }}px;

    --drawer-border-thickness: {{ settings.drawer_border_thickness }}px;
    --drawer-border-opacity: {{ settings.drawer_border_opacity }}%;
    --drawer-shadow-opacity: {{ settings.drawer_shadow_opacity }}%;
    --drawer-shadow-offset-x: {{ settings.drawer_shadow_offset_x }}px;
    --drawer-shadow-offset-y: {{ settings.drawer_shadow_offset_y }}px;
    --drawer-shadow-blur: {{ settings.drawer_shadow_blur }}px;

    --product-discount-radius: {{ settings.product_discount_radius }}px;

    --swatch-background-default-image: url({{ asset_url 'color-swatch-default.png'}})
}
</style>