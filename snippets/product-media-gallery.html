{{#comment}}
  Renders a product media gallery. Should be used with 'media-gallery/index.js'
  Also see 'product-media-modal'

  Accepts:
  - product: {Object} Product Handlebars object
  - variant_images: {Array} Product images associated with a variant
  - is_duplicate: {<PERSON><PERSON><PERSON>} Prevents rendering uneeded elements and duplicate ids for subsequent instances
  - pc_magnifier_type: {click|hover} Commodity amplifier trigger method
  - image_quality: {Number} Image compression ratio (optional) default 80.
  - magnifier_interactive_type: {mode_1|mode_2 } Default: mode_1
  - youtube_control: {Number} Enable YouTube controls (optional) 
  - youtube_show_playlist: {Number} Enable YouTube playlist (optional)

  Usage:
  {{snippet 'product-media-gallery'}}
{{/comment}}



{{assign "hide_variants" (default section.settings.hide_variants false)}}
{{assign "default_selected_variant" (default section.settings.default_selected_variant true)}}
{{assign "is_selected_variant" product.selected_variant}}

{{assign "media_count" product.media.length}}

{{assign "featured_variant" ""}}
{{assign "featured_media" product.featured_media}}
{{#if default_selected_variant or is_selected_variant}}
  {{assign "featured_variant" product.selected_or_first_available_variant}}
{{/if}}

{{#if hide_variants and featured_variant and media_count > 1 and variant_images.length > 0}}
  {{#if featured_media}}
    {{assign "media_count" (plus (minus media_count variant_images.length) 1)}}
  {{else}}
    {{#comment}}
      If the first media image is a variant image, it will be displayed on the page.
    {{/comment}}
    {{#if variant_images contains product.featured_media.src}}
      {{assign "media_count" (plus (minus media_count variant_images.length) 1)}}
    {{else}}
      {{assign "media_count" (minus media_count variant_images.length)}}
    {{/if}}
  {{/if}}
{{/if}}

{{assign "media_width" 0.65}}

{{#if section.settings.product_image_size "large"}}
  {{assign "media_width" 0.65}}
{{else if section.settings.product_image_size "medium"}}
  {{assign "media_width" 0.55}}
{{else if section.settings.product_image_size "small"}}
  {{assign "media_width" 0.45}}
{{/if}}

{{assign "id_append" ""}}

{{#if is_duplicate}}
  {{assign "id_append" "-duplicate"}}
{{/if}}

{{assign "video_mute" 0}}
{{#if section.settings.video_autoplay}}
  {{assign "video_mute" 1}}
{{/if}}

{{assign "product_image_pc_thumbnail_postion" (default section.settings.product_image_pc_thumbnail_postion "bottom")}}
{{assign "pc_style" section.settings.product_image_pc_show_style}}
{{!-- {{assign "pc_need_thumbnail" (if pc_style == "thumbnail_flatten" or pc_style == "carousel")}} --}}
{{assign "pc_need_thumbnail" true}}
{{assign "need_move_first_image" (if featured_media and (if (not pc_need_thumbnail) or hide_variants))}}
{{!-- {{assign "mobile_thumbnail_show" (if section.settings.product_mobile_thumbnail_image_hide == "show")}} --}}
{{assign "mobile_thumbnail_show" true}}
{{assign "product_thumbnail_image_size" (default section.settings.product_thumbnail_image_size "medium")}}
{{assign "product_mobile_thumbnail_image_size" (default section.settings.product_mobile_thumbnail_image_size "medium")}}

<media-gallery
  data-section-id="{{section.id}}"
  id="MediaGallery-{{section.id}}{{id_append}}"
  class="{{#if section.settings.product_info_sticky}}product__column-sticky{{/if}}
    {{#if (size product.media) == 0}}product__media--empty{{/if}}
    {{#if (isFalsey featured_variant)}}js-init-not-selected-variant{{/if}}"
  data-desktop-layout="{{section.settings.product_image_pc_show_style}}"
  data-video-autoplay="{{section.settings.video_autoplay}}"
  data-desktop-thumbnail-postion="{{product_image_pc_thumbnail_postion}}"
  data-hide-variants="{{hide_variants}}"
>
  <div id="GalleryStatus-{{section.id}}"></div>
  <div
    class="product__thumbnail-slider-group
      product__thumbnail--beside
      product__thumbnail--{{product_thumbnail_image_size}}
      product__mobile-thumbnail--{{product_mobile_thumbnail_image_size}}"
  >
    
    <slider-component
      id="GalleryViewer-{{section.id}}{{id_append}}"
      class="thumbnail-preview slider-mobile-gutter {{#unless featured_media}}no-featured-media{{/unless}}  product__media-wrapper-position"
    >
    {{!-- <div class="Thumbs-Animation-Main-Box">
<div class="split-chars">
<span aria-hidden="true" class="split-char" style="--char-index: 0;">•</span>
<span aria-hidden="true" class="split-char" style="--char-index: 1;">P</span>
<span aria-hidden="true" class="split-char" style="--char-index: 2;">I</span>
<span aria-hidden="true" class="split-char" style="--char-index: 3;">C</span>
<span aria-hidden="true" class="split-char" style="--char-index: 4;">K</span>
<span aria-hidden="true" class="split-char" style="--char-index: 5;">•</span>
<span aria-hidden="true" class="split-char" style="--char-index: 6;">U</span>
<span aria-hidden="true" class="split-char" style="--char-index: 7;">P</span>
<span aria-hidden="true" class="split-char" style="--char-index: 8;">•</span>
<span aria-hidden="true" class="split-char" style="--char-index: 9;">I</span>
<span aria-hidden="true" class="split-char" style="--char-index: 10;">T</span>
<span aria-hidden="true" class="split-char" style="--char-index: 11;">E</span>
<span aria-hidden="true" class="split-char" style="--char-index: 12;">M</span>
</div>
<svg class="Thumbs-icons" viewBox="0 0 41 41" stroke="currentColor" fill="none" xmlns="http://www.w3.org/2000/svg">
<path stroke-linecap="round" stroke-linejoin="round" d="M17.486 31.8298C17.486 31.8298 19.6732 33.5701 21.9404 32.5164C23.3536 31.8596 28.2906 29.565 31.8091 27.9298C33.1341 27.3139 33.7966 27.006 34.2191 26.5148C34.5915 26.0818 34.8326 25.5515 34.914 24.9862C35.0063 24.3449 34.8027 23.6431 34.3954 22.2395L32.5204 15.7778C31.9832 13.9266 31.7146 13.0009 31.1459 12.4479C30.6466 11.9626 29.9941 11.6659 29.3002 11.6089C28.5095 11.544 27.6355 11.9502 25.8875 12.7626L24.0932 13.5965C23.3585 13.938 22.9912 14.1087 22.6896 14.0608C22.426 14.0189 22.1901 13.8733 22.0344 13.6566C21.8563 13.4086 21.844 13.0037 21.8196 12.1938L21.7051 8.40173C21.698 8.16642 21.6945 8.04876 21.6844 7.96256C21.5339 6.6814 20.2279 5.87567 19.0154 6.31596C18.9338 6.34559 18.827 6.3952 18.6136 6.49442V6.49442C18.5811 6.50949 18.5649 6.51702 18.5496 6.52482C18.3343 6.63405 18.1658 6.81746 18.0752 7.04121C18.0687 7.05719 18.0626 7.07397 18.0503 7.10755L12.7445 21.6278M12.9518 33.9372L8.21028 23.7352C7.62835 22.4831 8.17162 20.9963 9.42371 20.4144V20.4144C10.6758 19.8325 12.1626 20.3758 12.7445 21.6278L17.486 31.8298C18.0679 33.0819 17.5247 34.5687 16.2726 35.1506V35.1506C15.0205 35.7325 13.5337 35.1893 12.9518 33.9372Z"></path>
</svg>
</div> --}}
      <ul
        id="Slider-Gallery-{{section.id}}{{id_append}}"
        class="product__media-list contains-media grid grid--peek list-unstyled slider slider--mobile"
      >

        {{#if need_move_first_image}}
          <li
            id="Slide-{{section.id}}-{{featured_media.id}}{{id_append}}"
            class="product__media-item grid__item slider__slide is-active
              {{#if hide_variants}}{{#if variant_images contains featured_media.src}} product__media-item--variant{{/if}}{{/if}}"
            data-media-id="{{section.id}}-{{featured_media.id}}"
          >
            {{snippet
              "product-thumbnail"
              media=featured_media
              media_count=media_count
              desktop_layout=section.settings.product_image_pc_show_style
              mobile_layout=section.settings.product_mobile_thumbnail_image_hide
              loop=section.settings.video_loop
              modal_id=section.id
              media_width=media_width
              lazy_load=false
              pc_magnifier_type=pc_magnifier_type
              magnifier_interactive_type=magnifier_interactive_type
              video_mute=video_mute
              image_quality=image_quality
              product_image_ratio=section.settings.product_image_ratio
              product_image_fill_type=section.settings.product_image_fill_type
              product_mobile_image_ratio=section.settings.product_mobile_image_ratio
              product_mobile_image_fill_type=section.settings.product_mobile_image_fill_type
            }}
          </li>
        {{else if (size product.media) == 0}}
          <li class="product__media-item slider__slide is-active">
            <div class="placeholder">
              {{placeholder_svg_tag "image" "product-image-placeholder"}}
            </div>
          </li>
        {{/if}}

        {{#for product.media as |media|}}
          {{#not (if need_move_first_image and (if media.id == featured_media.id))}}
            <li
              id="Slide-{{section.id}}-{{media.id}}{{id_append}}"
              class="product__media-item slider__slide
                {{#if (if media.id == featured_media.id) or (if (not featured_media) and forloop.index0 == 0)}} is-active{{/if}}
                {{#if hide_variants}}
                  {{#if variant_images contains media.src}} product__media-item--variant{{/if}}
                {{/if}}
                "
              data-media-id="{{section.id}}-{{media.id}}"
            >
              {{snippet
                "product-thumbnail"
                media=media
                media_count=media_count
                desktop_layout=section.settings.product_image_pc_show_style
                mobile_layout=section.settings.product_mobile_thumbnail_image_hide
                loop=section.settings.video_loop
                modal_id=section.id
                media_width=media_width
                pc_magnifier_type=pc_magnifier_type
                magnifier_interactive_type=magnifier_interactive_type
                video_mute=video_mute
                image_quality=image_quality
                product_image_ratio=section.settings.product_image_ratio
                product_image_fill_type=section.settings.product_image_fill_type
                product_mobile_image_ratio=section.settings.product_mobile_image_ratio
                product_mobile_image_fill_type=section.settings.product_mobile_image_fill_type
              }}
            </li>
          {{/not}}
        {{/for}}
      </ul>
      {{#unless is_duplicate}}
        {{snippet
          "slider-button"
          media_count=media_count
          variant_images=variant_images
          product_mobile_image_switch_style="number"
          media=product.media
          product_mobile_thumbnail_image_hide=section.settings.product_mobile_thumbnail_image_hide
        }}
      {{/unless}}
       
    </slider-component>

    
    {{#if media_count > 0 and pc_need_thumbnail or mobile_thumbnail_show}}
      <slider-component
        id="GalleryThumbnails-{{section.id}}{{id_append}}"
        {{!-- pc-direction="{{#if product_image_pc_thumbnail_postion == 'beside'}}vertical{{else}}horizontal{{/if}}" --}}
        pc-direction="vertical"
        class="thumbnail-slider slider-mobile-gutter
          {{#unless pc_need_thumbnail}}display-none-desktop{{/unless}}
          {{#unless mobile_thumbnail_show}} display-none-tablet{{/unless}}
          "
      >
        <button
          type="button"
          class="slider-button slider-button--prev display-none-tablet {{#if media_count <= 4}}display-none{{/if}}"
          name="previous"
          data-step="3"
        >
          {{snippet "icon-arrow"}}
        </button>
        <ul
          id="Slider-Thumbnails-{{section.id}}{{id_append}}"
          class="thumbnail-list
            thumbnail-list--{{pc_style}}
            list-unstyled slider slider--mobile
            {{#if pc_need_thumbnail}}thumbnail-list-grid{{/if}}
            "
        >
          {{~#capture "sizes"~}}
            (min-width: {{settings.page_width}}px) calc(({{round (times (minus settings.page_width 100) media_width)}} - 40px) / 4),
            (min-width: 990px) calc(({{times media_width 100}}vw - 40px) / 4),
            (min-width: 750px) calc((100vw - 150px) / 8),
            calc((100vw - 80px) / 3)
          {{~/capture~}}

          {{#if hide_variants and featured_media}}
            <li
              id="Slide-Thumbnails-{{section.id}}-0{{id_append}}"
              class="thumbnail-list__item
                slider__slide{{#if variant_images contains featured_media.src}} thumbnail-list_item--variant{{/if}}"
              data-target="{{section.id}}-{{featured_media.id}}"
            >
              {{~#capture "thumbnail_id"~}}
                Thumbnail-{{section.id}}-0{{id_append}}
              {{~/capture~}}
              {{assign "available_media_image" null}}

              {{#if (hasOwn featured_media.preview_image "src") and (isTruthy featured_media.preview_image.src)}}
                {{assign "available_media_image" featured_media.preview_image}}
              {{else if (hasOwn featured_media "src") and (isTruthy featured_media.src)}}
                {{assign "available_media_image" featured_media}}
              {{/if}}

              <button
                class="thumbnail
                  {{#if (gte (toFloat available_media_image.aspect_ratio) 1)}}thumbnail--wide{{else}}thumbnail--narrow{{/if}}"
                data-current="true"
              >
                {{image_tag
                  (image_url available_media_image width=416 quality=image_quality)
                  loading=lazy
                  sizes=sizes
                  widths="54, 74, 104, 162, 208, 324, 416"
                  id=thumbnail_id
                  alt=featured_media.alt
                }}
              </button>
            </li>
          {{/if}}
          {{#for product.media as |media|}}
            {{#not (if hide_variants and (if media.id == featured_media.id))}}
              {{assign "index" (plus forloop.index0 1)}}
              <li
                id="Slide-Thumbnails-{{section.id}}-{{index}}{{id_append}}"
                class="thumbnail-list__item slider__slide
                  {{#if (if media.id == featured_media.id) or (if (not featured_media) and forloop.index0 == 0)}} is-active{{/if}}
                  {{#if hide_variants}}{{#if variant_images contains media.src}} thumbnail-list_item--variant{{/if}}{{/if}}"
                data-target="{{section.id}}-{{media.id}}"
              >
                {{#if media.media_type == "video" or media.media_type == "external_video"}}
                  <span class="thumbnail__badge">
                    {{snippet "icon-play"}}
                  </span>
                {{/if}}

                {{~#capture "thumbnail_id"~}}
                  Thumbnail-{{section.id}}-{{index}}{{id_append}}
                {{~/capture~}}
                {{assign "available_media_image2" null}}

                {{#if (hasOwn media.preview_image "src") and (isTruthy media.preview_image.src)}}
                  {{assign "available_media_image2" media.preview_image}}
                {{else if (hasOwn media "src") and (isTruthy media.src)}}
                  {{assign "available_media_image2" media}}
                {{/if}}
                <button
                  class="thumbnail
                    {{#if (gte (toFloat available_media_image2.aspect_ratio) 1)}}thumbnail--wide{{else}}thumbnail--narrow{{/if}}"
                  {{#if featured_media}}
                    {{#if media.id == featured_media.id}}
                      data-current="true"
                    {{/if}}
                  {{else}}
                    {{#if forloop.index0 == 0}}data-current="true"{{/if}}
                  {{/if}}
                >
                  {{image_tag
                    (image_url available_media_image2 width=416 quality=image_quality)
                    loading=lazy
                    sizes=sizes
                    widths="54, 74, 104, 162, 208, 324, 416"
                    id=thumbnail_id
                    alt=media.alt
                  }}
                </button>
              </li>
            {{/not}}
          {{/for}}
        </ul>
        <button
          type="button"
          class="slider-button slider-button--next display-none-tablet {{#if media_count <= 4}}display-none{{/if}}"
          name="next"
          data-step="3"
        >
          {{snippet "icon-arrow"}}
        </button>
      </slider-component>
    {{/if}}
  </div>
</media-gallery>