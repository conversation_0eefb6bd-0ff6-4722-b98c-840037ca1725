{{#comment}}
 
  Renders official ig icon

  Accepts:
  - id: {String} Unique id for svg.
{{/comment}}

{{assign "random_id" (increment "random_id")}}

<svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
  <rect width="27.9999" height="27.9999" rx="14" fill="url(#ig_official_{{section.id}}_{{random_id}})"></rect>
  <path
    d="M11.6708 14.036C11.6708 12.7462 12.7158 11.7004 14.0052 11.7004C15.2945 11.7004 16.3401 12.7462 16.3401 14.036C16.3401 15.3257 15.2945 16.3715 14.0052 16.3715C12.7158 16.3715 11.6708 15.3257 11.6708 14.036ZM10.4086 14.036C10.4086 16.0229 12.0188 17.6336 14.0052 17.6336C15.9915 17.6336 17.6017 16.0229 17.6017 14.036C17.6017 12.049 15.9915 10.4384 14.0052 10.4384C12.0188 10.4384 10.4086 12.049 10.4086 14.036ZM16.9036 10.2957C16.9035 10.462 16.9528 10.6246 17.0451 10.7629C17.1374 10.9012 17.2686 11.009 17.4222 11.0727C17.5757 11.1364 17.7447 11.1531 17.9078 11.1207C18.0708 11.0883 18.2206 11.0083 18.3382 10.8908C18.4558 10.7732 18.5359 10.6235 18.5684 10.4604C18.6009 10.2973 18.5843 10.1283 18.5208 9.97462C18.4572 9.82097 18.3495 9.68962 18.2113 9.59718C18.0732 9.50474 17.9107 9.45537 17.7444 9.4553H17.7441C17.5213 9.45541 17.3076 9.54397 17.15 9.70155C16.9924 9.85912 16.9038 10.0728 16.9036 10.2957ZM11.1755 19.7388C10.4926 19.7077 10.1214 19.594 9.87477 19.4978C9.54775 19.3705 9.31443 19.2188 9.06912 18.9738C8.8238 18.7287 8.67195 18.4956 8.5452 18.1684C8.44905 17.9218 8.3353 17.5504 8.30426 16.8674C8.2703 16.1289 8.26352 15.907 8.26352 14.036C8.26352 12.165 8.27086 11.9438 8.30426 11.2047C8.33536 10.5216 8.44995 10.1509 8.5452 9.90359C8.67251 9.57648 8.82414 9.34309 9.06912 9.09771C9.31409 8.85232 9.54719 8.70043 9.87477 8.57364C10.1213 8.47746 10.4926 8.36368 11.1755 8.33263C11.9138 8.29866 12.1355 8.29188 14.0052 8.29188C15.8748 8.29188 16.0968 8.29922 16.8357 8.33263C17.5186 8.36374 17.8891 8.47836 18.1364 8.57364C18.4634 8.70043 18.6967 8.85266 18.942 9.09771C19.1874 9.34276 19.3387 9.57648 19.466 9.90359C19.5621 10.1502 19.6759 10.5216 19.7069 11.2047C19.7409 11.9438 19.7476 12.165 19.7476 14.036C19.7476 15.907 19.7409 16.1282 19.7069 16.8674C19.6758 17.5504 19.5615 17.9217 19.466 18.1684C19.3387 18.4956 19.187 18.7289 18.942 18.9738C18.6971 19.2186 18.4634 19.3705 18.1364 19.4978C17.8899 19.594 17.5186 19.7078 16.8357 19.7388C16.0974 19.7728 15.8756 19.7796 14.0052 19.7796C12.1347 19.7796 11.9135 19.7728 11.1755 19.7388ZM11.1175 7.07218C10.3718 7.10615 9.86233 7.22442 9.41736 7.39761C8.95654 7.57646 8.56644 7.81641 8.17661 8.20573C7.78679 8.59505 7.54753 8.98589 7.36872 9.44684C7.19558 9.89221 7.07735 10.4016 7.04339 11.1474C7.00888 11.8945 7.00098 12.1333 7.00098 14.036C7.00098 15.9386 7.00888 16.1775 7.04339 16.9245C7.07735 17.6704 7.19558 18.1797 7.36872 18.6251C7.54753 19.0858 7.78685 19.477 8.17661 19.8662C8.56638 20.2553 8.95654 20.495 9.41736 20.6743C9.86317 20.8475 10.3718 20.9658 11.1175 20.9997C11.8647 21.0337 12.103 21.0422 14.0052 21.0422C15.9073 21.0422 16.146 21.0343 16.8928 20.9997C17.6385 20.9658 18.1477 20.8475 18.593 20.6743C19.0535 20.495 19.4439 20.2555 19.8337 19.8662C20.2235 19.4769 20.4623 19.0858 20.6416 18.6251C20.8147 18.1797 20.9335 17.6703 20.9669 16.9245C21.0009 16.1769 21.0088 15.9386 21.0088 14.036C21.0088 12.1333 21.0009 11.8945 20.9669 11.1474C20.933 10.4015 20.8147 9.89193 20.6416 9.44684C20.4623 8.98617 20.2229 8.59567 19.8337 8.20573C19.4445 7.81579 19.0535 7.57646 18.5935 7.39761C18.1477 7.22442 17.6385 7.10559 16.8934 7.07218C16.1466 7.03822 15.9078 7.02975 14.0057 7.02975C12.1036 7.02975 11.8647 7.03766 11.1175 7.07218Z"
    fill="white"
  ></path>
  <defs>
    <linearGradient
      id="ig_official_{{section.id}}_{{random_id}}"
      x1="27.4618"
      y1="27.9999"
      x2="-0.538158"
      y2="-7.5368e-07"
      gradientUnits="userSpaceOnUse"
    >
      <stop stop-color="#FBE18A"></stop>
      <stop offset="0.21" stop-color="#FCBB45"></stop>
      <stop offset="0.38" stop-color="#F75274"></stop>
      <stop offset="0.52" stop-color="#D53692"></stop>
      <stop offset="0.74" stop-color="#8F39CE"></stop>
      <stop offset="1" stop-color="#5B4FE9"></stop>
    </linearGradient>
  </defs>
</svg>