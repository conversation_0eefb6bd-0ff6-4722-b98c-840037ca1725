{{~assign "og_title" (default page_title shop.name)}}
{{~assign "og_url" (default canonical_url request.origin)}}
{{~assign "og_type" "website"}}
{{~assign "og_description" (default page_description (default shop.description shop.name))}}

{{~assign "page_type" template.name}}
{{~#if page_type == "product"}}
  {{~assign "og_type" "product"}}
{{~else if page_type == "article"}}
  {{~assign "og_type" "article"}}
{{~/if}}

<meta property="og:site_name" content="{{shop.name}}" />
<meta property="og:url" content="{{og_url}}" />
<meta property="og:title" content="{{og_title}}" />
<meta property="og:type" content="{{og_type}}" />
<meta property="og:description" content="{{og_description}}" />

{{~#if page_image.src}}
  <meta property="og:image" content="{{page_image.src}}" />
  <meta property="og:image:secure_url" content="{{page_image.src}}" />
  <meta property="og:image:width" content="{{page_image.width}}" />
  <meta property="og:image:height" content="{{page_image.height}}" />
{{/if}}

{{~#if page_type == "product"}}
  <meta property="og:price:amount" content="{{money_without_currency product.price}}" />
  <meta property="og:price:currency" content="{{cart.currency.iso_code}}" />
{{/if}}

{{~#if settings.social_twitter_link}}
  <meta name="twitter:site" content="{{append '@' (last (split settings.social_twitter_link 'twitter.com/'))}}" />
{{/if}}
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content="{{og_title}}" />
<meta name="twitter:description" content="{{og_description}}" />