{{assign "scene" "cart-drawer"}}

<cart-drawer id="cart-drawer" class="cart-drawer">
  <div id="CartDrawer" class="cart-drawer-container visible">
    <div id="CartDrawer-Overlay" class="cart-drawer__overlay"></div>
    <div class="cart-drawer__body">
      <div class="cart-drawer__inner">
        <div class="cart-drawer__inner-wrapper {{#if cart.empty}}cart-empty{{/if}}">
          <div class="cart-drawer__header">
            <h2 class="cart-drawer__heading title5 text-uppercase">{{t "general.header.cart"}}</h2>
            <div class="cart-drawer__close" onclick="this.closest('cart-drawer').close()">
              {{snippet "icon-close"}}
            </div>
          </div>
          <div class="cart__warnings">
            <div class="cart__empty-text-wrapper">
              <h2 class="cart__empty-text body3">{{t "cart.item.empty_cart"}}</h2>
              <a href="{{routes.all_products_collection_url}}" class="button body3 fw-bold">
                {{t "cart.checkout_proceeding.continue_shopping"}}
              </a>
            </div>
            {{snippet "cart-empty-recommend-product" grid_class="grid grid-cols-2" title_class="title5"}}
          </div>
          <cart-drawer-items>
            <div class="cart-drawer__items" id="CartDrawer-CartItems">
              {{#for cart.items as |item|}}
                {{snippet "cart-item" item=item namespace="CartDrawer-" type="drawer"}}
              {{/for}}
            </div>
            <div id="CartDrawer-CartErrors"></div>
          </cart-drawer-items>
          <div class="cart-drawer__checkout-container">
            {{#if cart.discount_enable_cart}}
              {{snippet "cart-coupon" cart=cart}}
            {{/if}}

            <ul class="cart-drawer__amount-wrapper">
              {{#if cart.items_subtotal_price != cart.total_price}}
                <li class="cart__subtotal">
                  <em class="body3">{{t "cart.checkout_proceeding.subtotal"}}</em>
                  <span class="body2 fw-bold">{{money_with_currency cart.items_subtotal_price}}</span>
                </li>
              {{/if}}
              {{#if cart.total_discount > 0}}
                <li class="cart__discount">
                  <em class="body3">{{t "transaction.general.discount_off"}}</em>
                  <span class="body2 fw-bold">-{{money_with_currency cart.total_discount}}</span>
                </li>
              {{/if}}
              <li class="cart__total">
                <em class="body2 fw-bold">{{t "cart.payment.total_sum"}}</em>
                <span class="body2 fw-bold">{{money_with_currency cart.total_price}}</span>
              </li>
            </ul>
            <div class="cart-drawer__checkout-wrapper">
              <div class="cart-drawer__taxes__desc body4 rte">
                {{#if cart.taxes_included and shop.shipping_policy.body}}
                  {{{t "cart.next_step.taxes_included_and_shipping_policy_html" link=shop.shipping_policy.url}}}
                {{else if cart.taxes_included}}
                  {{t "cart.next_step.taxes_included_but_shipping_at_checkout"}}
                {{else if shop.shipping_policy.body}}
                  {{{t "cart.next_step.taxes_and_shipping_policy_at_checkout_html" link=shop.shipping_policy.url}}}
                {{else}}
                  {{t "cart.next_step.calculated_taxes_fees"}}
                {{/if}}
              </div>
            </div>
          </div>
        </div>
      </div>
      {{snippet "cart-fixed-checkout" scene=scene cart=cart shop=shop className="cart-drawer__fixed-checkout"}}
    </div>
  </div>
</cart-drawer>