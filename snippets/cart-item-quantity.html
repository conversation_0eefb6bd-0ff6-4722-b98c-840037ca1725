{{assign "has_qty_rules" false}}
{{#if
  item.variant.quantity_rule.increment > 1 or
  item.variant.quantity_rule.min > 1 or
  item.variant.quantity_rule.max
}}
  {{assign "has_qty_rules" true}}
{{/if}}

{{assign "has_vol_pricing" false}}
{{#if item.variant.quantity_price_breaks.length > 0}}
  {{assign "has_vol_pricing" true}}
{{/if}}

<cart-item-quantity class="cart-item__quantity">
  <div>
    <div class="cart-item__quantity-wrapper">
      {{#if item.enable_quantity_adjust}}
        {{snippet
          "quantity-input"
          namespace=namespace
          value=item.quantity
          id=item.key
          min=item.variant.quantity_rule.min
          max=item.variant.quantity_rule.max
          step=item.variant.quantity_rule.increment
          cart_qty=0
        }}
      {{else}}
        {{snippet
          "quantity-input"
          namespace=namespace
          value=item.quantity
          id=item.key
          min=item.quantity
          max=item.quantity
        }}
      {{/if}}
      {{#if item.enable_delete}}
        <cart-remove-button>{{snippet "icon-delete"}}</cart-remove-button>
      {{/if}}
    </div>
    {{#if has_qty_rules or has_vol_pricing}}
      <details class="cart-item__volume-pricing-wrapper">
        <summary>
          <div class="body5 cart-item__volume-pricing-title">
            {{snippet "icon-warning"}}
            <span>{{t "products.product_list.price_break_tag"}}</span>
          </div>
        </summary>
        <volume-pricing class="global-modal-border-shadow">
          {{#if item.variant.quantity_rule}}
            <div class="cart-item__quantity-rules body5">
              {{#if item.variant.quantity_rule.increment > 1 }}
                <span class="divider">
                  {{t "products.product_details.moq_increment" num=item.variant.quantity_rule.increment}}
                </span>
              {{/if}}
              {{#if item.variant.quantity_rule.min > 1 }}
                <span class="divider">
                  {{t "products.product_details.moq_minimum" num=item.variant.quantity_rule.min}}
                </span>
              {{/if}}
              {{#if item.variant.quantity_rule.max }}
                <span class="divider">
                  {{t "products.product_details.moq_maximum" num=item.variant.quantity_rule.max}}
                </span>
              {{/if}}
            </div>
          {{/if}}
          {{#if item.variant.quantity_price_breaks.length > 0}}
            <ul class="list-unstyled body5">
              <li>
                <span>{{item.variant.quantity_rule.min}}+</span>
                {{assign "price" (money_with_currency item.variant.price)}}
                <span data-text="{{t "products.product_details.each_price" price=price}}">{{{t "products.product_details.each_price" price=price}}}</span>
              </li>
              {{#for item.variant.quantity_price_breaks as |price_break|}}
                {{assign "price_break_price" (money_with_currency price_break.price)}}
                <li>
                  <span>{{price_break.minimum_quantity}}+</span>
                  <span data-text="{{t "products.product_details.each_price" price=price_break_price}}">{{{t "products.product_details.each_price" price=price_break_price}}}</span>
                </li>
              {{/for}}
            </ul>
          {{/if}}
        </volume-pricing>
      </details>
    {{/if}}
    <div class="cart-item__error" id="Line-item-error-{{namespace}}{{item.key}}">
      <span class="cart-item__error-text body6"></span>
      {{snippet "icon-error"}}
    </div>
  </div>
</cart-item-quantity>