{{#comment}}
  Render expandable content controls

  Accepts:
  - class: {String} Class attr.

  Usage:
    {{snippet "expandable-content-controls" class="accordion__footer"}}
{{/comment}}

<div class="expandable-content__controls {{class}}">
  <div class="expandable-content__mask"></div>
  <div class="expandable-content__buttons body3 fw-bold">
    <span class="button--link button-more">商品説明をもっと見る</span>
    <span class="button--link button-less">
        {{!-- <svg data-v-65e65d51="" width="20" height="20" aria-labelledby="close" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 60 60" role="presentation" fill="currentColor" class="svg"><polygon data-v-65e65d51="" points="49.092 13.737 46.263 10.908 30 27.172 13.737 10.908 10.908 13.737 27.172 30 10.908 46.263 13.737 49.092 30 32.828 46.263 49.092 49.092 46.263 32.828 30 49.092 13.737"></polygon></svg> --}}
    閉じる</span>
  </div>
</div>
