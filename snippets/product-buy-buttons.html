{{assign "default_selected_variant" (default section.settings.default_selected_variant true)}}
{{assign "is_selected_variant" product.selected_variant}}
{{assign "is_single_specification_product" product.has_only_default_variant}}

{{#capture "default_variant_id"}}
  {{~#if default_selected_variant or is_selected_variant or is_single_specification_product~}}
    {{default product.selected_or_first_available_variant.id (get "id" (first product.variants))}}
  {{~/if~}}
{{/capture}}

<product-form
  id="{{append product_form_id '__wrapper'}}"
  class="product-form"
  data-default-error-message="{{t 'products.general.no_product_data_found'}}"
>
  <div class="product-form__error-message-wrapper" hidden>
    {{snippet "icon-error"}}
    <span class="product-form__error-message body6"></span>
  </div>
  {{#unless product}}
    <button class="product-form__submit button button--full-width button--secondary" disabled>
      <span>
        {{t "products.general.sold_out"}}
      </span>
    </button>
  {{/unless}}
  {{#form "product" product id=product_form_id}}
    <input type="hidden" name="id" value="{{default_variant_id}}" />
    <div class="product-form__buttons">
      <button
        id="{{product_form_id}}-submit"
        type="submit"
        name="add"
        class="product-form__submit button button--full-width button--secondary"
        data-selected_or_first_available_variant="{{product.selected_or_first_available_variant.available}}"
        {{#unless product.selected_or_first_available_variant.available}}
          disabled
        {{/unless}}
        {{#unless default_variant_id}}
          disabled
        {{/unless}}
      >
        <span class="product-form__submit-span">
          {{#if product.selected_or_first_available_variant.available}}
            {{t "products.product_list.add_to_cart"}}
          {{else}}
            {{t "products.general.sold_out"}}
          {{/if}}
        </span>

        {{snippet "loading-overlay-spinner"}}
        <span class="color_span_move" style="transform:translate(0px, 50px);"><span>

      </button>

      {{payment_button}}
    </div>
  {{/form}}
</product-form>
<style>
    .product-form__submit{
        height:40px;
        overflow:hidden;
        transition: all 1s ease 0s;
    }
    .color_span_move {
    position: absolute;
    /* left: 0; */
    border: 0;
    background: #000;
    width: 150%;
    height: 120%;
    border-radius: 55%;
    transition: all 0.5s ease 0s;
}
.btn-primary > .color_span_move{
    background: #fff;
    transform:translate(0px, 100px);
}
.product-form__submit-span {
    position: relative;
    z-index: 999;
    transition: all 0.5s ease 0s;
}
.pay-button-buy-now-text{
    transition: all 0.5s ease 0s;
    z-index: 999;
}
.product-form .product-form__buttons .pay-button-buy-now{
    overflow: hidden !important;
}
</style>
<script>
document.addEventListener("DOMContentLoaded", function() {
    const whitebutton = document.querySelector('.product-form__submit');
    const whitespan = document.querySelector('.product-form__submit-span');
    const whitemove = document.querySelector('.product-form__submit>.color_span_move');
    whitebutton.addEventListener('mouseenter', () => {
        whitemove.style.transform = 'translate(0px, 0px)';
        whitespan.style.color = '#fff';
    });

    whitebutton.addEventListener('mouseleave', () => {
        whitemove.style.transform = 'translate(0px, -50px)';
        whitespan.style.color = '#000';    
    });
    setTimeout(()=>{
    const blackbutton = document.querySelector('.btn-primary');
    const blackspan = document.querySelector('.pay-button-buy-now-text');
    const span = document.createElement('span');
    span.className = 'color_span_move';
    blackbutton.appendChild(span);
    const blackmove = document.querySelector('.btn-primary > .color_span_move');
    blackbutton.addEventListener('mouseenter', () => {
        blackmove.style.transform = 'translate(0px, 0px)';
        blackspan.style.color = '#000';
    });

    blackbutton.addEventListener('mouseleave', () => {
        blackmove.style.transform = 'translate(0px, 100px)';
        blackspan.style.color = '#fff';
    });
    },1000)
});
</script>