[{"name": "North", "theme_name": "North", "theme_version": "1.3.40", "theme_author": "Shopline", "theme_documentation_url": "", "theme_support_url": "", "theme_cover_img": "https://img.myshopline.com/image/official/e68f38021b1f490d8b2a0e1b85d29b0e.png", "theme_home_pc_preview_img": "https://img.myshopline.com/image/official/e68f38021b1f490d8b2a0e1b85d29b0e.png", "theme_home_mobile_preview_img": "https://img.myshopline.com/image/official/acbda8328506462abc519e256d65b06e.png", "theme_pdp_pc_preview_img": "https://img.myshopline.com/image/official/2d330075f3c5481e9c4d36251c75fc50.png", "theme_pdp_mobile_preview_img": "https://img.myshopline.com/image/official/34a0aa2e8f9f41c1a1dcfc09a83d5f16.png", "theme_type_version": "OS_2.1"}, {"name": "t:settings_schema.logo.name", "settings": [{"type": "image_picker", "id": "logo", "label": "t:settings_schema.logo.settings.logo.label"}, {"type": "range", "id": "desktop_logo_width", "label": "t:settings_schema.logo.settings.desktop_logo_width.label", "info": "t:settings_schema.logo.settings.desktop_logo_width.info", "default": 160, "min": 50, "max": 400, "step": 10, "unit": "px"}, {"type": "range", "id": "desktop_logo_height", "label": "t:settings_schema.logo.settings.desktop_logo_height.label", "default": 60, "min": 60, "max": 400, "step": 10, "unit": "px"}, {"type": "range", "id": "mobile_logo_width", "label": "t:settings_schema.logo.settings.mobile_logo_width.label", "info": "t:settings_schema.logo.settings.mobile_logo_width.info", "default": 145, "min": 30, "max": 200, "step": 10, "unit": "px"}, {"type": "range", "id": "mobile_logo_height", "label": "t:settings_schema.logo.settings.mobile_logo_height.label", "default": 60, "min": 60, "max": 200, "step": 10, "unit": "px"}]}, {"name": "t:settings_schema.color.name", "settings": [{"type": "group_header", "label": "t:settings_schema.color.settings.group_header__0.label"}, {"id": "color_page_background", "type": "color", "label": "t:settings_schema.color.settings.color_page_background.label", "default": "#FFFFFF"}, {"id": "color_text", "type": "color", "label": "t:settings_schema.color.settings.color_text.label", "default": "#29252C"}, {"id": "color_light_text", "type": "color", "label": "t:settings_schema.color.settings.color_light_text.label", "default": "#949494"}, {"id": "color_entry_line", "type": "color", "label": "t:settings_schema.color.settings.color_entry_line.label", "default": "#DDDDDD"}, {"id": "color_card_background", "type": "color", "label": "t:settings_schema.color.settings.color_card_background.label", "info": "t:settings_schema.color.settings.color_card_background.info", "default": ""}, {"id": "color_card_text", "type": "color", "label": "t:settings_schema.color.settings.color_card_text.label", "info": "t:settings_schema.color.settings.color_card_text.info", "default": ""}, {"type": "group_header", "label": "t:settings_schema.color.settings.group_header__1.label"}, {"id": "color_button_background", "type": "color", "label": "t:settings_schema.color.settings.color_button_background.label", "default": "#122D47"}, {"id": "color_button_text", "type": "color", "label": "t:settings_schema.color.settings.color_button_text.label", "default": "#FFFFFF"}, {"id": "color_button_secondary_background", "type": "color", "label": "t:settings_schema.color.settings.color_button_secondary_background.label", "info": "t:settings_schema.color.settings.color_button_secondary_background.info", "default": "#FFFFFF"}, {"id": "color_button_secondary_text", "type": "color", "label": "t:settings_schema.color.settings.color_button_secondary_text.label", "default": "#29252C"}, {"id": "color_button_secondary_border", "type": "color", "label": "t:settings_schema.color.settings.color_button_secondary_border.label", "default": "#DDDDDD"}, {"type": "group_header", "label": "t:settings_schema.color.settings.group_header__2.label"}, {"id": "color_sale", "type": "color", "label": "t:settings_schema.color.settings.color_sale.label", "default": "#29252C"}, {"id": "color_discount", "type": "color", "label": "t:settings_schema.color.settings.color_discount.label", "default": "#E32619"}, {"id": "color_discount_tag_background", "type": "color", "label": "t:settings_schema.color.settings.color_discount_tag_background.label", "default": "#E32619"}, {"id": "color_discount_tag_text", "type": "color", "label": "t:settings_schema.color.settings.color_discount_tag_text.label", "default": "#FFFFFF"}, {"type": "group_header", "label": "t:settings_schema.color.settings.group_header__3.label"}, {"id": "color_cart_dot", "type": "color", "label": "t:settings_schema.color.settings.color_cart_dot.label", "default": "#E60012"}, {"id": "color_cart_dot_text", "type": "color", "label": "t:settings_schema.color.settings.color_cart_dot_text.label", "default": "#FFFFFF"}, {"id": "color_image_background", "type": "color", "label": "t:settings_schema.color.settings.color_image_background.label", "default": "#F4F7FB"}, {"id": "color_image_loading_background", "type": "color", "label": "t:settings_schema.color.settings.color_image_loading_background.label", "default": "#F4F7FB"}, {"id": "color_mask", "type": "color", "label": "t:settings_schema.color.settings.color_mask.label", "default": "#171717"}, {"id": "color_shadow", "type": "color", "label": "t:settings_schema.color.settings.color_shadow.label", "default": ""}, {"type": "group_header", "label": "t:settings_schema.color.settings.group_header__4.label"}, {"id": "color_scheme_1_bg", "type": "color", "label": "t:settings_schema.color.settings.color_scheme_1_bg.label", "default": "#122D47"}, {"id": "color_scheme_1_text", "type": "color", "label": "t:settings_schema.color.settings.color_scheme_1_text.label", "default": "#FFFFFF"}, {"type": "group_header", "label": "t:settings_schema.color.settings.group_header__5.label"}, {"id": "color_scheme_2_bg", "type": "color", "label": "t:settings_schema.color.settings.color_scheme_2_bg.label", "default": "#ECF0F4"}, {"id": "color_scheme_2_text", "type": "color", "label": "t:settings_schema.color.settings.color_scheme_2_text.label", "default": "#122D47"}, {"type": "group_header", "label": "t:settings_schema.color.settings.group_header__6.label"}, {"id": "color_scheme_3_bg", "type": "color", "label": "t:settings_schema.color.settings.color_scheme_3_bg.label", "default": "#000000"}, {"id": "color_scheme_3_text", "type": "color", "label": "t:settings_schema.color.settings.color_scheme_3_text.label", "default": "#FFFFFF"}]}, {"name": "t:settings_schema.font.name", "settings": [{"type": "group_header", "label": "t:settings_schema.font.settings.group_header__0.label"}, {"id": "title_font_family", "type": "font", "label": "t:settings_schema.font.settings.title_font_family.label", "default": "Montserrat:600"}, {"id": "title_letter_spacing", "type": "range", "label": "t:settings_schema.font.settings.title_letter_spacing.label", "default": 0, "min": -75, "max": 250, "step": 25, "unit": "%"}, {"id": "title_font_size", "type": "range", "label": "t:settings_schema.font.settings.title_font_size.label", "default": 30, "min": 22, "max": 60, "unit": "px"}, {"id": "title_line_height", "type": "range", "label": "t:settings_schema.font.settings.title_line_height.label", "default": 1.2, "min": 0.8, "max": 2, "step": 0.1}, {"id": "title_uppercase", "type": "switch", "label": "t:settings_schema.font.settings.title_uppercase.label", "default": false}, {"type": "group_header", "label": "t:settings_schema.font.settings.group_header__1.label"}, {"id": "body_font_family", "type": "font", "label": "t:settings_schema.font.settings.body_font_family.label", "default": "Montserrat:regular"}, {"id": "body_letter_spacing", "type": "range", "label": "t:settings_schema.font.settings.body_letter_spacing.label", "default": 0, "min": -75, "max": 250, "step": 25, "unit": "%"}, {"id": "body_font_size", "type": "range", "label": "t:settings_schema.font.settings.body_font_size.label", "default": 14, "min": 12, "max": 20, "unit": "px"}, {"id": "body_line_height", "type": "range", "label": "t:settings_schema.font.settings.body_line_height.label", "default": 1.6, "min": 1, "max": 2, "step": 0.1}]}, {"name": "t:settings_schema.layout.name", "settings": [{"id": "page_width", "type": "range", "label": "t:settings_schema.layout.settings.page_width.label", "min": 1000, "max": 1920, "step": 100, "unit": "px", "default": 1920}, {"id": "section_vertical_gap", "type": "range", "label": "t:settings_schema.layout.settings.section_vertical_gap.label", "min": 0, "max": 100, "step": 4, "unit": "px", "default": 0}, {"type": "group_header", "label": "t:settings_schema.layout.settings.group_header__0.label", "info": "t:settings_schema.layout.settings.group_header__0.info"}, {"id": "grid_horizontal_space", "type": "range", "label": "t:settings_schema.layout.settings.grid_horizontal_space.label", "min": 0, "max": 40, "step": 4, "unit": "px", "default": 20}, {"id": "grid_vertical_space", "type": "range", "label": "t:settings_schema.layout.settings.grid_vertical_space.label", "min": 0, "max": 40, "step": 4, "unit": "px", "default": 20}]}, {"name": "t:settings_schema.button.name", "settings": [{"id": "btn_hover_animation", "type": "select", "label": "t:settings_schema.button.settings.btn_hover_animation.label", "default": "fill_right", "options": [{"value": "light_sweep", "label": "t:settings_schema.button.settings.btn_hover_animation.options__0.label"}, {"value": "overlay_projection", "label": "t:settings_schema.button.settings.btn_hover_animation.options__1.label"}, {"value": "hover_zoom", "label": "t:settings_schema.button.settings.btn_hover_animation.options__2.label"}, {"value": "fill_right", "label": "t:settings_schema.button.settings.btn_hover_animation.options__3.label"}]}, {"type": "group_header", "label": "t:settings_schema.button.settings.group_header__0.label"}, {"id": "btn_border_thickness", "type": "range", "label": "t:settings_schema.button.settings.btn_border_thickness.label", "min": 0, "max": 12, "step": 1, "unit": "px", "default": 1}, {"id": "btn_border_opacity", "type": "range", "label": "t:settings_schema.button.settings.btn_border_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 100}, {"id": "btn_border_radius", "type": "range", "label": "t:settings_schema.button.settings.btn_border_radius.label", "min": 0, "max": 40, "step": 2, "unit": "px", "default": 26}, {"type": "group_header", "label": "t:settings_schema.button.settings.group_header__1.label"}, {"id": "btn_shadow_opacity", "type": "range", "label": "t:settings_schema.button.settings.btn_shadow_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "btn_shadow_offset_x", "type": "range", "label": "t:settings_schema.button.settings.btn_shadow_offset_x.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "btn_shadow_offset_y", "type": "range", "label": "t:settings_schema.button.settings.btn_shadow_offset_y.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "btn_shadow_blur", "type": "range", "label": "t:settings_schema.button.settings.btn_shadow_blur.label", "min": 0, "max": 20, "step": 5, "unit": "px", "default": 0}]}, {"name": "t:settings_schema.sku.name", "settings": [{"type": "group_header", "label": "t:settings_schema.sku.settings.group_header__0.label"}, {"id": "sku_selector_border_thickness", "type": "range", "label": "t:settings_schema.sku.settings.sku_selector_border_thickness.label", "min": 0, "max": 12, "step": 1, "unit": "px", "default": 1}, {"id": "sku_selector_border_opacity", "type": "range", "label": "t:settings_schema.sku.settings.sku_selector_border_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 20}, {"id": "sku_selector_border_radius", "type": "range", "label": "t:settings_schema.sku.settings.sku_selector_border_radius.label", "min": 0, "max": 40, "step": 2, "unit": "px", "default": 0}, {"type": "group_header", "label": "t:settings_schema.sku.settings.group_header__1.label"}, {"id": "sku_selector_shadow_opacity", "type": "range", "label": "t:settings_schema.sku.settings.sku_selector_shadow_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "sku_selector_shadow_offset_x", "type": "range", "label": "t:settings_schema.sku.settings.sku_selector_shadow_offset_x.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "sku_selector_shadow_offset_y", "type": "range", "label": "t:settings_schema.sku.settings.sku_selector_shadow_offset_y.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "sku_selector_shadow_blur", "type": "range", "label": "t:settings_schema.sku.settings.sku_selector_shadow_blur.label", "min": 0, "max": 20, "step": 5, "unit": "px", "default": 0}]}, {"name": "t:settings_schema.input.name", "settings": [{"type": "group_header", "label": "t:settings_schema.input.settings.group_header__0.label"}, {"id": "input_border_thickness", "type": "range", "label": "t:settings_schema.input.settings.input_border_thickness.label", "min": 0, "max": 12, "step": 1, "unit": "px", "default": 1}, {"id": "input_border_opacity", "type": "range", "label": "t:settings_schema.input.settings.input_border_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 20}, {"id": "input_border_radius", "type": "range", "label": "t:settings_schema.input.settings.input_border_radius.label", "min": 0, "max": 40, "step": 2, "unit": "px", "default": 28}, {"type": "group_header", "label": "t:settings_schema.input.settings.group_header__1.label"}, {"id": "input_shadow_opacity", "type": "range", "label": "t:settings_schema.input.settings.input_shadow_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "input_shadow_offset_x", "type": "range", "label": "t:settings_schema.input.settings.input_shadow_offset_x.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "input_shadow_offset_y", "type": "range", "label": "t:settings_schema.input.settings.input_shadow_offset_y.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "input_shadow_blur", "type": "range", "label": "t:settings_schema.input.settings.input_shadow_blur.label", "min": 0, "max": 20, "step": 5, "unit": "px", "default": 0}]}, {"name": "t:settings_schema.product.name", "settings": [{"type": "group_header", "label": "t:settings_schema.product.settings.group_header__0.label"}, {"id": "enable_quick_view", "type": "switch", "label": "t:settings_schema.product.settings.enable_quick_view.label", "default": true}, {"id": "quick_view_button_pc_style", "type": "select", "label": "t:settings_schema.product.settings.quick_view_button_pc_style.label", "info": "t:settings_schema.product.settings.quick_view_button_pc_style.info", "options": [{"value": "button", "label": "t:settings_schema.product.settings.quick_view_button_pc_style.options__0.label"}, {"value": "icon", "label": "t:settings_schema.product.settings.quick_view_button_pc_style.options__1.label"}], "default": "icon"}, {"id": "quick_view_button_mobile_style", "type": "select", "label": "t:settings_schema.product.settings.quick_view_button_mobile_style.label", "info": "t:settings_schema.product.settings.quick_view_button_mobile_style.info", "options": [{"value": "icon", "label": "t:settings_schema.product.settings.quick_view_button_mobile_style.options__0.label"}, {"value": "button", "label": "t:settings_schema.product.settings.quick_view_button_mobile_style.options__1.label"}], "default": "icon"}, {"id": "product_title_show_type", "type": "select", "label": "t:settings_schema.product.settings.product_title_show_type.label", "options": [{"value": "full-display", "label": "t:settings_schema.product.settings.product_title_show_type.options__0.label"}, {"value": "display-1-row", "label": "t:settings_schema.product.settings.product_title_show_type.options__1.label"}, {"value": "display-2-rows", "label": "t:settings_schema.product.settings.product_title_show_type.options__2.label"}], "default": "full-display"}, {"id": "product_pc_title_show", "type": "switch", "label": "t:settings_schema.product.settings.product_pc_title_show.label", "default": true}, {"id": "product_mobile_title_show", "type": "switch", "label": "t:settings_schema.product.settings.product_mobile_title_show.label", "default": true}, {"type": "group_header", "label": "t:settings_schema.product.settings.group_header__1.label", "info": "t:settings_schema.product.settings.group_header__1.info"}, {"id": "product_discount", "type": "switch", "label": "t:settings_schema.product.settings.product_discount.label", "default": true}, {"id": "product_discount_tag_style", "type": "select", "label": "t:settings_schema.product.settings.product_discount_tag_style.label", "options": [{"value": "sale", "label": "t:settings_schema.product.settings.product_discount_tag_style.options__0.label"}, {"value": "save", "label": "t:settings_schema.product.settings.product_discount_tag_style.options__1.label"}], "default": "sale"}, {"id": "product_discount_style", "type": "select", "label": "t:settings_schema.product.settings.product_discount_style.label", "info": "t:settings_schema.product.settings.product_discount_style.info", "options": [{"value": "number", "label": "t:settings_schema.product.settings.product_discount_style.options__0.label"}, {"value": "ratio", "label": "t:settings_schema.product.settings.product_discount_style.options__1.label"}], "default": "number"}, {"id": "product_discount_size", "type": "select", "label": "t:settings_schema.product.settings.product_discount_size.label", "options": [{"value": "medium", "label": "t:settings_schema.product.settings.product_discount_size.options__0.label"}, {"value": "mini", "label": "t:settings_schema.product.settings.product_discount_size.options__1.label"}], "default": "medium"}, {"id": "product_discount_position", "type": "select", "label": "t:settings_schema.product.settings.product_discount_position.label", "options": [{"value": "left_top", "label": "t:settings_schema.product.settings.product_discount_position.options__0.label"}, {"value": "right_top", "label": "t:settings_schema.product.settings.product_discount_position.options__1.label"}, {"value": "left_bottom", "label": "t:settings_schema.product.settings.product_discount_position.options__2.label"}, {"value": "right_bottom", "label": "t:settings_schema.product.settings.product_discount_position.options__3.label"}], "default": "left_bottom"}, {"id": "product_discount_radius", "type": "range", "label": "t:settings_schema.product.settings.product_discount_radius.label", "min": 0, "max": 40, "step": 2, "unit": "px", "default": 0}, {"type": "group_header", "label": "t:settings_schema.product.settings.group_header__2.label", "info": "t:settings_schema.product.settings.group_header__2.info"}, {"type": "select", "id": "product_card_price_show_type", "label": "t:settings_schema.product.settings.product_card_price_show_type.label", "info": "t:settings_schema.product.settings.product_card_price_show_type.info", "options": [{"value": "lowest_price", "label": "t:settings_schema.product.settings.product_card_price_show_type.options__0.label"}, {"value": "price_interval", "label": "t:settings_schema.product.settings.product_card_price_show_type.options__1.label"}, {"value": "from_lowest_price", "label": "t:settings_schema.product.settings.product_card_price_show_type.options__2.label"}], "default": "lowest_price"}]}, {"name": "t:settings_schema.product_card.name", "settings": [{"id": "product_card_style", "type": "select", "label": "t:settings_schema.product_card.settings.product_card_style.label", "options": [{"value": "normal", "label": "t:settings_schema.product_card.settings.product_card_style.options__0.label"}, {"value": "card", "label": "t:settings_schema.product_card.settings.product_card_style.options__1.label"}], "default": "normal"}, {"id": "product_card_image_padding", "type": "range", "label": "t:settings_schema.product_card.settings.product_card_image_padding.label", "info": "t:settings_schema.product_card.settings.product_card_image_padding.info", "min": 0, "max": 20, "step": 2, "unit": "px", "default": 0}, {"id": "product_card_content_align", "type": "select", "label": "t:settings_schema.product_card.settings.product_card_content_align.label", "default": "left", "options": [{"value": "left", "label": "t:settings_schema.product_card.settings.product_card_content_align.options__0.label"}, {"value": "center", "label": "t:settings_schema.product_card.settings.product_card_content_align.options__1.label"}]}, {"type": "group_header", "label": "t:settings_schema.product_card.settings.group_header__0.label"}, {"id": "product_card_border_thickness", "type": "range", "label": "t:settings_schema.product_card.settings.product_card_border_thickness.label", "min": 0, "max": 12, "step": 1, "unit": "px", "default": 0}, {"id": "product_card_border_opacity", "type": "range", "label": "t:settings_schema.product_card.settings.product_card_border_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 10}, {"id": "product_card_border_radius", "type": "range", "label": "t:settings_schema.product_card.settings.product_card_border_radius.label", "min": 0, "max": 40, "step": 2, "unit": "px", "default": 0}, {"type": "group_header", "label": "t:settings_schema.product_card.settings.group_header__1.label"}, {"id": "product_card_shadow_opacity", "type": "range", "label": "t:settings_schema.product_card.settings.product_card_shadow_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "product_card_shadow_offset_x", "type": "range", "label": "t:settings_schema.product_card.settings.product_card_shadow_offset_x.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "product_card_shadow_offset_y", "type": "range", "label": "t:settings_schema.product_card.settings.product_card_shadow_offset_y.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "product_card_shadow_blur", "type": "range", "label": "t:settings_schema.product_card.settings.product_card_shadow_blur.label", "min": 0, "max": 20, "step": 5, "unit": "px", "default": 0}]}, {"name": "t:settings_schema.collection_card.name", "settings": [{"id": "collection_card_style", "type": "select", "label": "t:settings_schema.collection_card.settings.collection_card_style.label", "options": [{"value": "normal", "label": "t:settings_schema.collection_card.settings.collection_card_style.options__0.label"}, {"value": "card", "label": "t:settings_schema.collection_card.settings.collection_card_style.options__1.label"}], "default": "normal"}, {"id": "collection_card_image_padding", "type": "range", "label": "t:settings_schema.collection_card.settings.collection_card_image_padding.label", "info": "t:settings_schema.collection_card.settings.collection_card_image_padding.info", "min": 0, "max": 20, "step": 2, "unit": "px", "default": 0}, {"id": "collection_card_content_align", "type": "select", "label": "t:settings_schema.collection_card.settings.collection_card_content_align.label", "default": "center", "options": [{"value": "left", "label": "t:settings_schema.collection_card.settings.collection_card_content_align.options__0.label"}, {"value": "center", "label": "t:settings_schema.collection_card.settings.collection_card_content_align.options__1.label"}]}, {"type": "group_header", "label": "t:settings_schema.collection_card.settings.group_header__0.label"}, {"id": "collection_card_border_thickness", "type": "range", "label": "t:settings_schema.collection_card.settings.collection_card_border_thickness.label", "min": 0, "max": 12, "step": 1, "unit": "px", "default": 0}, {"id": "collection_card_border_opacity", "type": "range", "label": "t:settings_schema.collection_card.settings.collection_card_border_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 10}, {"id": "collection_card_border_radius", "type": "range", "label": "t:settings_schema.collection_card.settings.collection_card_border_radius.label", "min": 0, "max": 40, "step": 2, "unit": "px", "default": 0}, {"type": "group_header", "label": "t:settings_schema.collection_card.settings.group_header__1.label"}, {"id": "collection_card_shadow_opacity", "type": "range", "label": "t:settings_schema.collection_card.settings.collection_card_shadow_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "collection_card_shadow_offset_x", "type": "range", "label": "t:settings_schema.collection_card.settings.collection_card_shadow_offset_x.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "collection_card_shadow_offset_y", "type": "range", "label": "t:settings_schema.collection_card.settings.collection_card_shadow_offset_y.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "collection_card_shadow_blur", "type": "range", "label": "t:settings_schema.collection_card.settings.collection_card_shadow_blur.label", "min": 0, "max": 20, "step": 5, "unit": "px", "default": 0}]}, {"name": "t:settings_schema.blog_card.name", "settings": [{"id": "blog_card_style", "type": "select", "label": "t:settings_schema.blog_card.settings.blog_card_style.label", "options": [{"value": "normal", "label": "t:settings_schema.blog_card.settings.blog_card_style.options__0.label"}, {"value": "card", "label": "t:settings_schema.blog_card.settings.blog_card_style.options__1.label"}], "default": "normal"}, {"id": "blog_card_image_padding", "type": "range", "label": "t:settings_schema.blog_card.settings.blog_card_image_padding.label", "info": "t:settings_schema.blog_card.settings.blog_card_image_padding.info", "min": 0, "max": 20, "step": 2, "unit": "px", "default": 0}, {"id": "blog_card_content_align", "type": "select", "label": "t:settings_schema.blog_card.settings.blog_card_content_align.label", "default": "center", "options": [{"value": "left", "label": "t:settings_schema.blog_card.settings.blog_card_content_align.options__0.label"}, {"value": "center", "label": "t:settings_schema.blog_card.settings.blog_card_content_align.options__1.label"}]}, {"type": "group_header", "label": "t:settings_schema.blog_card.settings.group_header__0.label"}, {"id": "blog_card_border_thickness", "type": "range", "label": "t:settings_schema.blog_card.settings.blog_card_border_thickness.label", "min": 0, "max": 12, "step": 1, "unit": "px", "default": 0}, {"id": "blog_card_border_opacity", "type": "range", "label": "t:settings_schema.blog_card.settings.blog_card_border_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 10}, {"id": "blog_card_border_radius", "type": "range", "label": "t:settings_schema.blog_card.settings.blog_card_border_radius.label", "min": 0, "max": 40, "step": 2, "unit": "px", "default": 0}, {"type": "group_header", "label": "t:settings_schema.blog_card.settings.group_header__1.label"}, {"id": "blog_card_shadow_opacity", "type": "range", "label": "t:settings_schema.blog_card.settings.blog_card_shadow_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "blog_card_shadow_offset_x", "type": "range", "label": "t:settings_schema.blog_card.settings.blog_card_shadow_offset_x.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "blog_card_shadow_offset_y", "type": "range", "label": "t:settings_schema.blog_card.settings.blog_card_shadow_offset_y.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "blog_card_shadow_blur", "type": "range", "label": "t:settings_schema.blog_card.settings.blog_card_shadow_blur.label", "min": 0, "max": 20, "step": 5, "unit": "px", "default": 0}]}, {"name": "t:settings_schema.other_card.name", "settings": [{"type": "group_header", "label": "t:settings_schema.other_card.settings.group_header__0.label"}, {"id": "card_border_thickness", "type": "range", "label": "t:settings_schema.other_card.settings.card_border_thickness.label", "min": 0, "max": 12, "step": 1, "unit": "px", "default": 0}, {"id": "card_border_opacity", "type": "range", "label": "t:settings_schema.other_card.settings.card_border_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 10}, {"id": "card_border_radius", "type": "range", "label": "t:settings_schema.other_card.settings.card_border_radius.label", "min": 0, "max": 40, "step": 2, "unit": "px", "default": 0}, {"type": "group_header", "label": "t:settings_schema.other_card.settings.group_header__1.label"}, {"id": "card_shadow_opacity", "type": "range", "label": "t:settings_schema.other_card.settings.card_shadow_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "card_shadow_offset_x", "type": "range", "label": "t:settings_schema.other_card.settings.card_shadow_offset_x.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "card_shadow_offset_y", "type": "range", "label": "t:settings_schema.other_card.settings.card_shadow_offset_y.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "card_shadow_blur", "type": "range", "label": "t:settings_schema.other_card.settings.card_shadow_blur.label", "min": 0, "max": 20, "step": 5, "unit": "px", "default": 0}]}, {"name": "t:settings_schema.content.name", "settings": [{"type": "group_header", "label": "t:settings_schema.content.settings.group_header__0.label"}, {"id": "content_border_thickness", "type": "range", "label": "t:settings_schema.content.settings.content_border_thickness.label", "min": 0, "max": 12, "step": 1, "unit": "px", "default": 0}, {"id": "content_border_opacity", "type": "range", "label": "t:settings_schema.content.settings.content_border_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "content_border_radius", "type": "range", "label": "t:settings_schema.content.settings.content_border_radius.label", "min": 0, "max": 40, "step": 2, "unit": "px", "default": 0}, {"type": "group_header", "label": "t:settings_schema.content.settings.group_header__1.label"}, {"id": "content_shadow_opacity", "type": "range", "label": "t:settings_schema.content.settings.content_shadow_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "content_shadow_offset_x", "type": "range", "label": "t:settings_schema.content.settings.content_shadow_offset_x.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "content_shadow_offset_y", "type": "range", "label": "t:settings_schema.content.settings.content_shadow_offset_y.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "content_shadow_blur", "type": "range", "label": "t:settings_schema.content.settings.content_shadow_blur.label", "min": 0, "max": 20, "step": 5, "unit": "px", "default": 0}]}, {"name": "t:settings_schema.media_files.name", "settings": [{"type": "group_header", "label": "t:settings_schema.media_files.settings.group_header__0.label"}, {"id": "media_border_thickness", "type": "range", "label": "t:settings_schema.media_files.settings.media_border_thickness.label", "min": 0, "max": 12, "step": 1, "unit": "px", "default": 0}, {"id": "media_border_opacity", "type": "range", "label": "t:settings_schema.media_files.settings.media_border_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "media_border_radius", "type": "range", "label": "t:settings_schema.media_files.settings.media_border_radius.label", "min": 0, "max": 40, "step": 2, "unit": "px", "default": 0}, {"type": "group_header", "label": "t:settings_schema.media_files.settings.group_header__1.label"}, {"id": "media_shadow_opacity", "type": "range", "label": "t:settings_schema.media_files.settings.media_shadow_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "media_shadow_offset_x", "type": "range", "label": "t:settings_schema.media_files.settings.media_shadow_offset_x.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "media_shadow_offset_y", "type": "range", "label": "t:settings_schema.media_files.settings.media_shadow_offset_y.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "media_shadow_blur", "type": "range", "label": "t:settings_schema.media_files.settings.media_shadow_blur.label", "min": 0, "max": 20, "step": 5, "unit": "px", "default": 0}]}, {"name": "t:settings_schema.dropdown_menu.name", "settings": [{"type": "group_header", "label": "t:settings_schema.dropdown_menu.settings.group_header__0.label"}, {"id": "menu_modal_border_thickness", "type": "range", "label": "t:settings_schema.dropdown_menu.settings.menu_modal_border_thickness.label", "min": 0, "max": 12, "step": 1, "unit": "px", "default": 1}, {"id": "menu_modal_border_opacity", "type": "range", "label": "t:settings_schema.dropdown_menu.settings.menu_modal_border_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 10}, {"id": "menu_modal_border_radius", "type": "range", "label": "t:settings_schema.dropdown_menu.settings.menu_modal_border_radius.label", "min": 0, "max": 40, "step": 2, "unit": "px", "default": 4}, {"type": "group_header", "label": "t:settings_schema.dropdown_menu.settings.group_header__1.label"}, {"id": "menu_modal_shadow_opacity", "type": "range", "label": "t:settings_schema.dropdown_menu.settings.menu_modal_shadow_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "menu_modal_shadow_offset_x", "type": "range", "label": "t:settings_schema.dropdown_menu.settings.menu_modal_shadow_offset_x.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "menu_modal_shadow_offset_y", "type": "range", "label": "t:settings_schema.dropdown_menu.settings.menu_modal_shadow_offset_y.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 4}, {"id": "menu_modal_shadow_blur", "type": "range", "label": "t:settings_schema.dropdown_menu.settings.menu_modal_shadow_blur.label", "min": 0, "max": 20, "step": 5, "unit": "px", "default": 5}]}, {"name": "t:settings_schema.drawer.name", "settings": [{"type": "group_header", "label": "t:settings_schema.drawer.settings.group_header__0.label"}, {"id": "drawer_border_thickness", "type": "range", "label": "t:settings_schema.drawer.settings.drawer_border_thickness.label", "min": 0, "max": 12, "step": 1, "unit": "px", "default": 1}, {"id": "drawer_border_opacity", "type": "range", "label": "t:settings_schema.drawer.settings.drawer_border_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 10}, {"type": "group_header", "label": "t:settings_schema.drawer.settings.group_header__1.label"}, {"id": "drawer_shadow_opacity", "type": "range", "label": "t:settings_schema.drawer.settings.drawer_shadow_opacity.label", "min": 0, "max": 100, "step": 5, "unit": "%", "default": 0}, {"id": "drawer_shadow_offset_x", "type": "range", "label": "t:settings_schema.drawer.settings.drawer_shadow_offset_x.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 0}, {"id": "drawer_shadow_offset_y", "type": "range", "label": "t:settings_schema.drawer.settings.drawer_shadow_offset_y.label", "min": -12, "max": 12, "step": 2, "unit": "px", "default": 4}, {"id": "drawer_shadow_blur", "type": "range", "label": "t:settings_schema.drawer.settings.drawer_shadow_blur.label", "min": 0, "max": 20, "step": 5, "unit": "px", "default": 5}]}, {"name": "t:settings_schema.cart.name", "settings": [{"id": "cart_type", "type": "select", "label": "t:settings_schema.cart.settings.cart_type.label", "options": [{"value": "drawer", "label": "t:settings_schema.cart.settings.cart_type.options__0.label"}, {"value": "page", "label": "t:settings_schema.cart.settings.cart_type.options__1.label"}, {"value": "notification", "label": "t:settings_schema.cart.settings.cart_type.options__2.label"}], "default": "drawer"}, {"type": "group_header", "label": "t:settings_schema.cart.settings.group_header__0.label"}, {"type": "text", "id": "cart_empty_recommend_title", "label": "t:settings_schema.cart.settings.cart_empty_recommend_title.label", "default": "You May Also Like"}, {"type": "collection_picker", "id": "cart_empty_recommend_collection", "label": "t:settings_schema.cart.settings.cart_empty_recommend_collection.label"}, {"type": "range", "id": "cart_empty_recommend_product_to_show", "min": 4, "max": 12, "step": 1, "default": 12, "label": "t:settings_schema.cart.settings.cart_empty_recommend_product_to_show.label"}, {"id": "cart_empty_recommend_product_image_ratio", "type": "select", "label": "t:settings_schema.cart.settings.cart_empty_recommend_product_image_ratio.label", "options": [{"value": "adapt", "label": "t:settings_schema.cart.settings.cart_empty_recommend_product_image_ratio.options__0.label"}, {"value": "100", "label": "t:settings_schema.cart.settings.cart_empty_recommend_product_image_ratio.options__1.label"}, {"value": "133.33", "label": "3:4"}, {"value": "75", "label": "t:settings_schema.cart.settings.cart_empty_recommend_product_image_ratio.options__3.label"}, {"value": "150", "label": "t:settings_schema.cart.settings.cart_empty_recommend_product_image_ratio.options__4.label"}], "default": "150"}, {"id": "cart_empty_recommend_product_image_fill_type", "type": "select", "label": "t:settings_schema.cart.settings.cart_empty_recommend_product_image_fill_type.label", "options": [{"value": "contain", "label": "t:settings_schema.cart.settings.cart_empty_recommend_product_image_fill_type.options__0.label"}, {"value": "cover", "label": "t:settings_schema.cart.settings.cart_empty_recommend_product_image_fill_type.options__1.label"}], "default": "contain"}]}, {"name": "t:settings_schema.checkout.name", "settings": [{"type": "group_header", "label": "t:settings_schema.checkout.settings.group_header__0.label"}, {"id": "co_banner_pc_img", "type": "image_picker", "label": "t:settings_schema.checkout.settings.co_banner_pc_img.label", "default": {}}, {"id": "co_banner_phone_img", "type": "image_picker", "label": "t:settings_schema.checkout.settings.co_banner_phone_img.label", "info": "t:settings_schema.checkout.settings.co_banner_phone_img.info", "default": {}}, {"id": "co_banner_pc_height", "type": "select", "label": "t:settings_schema.checkout.settings.co_banner_pc_height.label", "options": [{"label": "t:settings_schema.checkout.settings.co_banner_pc_height.options__0.label", "value": "natural"}, {"value": "high", "label": "t:settings_schema.checkout.settings.co_banner_pc_height.options__1.label"}, {"value": "low", "label": "t:settings_schema.checkout.settings.co_banner_pc_height.options__2.label"}], "default": "low"}, {"id": "co_banner_phone_height", "type": "select", "label": "t:settings_schema.checkout.settings.co_banner_phone_height.label", "options": [{"label": "t:settings_schema.checkout.settings.co_banner_phone_height.options__0.label", "value": "natural"}, {"value": "high", "label": "t:settings_schema.checkout.settings.co_banner_phone_height.options__1.label"}, {"value": "low", "label": "t:settings_schema.checkout.settings.co_banner_phone_height.options__2.label"}], "default": "low"}, {"id": "co_banner_img_show", "type": "select", "label": "t:settings_schema.checkout.settings.co_banner_img_show.label", "options": [{"label": "t:settings_schema.checkout.settings.co_banner_img_show.options__0.label", "value": "top"}, {"value": "center", "label": "t:settings_schema.checkout.settings.co_banner_img_show.options__1.label"}, {"value": "bottom", "label": "t:settings_schema.checkout.settings.co_banner_img_show.options__2.label"}], "default": "center"}, {"id": "co_full_screen", "type": "switch", "label": "t:settings_schema.checkout.settings.co_full_screen.label", "default": true}, {"type": "group_header", "label": "t:settings_schema.checkout.settings.group_header__1.label"}, {"id": "co_checkout_image", "type": "image_picker", "label": "t:settings_schema.checkout.settings.co_checkout_image.label", "default": {}}, {"id": "co_logo_size", "type": "select", "label": "t:settings_schema.checkout.settings.co_logo_size.label", "options": [{"label": "t:settings_schema.checkout.settings.co_logo_size.options__0.label", "value": "small"}, {"value": "medium", "label": "t:settings_schema.checkout.settings.co_logo_size.options__1.label"}, {"value": "large", "label": "t:settings_schema.checkout.settings.co_logo_size.options__2.label"}], "default": "medium"}, {"id": "co_logo_position", "type": "select", "label": "t:settings_schema.checkout.settings.co_logo_position.label", "options": [{"label": "t:settings_schema.checkout.settings.co_logo_position.options__0.label", "value": "left"}, {"value": "center", "label": "t:settings_schema.checkout.settings.co_logo_position.options__1.label"}, {"value": "right", "label": "t:settings_schema.checkout.settings.co_logo_position.options__2.label"}], "default": "left"}, {"type": "group_header", "label": "t:settings_schema.checkout.settings.group_header__2.label"}, {"id": "co_bg_image", "type": "image_picker", "label": "t:settings_schema.checkout.settings.co_bg_image.label", "default": {}}, {"id": "co_background_color", "type": "color", "label": "t:settings_schema.checkout.settings.co_background_color.label", "default": "#FFFFFF"}, {"id": "co_form_bg_color", "type": "select", "label": "t:settings_schema.checkout.settings.co_form_bg_color.label", "options": [{"label": "t:settings_schema.checkout.settings.co_form_bg_color.options__0.label", "value": "white"}, {"value": "transparent", "label": "t:settings_schema.checkout.settings.co_form_bg_color.options__1.label"}], "default": "white"}, {"type": "group_header", "label": "t:settings_schema.checkout.settings.group_header__3.label"}, {"id": "co_order_bg_image", "type": "image_picker", "label": "t:settings_schema.checkout.settings.co_order_bg_image.label", "default": {}}, {"id": "co_order_background_color", "type": "color", "label": "t:settings_schema.checkout.settings.co_order_background_color.label", "default": "#FAFAFA"}, {"type": "group_header", "label": "t:settings_schema.checkout.settings.group_header__4.label"}, {"id": "co_type_title_font", "type": "font_lib_picker", "label": "t:settings_schema.checkout.settings.co_type_title_font.label", "options": ["Open Sans", "<PERSON><PERSON>", "<PERSON>", "Noto Sans Display", "Roboto", "<PERSON><PERSON><PERSON>", "Fjalla One", "<PERSON>", "Source Sans Pro", "EB Garamond", "PT Serif", "<PERSON><PERSON>", "Arvo", "Playfair Display", "Vidaloka", "<PERSON><PERSON><PERSON>", "Playball", "Lobster Two"], "default": {"lib": "system", "value": "Helvetica Neue / Arial"}}, {"id": "co_type_body_font", "type": "font_lib_picker", "label": "t:settings_schema.checkout.settings.co_type_body_font.label", "options": ["Open Sans", "<PERSON><PERSON>", "<PERSON>", "Noto Sans Display", "Roboto", "<PERSON><PERSON><PERSON>", "Fjalla One", "<PERSON>", "Source Sans Pro", "EB Garamond", "PT Serif", "<PERSON><PERSON>", "Arvo", "Playfair Display", "Vidaloka", "<PERSON><PERSON><PERSON>", "Playball", "Lobster Two"], "default": {"lib": "system", "value": "Helvetica Neue / Arial"}}, {"type": "group_header", "label": "t:settings_schema.checkout.settings.group_header__5.label"}, {"id": "co_color_btn_bg", "type": "color", "label": "t:settings_schema.checkout.settings.co_color_btn_bg.label", "default": "#276EAF"}, {"id": "co_color_err_color", "type": "color", "label": "t:settings_schema.checkout.settings.co_color_err_color.label", "info": "t:settings_schema.checkout.settings.co_color_err_color.info", "default": "#F04949"}, {"id": "co_color_msg_color", "type": "color", "label": "t:settings_schema.checkout.settings.co_color_msg_color.label", "info": "t:settings_schema.checkout.settings.co_color_msg_color.info", "default": "#276EAF"}]}, {"name": "t:settings_schema.media_sosial.name", "settings": [{"type": "group_header", "label": "t:settings_schema.media_sosial.settings.group_header__0.label", "info": "t:settings_schema.media_sosial.settings.group_header__0.info"}, {"id": "show_official_icon", "type": "switch", "label": "t:settings_schema.media_sosial.settings.show_official_icon.label", "default": false}, {"id": "social_facebook_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_facebook_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"id": "social_twitter_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_twitter_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"id": "social_pinterest_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_pinterest_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"id": "social_instagram_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_instagram_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"id": "social_snapchat_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_snapchat_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"id": "social_tiktok_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_tiktok_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"id": "social_youtube_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_youtube_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"id": "social_vimeo_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_vimeo_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"id": "social_tumblr_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_tumblr_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"id": "social_linkedin_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_linkedin_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"id": "social_whatsapp_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_whatsapp_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"id": "social_line_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_line_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"id": "social_kakao_link", "type": "text", "label": "t:settings_schema.media_sosial.settings.social_kakao_link.label", "default": "", "placeholder": "http://", "format": "uri"}, {"type": "group_header", "label": "t:settings_schema.media_sosial.settings.group_header__1.label", "info": "t:settings_schema.media_sosial.settings.group_header__1.info"}, {"id": "show_official_share_icon", "type": "switch", "label": "t:settings_schema.media_sosial.settings.show_official_share_icon.label", "default": false}, {"id": "show_social_name", "type": "switch", "label": "t:settings_schema.media_sosial.settings.show_social_name.label", "default": true}, {"id": "share_to_facebook", "type": "switch", "label": "t:settings_schema.media_sosial.settings.share_to_facebook.label", "default": true}, {"id": "share_to_twitter", "type": "switch", "label": "t:settings_schema.media_sosial.settings.share_to_twitter.label", "default": true}, {"id": "share_to_pinterest", "type": "switch", "label": "t:settings_schema.media_sosial.settings.share_to_pinterest.label", "default": true}, {"id": "share_to_line", "type": "switch", "label": "t:settings_schema.media_sosial.settings.share_to_line.label", "default": true}, {"id": "share_to_whatsapp", "type": "switch", "label": "t:settings_schema.media_sosial.settings.share_to_whatsapp.label", "default": true}, {"id": "share_to_tumblr", "type": "switch", "label": "t:settings_schema.media_sosial.settings.share_to_tumblr.label", "default": true}]}, {"name": "t:settings_schema.search_behavior.name", "settings": [{"id": "show_search_goods_price", "type": "switch", "label": "t:settings_schema.search_behavior.settings.show_search_goods_price.label", "default": true}]}, {"name": "t:settings_schema.breadcrumb.name", "settings": [{"id": "show_pc_breadcrumb", "type": "switch", "label": "t:settings_schema.breadcrumb.settings.show_pc_breadcrumb.label", "default": false}, {"id": "show_mobile_breadcrumb", "type": "switch", "label": "t:settings_schema.breadcrumb.settings.show_mobile_breadcrumb.label", "default": false}]}, {"name": "t:settings_schema.favicon.name", "settings": [{"id": "favicon_image", "type": "favicon_picker", "label": "t:settings_schema.favicon.settings.favicon_image.label", "info": "t:settings_schema.favicon.settings.favicon_image.info", "default": ""}]}]