{"sections": {"17363308009519ad3221": {"disabled": false, "type": "shopline://apps/高阶组件库/sections/featured-carousel/01405986-ae6f-4fcc-adaa-2a21f14efc9f", "settings": {"pc_image_height": "auto", "mb_image_height": "auto", "autoplay": true, "scroll_parallax": false, "pagination_type": "left_right_arrow", "transition_effect": "dynamic_switch", "autoplay_speed": 5, "container_margin_config": {"pc": {"left": 0, "right": 0, "top": 0, "bottom": 0}, "mobile": {"left": 0, "right": 0, "top": 0, "bottom": 0}}, "mobile_pagination_type": "touch"}, "blocks": {"1736330800951c342563": {"type": "image", "icon": "image", "settings": {"pc_image_area": "center", "mb_image_area": "top", "pc_sub_title": "", "pc_title": "", "pc_body": "", "pc_text_position": "top-center", "pc_mask": 0, "mb_sub_title": "", "mb_title": "", "mb_body": "", "mb_text_position": "top-center", "mb_mask": 0, "button_text1": "", "jump_link1": "", "button_text2": "", "jump_link2": "", "title_font_family": "Inter:600", "title_font_color": "#ffffff", "title_pc_font_size": 32, "title_m_font_size": 19, "title_letter_spacing": 0, "title_line_height": 1.4, "body_font_family": "Inter", "body_font_color": "#ffffff", "body_pc_font_size": 12, "body_m_font_size": 12, "body_letter_spacing": 0, "body_line_height": 1.6, "sub_title_font_family": "Inter:regular", "sub_title_font_color": "#ffffff", "sub_title_pc_font_size": 14, "sub_title_m_font_size": 14, "sub_title_letter_spacing": 0, "sub_title_line_height": 1.6, "btn_font_family": "Montserrat:500", "btn_font_color1": "#1c1d1d", "btn_font_color2": "#1c1d1d", "btn_pc_font_size": 13, "btn_m_font_size": 12, "btn_letter_spacing": 0, "btn_line_height": 1.6, "btn_background1": "#ffffff", "btn_background2": "#ffffff", "btn_style1": "primary", "btn_style2": "primary", "btn_hover_animation": "1", "btn_border_thickness": 1, "btn_border_opacity": 100, "btn_border_radius": 0, "btn_border_color1": "#ffffff", "btn_border_color2": "#ffffff", "btn_shadow_opacity": 0, "btn_shadow_offset_x": 0, "btn_shadow_offset_y": 4, "btn_shadow_blur": 5, "btn_shadow_color": "#000000", "mb_image": "shopline://shop_images/kv_Grand-Form-lp-sp_6990914102022122474.jpg", "pc_image": "shopline://shop_images/kv_Grand-Form_lp-pc_6990913894152428394.jpg"}, "blockId": "1736330800951c342563"}}, "block_order": ["1736330800951c342563"], "custom_css": []}, "173638660539498ef25d": {"disabled": false, "type": "image-with-text", "settings": {"image_height": "auto", "pc_image_width": "small", "pc_image_position": "right", "pc_box_align": "middle", "pc_text_align": "left", "image_overlap_display": false, "color_scheme": "none", "mobile_text_align": "left", "padding_top": 30, "padding_bottom": 30, "image": "shopline://shop_images/1.gif"}, "blocks": {"1736386605394ba433c6": {"type": "content", "settings": {"content": "<span style=\"font-size: 20px;\"><strong>①キルト層</strong></span><br><strong>ふんわりと心地良い生地で至福の時間を約束</strong><br><br>やわらかく繊細でありつつも、優れた吸湿・発散性と通気性を持つ生地を使用。ノンホルムアルデヒドで、乳幼児にも安心してご使用いただけます。<br><br>さらに、グランドリュクスマットレスシリーズでは、1892年に誕生したマットレス生地専門会社「BekaertDeslee」の生地を採用し、贅沢の肌心地をお届け。"}, "blockId": "1736386605394ba433c6"}}, "block_order": ["1736386605394ba433c6"], "custom_css": []}, "1736408247884c0f37f2": {"disabled": false, "type": "custom-html", "settings": {"html": "<style><!--\n.background {\n            background-color: #fafafa;\n            font-size: 1rem;\n            padding: 15px;\n            border-radius: 15px;\n        }\n        .navbar a {\n            color: #399e96;\n            line-height: 2;\n        }\n        .navbar a:hover {\n            opacity: 75%;\n        }\n        .container {\n        \tdisplay: flex;\n        \tflex-wrap: wrap;\n        \tgap: 20px;\n        \talign-items: center;\n        }\n    \t.item-30 {\n        \tflex: 0.32 1;\n        \tbox-sizing: border-box;\n        }\n    \t.item-50 {\n        \tflex: 1 1 calc(50% - 30px);\n        \tbox-sizing: border-box;\n        }\n        @media (max-width: 768px) {\n        \t.item-30, .item-50 {\n            \tflex: 1 1 100%;\n            }\n        }\n    \timg {margin: auto !important;}\n    \t.navimage {\n        \tborder-radius: 10px;\n        }\n--></style>\n<div class=\"rich-text section-padding color-scheme-none \">\n  <div class=\"page-width\n      \n      rich-text__wrapper--left\n      rich-text__blocks\">\n        <div class=\"rte rich-text__text body2\"><p><span data-font-family=\"default\">人生の3分の1は睡眠時間なので、日々活き活きと生活するには睡眠環境を整えることが大切です。体に合わないマットレスを使っていると、睡眠の質が下がったり、腰痛や肩こりの原因になったりします。朝起きたタイミングで寝た感じがしない・腰が痛い・頭が痛いなどの違和感を感じたら、マットレスの買い替えどきです。</span></p>\n<p><span data-font-family=\"default\">CAGUUUではそんな方のために、オリジナルマットレスシリーズ「</span><span data-font-family=\"default\">Grand Form</span><span data-font-family=\"default\">（</span><span data-font-family=\"default\">グランドフォーム</span><span data-font-family=\"default\">）</span><span data-font-family=\"default\">」が登場！毎日朝までぐっすり眠れるマットレスを多数用意しているので、自分に合ったマットレスを見つけて快適に眠りましょう！</span></p></div>\n  </div>\n</div>\n<div class=\"page-width\" style=\"max-width:1024px;\">\n<div class=\"container background\">\n<div class=\"item-30\"><img class=\"navimage\" src=\"https://img.myshopline.com/image/store/1726464192427/Memory-Rest-t7.jpeg?w=1125&h=848\" alt=\"イメージ図\"></div>\n<div class=\"item-30 rte\">\n<p style=\"padding-left: 20px;\"><strong>目次</strong></p>\n<ul class=\"navbar\">\n<li><a href=\"#section1\">こだわりポイント</a></li>\n<li><a href=\"#section2\">安心サービス</a></li>\n<li><a href=\"#section3\">マットレスのご紹介</a></li>\n<li><a href=\"#section4\">マットレスの選び方</a></li>\n<li><a href=\"#section5\">オンライン相談サービス(無料)</a></li>\n<li><a href=\"#section6\">合わせて使えるベッドアイテム</a></li>\n</ul>\n</div>\n</div>\n</div>", "color_scheme": "none", "padding_top": 20, "padding_bottom": 30}, "blocks": {}, "block_order": [], "custom_css": []}, "1736491572282e09b871": {"disabled": false, "type": "custom-html", "settings": {"html": "<div class=\"page-width\">\n  <h2 id=\"section1\" class=\"rte rich-text__heading title3\">Grand Formのこだわりポイント\n</h2>\n</div>", "color_scheme": "none", "padding_top": 0, "padding_bottom": 0}, "blocks": {}, "block_order": [], "custom_css": ["h2 {background-color: #399e96; color: #fff; text-align: center; padding: 10px; border-radius: 10px; font-size: 24px;}", "@media (max-width: 768px) {h2 {font-size: 18px; }}"]}, "1737341984967efdc505": {"disabled": false, "type": "shopline://apps/高阶组件库/sections/anchor/01405986-ae6f-4fcc-adaa-2a21f14efc9f", "settings": {"pc_show": true, "mb_show": true, "title": "", "text_align": "left", "divider_style": "straight", "sticky": true, "container_bg_color": "#ffffff", "container_bg_color_gradient": "", "container_bg_opacity": 95, "container_max_width": 1420, "select_background_color": "#399e96", "select_text_color": "#ffffff", "container_config": {"pc": {"left": 40, "right": 40, "top": 15, "bottom": 15}, "mobile": {"left": 0, "right": 0, "top": 8, "bottom": 8, "lock": true}}, "shadow_opacity": 0, "shadow_offset_x": 0, "shadow_offset_y": 0, "shadow_blur": 10, "shadow_color": "#000000", "title_font_family": "Poppins:600", "title_font_color": "#000000", "title_pc_font_size": 30, "title_m_font_size": 15, "title_letter_spacing": 0, "title_line_height": 1.5, "sub_title_font_family": "Inter:600", "sub_title_font_color": "#1A1C1E", "sub_title_pc_font_size": 18, "sub_title_m_font_size": 12, "sub_title_letter_spacing": 0, "sub_title_line_height": 1.5, "open_select": true}, "blocks": {"17373419849683a2e903": {"type": "menu_anchor", "icon": "comment", "settings": {"title": "こだわりポイント", "anchor": {"sectionId": "1736491572282e09b871", "blockId": null}}, "blockId": "17373419849683a2e903"}, "1737341984968b6b094d": {"type": "menu_anchor", "icon": "comment", "settings": {"title": "安心サービス", "anchor": {"sectionId": "17382230841188e8055d", "blockId": null}}, "blockId": "1737341984968b6b094d"}, "17373419849685d1f4ab": {"type": "menu_anchor", "icon": "comment", "settings": {"title": "マットレスのご紹介", "anchor": {"sectionId": "173872266024790f92ee", "blockId": null}}, "blockId": "17373419849685d1f4ab"}, "1737341984968c4a76ff": {"type": "menu_anchor", "icon": "comment", "settings": {"title": "オンライン相談サービス(無料)", "anchor": {"sectionId": "1741255038180180d560", "blockId": null}}, "blockId": "1737341984968c4a76ff", "disabled": false}, "173872163163519db738": {"type": "menu_anchor", "settings": {"title": "マットレスの選び方", "anchor": {"sectionId": "17423762284924b84da7", "blockId": null}}}}, "block_order": ["17373419849683a2e903", "1737341984968b6b094d", "17373419849685d1f4ab", "173872163163519db738", "1737341984968c4a76ff"], "custom_css": []}, "17382230841188e8055d": {"disabled": false, "type": "custom-html", "settings": {"html": "<div class=\"page-width\">\n  <h2 id=\"section2\" class=\"rte rich-text__heading title3\">安心サービス\n</h2>\n</div>", "color_scheme": "none", "padding_top": 0, "padding_bottom": 0}, "blocks": {}, "block_order": [], "custom_css": ["h2 {background-color: #399e96; color: #fff; text-align: center; padding: 10px; border-radius: 10px; font-size: 24px;}", "@media (max-width: 768px) {h2 {font-size: 18px; }}"]}, "173872266024790f92ee": {"disabled": false, "type": "custom-html", "settings": {"html": "<div class=\"page-width\">\n  <h2 id=\"section3\" class=\"rte rich-text__heading title3\">マットレスのご紹介\n</h2>\n</div>", "color_scheme": "none", "padding_top": 0, "padding_bottom": 0}, "blocks": {}, "block_order": [], "custom_css": ["h2 {background-color: #399e96; color: #fff; text-align: center; padding: 10px; border-radius: 10px; font-size: 24px;}", "@media (max-width: 768px) {h2 {font-size: 18px; }}"]}, "17387227971964202262": {"disabled": false, "type": "combine-shoppable-image", "settings": {"text_title": "人気ランキングNo.1", "description": "<p><strong><span data-font-family=\"default\">グランドエアマットレス</span></strong><strong><span data-font-family=\"default\">（</span></strong><span data-font-family=\"default\">Grand Air Mattress</span><strong><span data-font-family=\"default\">）</span></strong></p>\n<p><span data-font-family=\"default\">日本人に最適な硬さで作られたマットレスが登場。穴あきメモリーフォームで通気性にも優れ、快適な睡眠環境を提供します。3D立体構造ウレタンが体圧を均等に分散させることで、毎晩の睡眠をさらに質の高いものに。独立スプリングで体をしっかり支え、どんな姿勢でも安心して寝られます。<br><br></span></p>\n<strong>▪︎</strong>【硬さ】8　【厚み】23cm　【コイル数】約725個", "button_text": "", "show_columns": "3", "image_ratio": "100%", "anchor_quick_view": false, "anchor_show_type": "fixed", "padding_top": 30, "padding_bottom": 60}, "blocks": {"1738722797197e8e79ba": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 5, "vertical_axis_position1": 5, "horizontal_axis_position2": 4, "vertical_axis_position2": 0, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p1.jpg", "product1": "16068285004603255782704095"}, "blockId": "1738722797197e8e79ba"}, "1738722797197b47aa2e": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p2.jpg"}, "blockId": "1738722797197b47aa2e"}, "1738722797197c22ee15": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p3.jpg"}, "blockId": "1738722797197c22ee15"}}, "block_order": ["1738722797197e8e79ba", "1738722797197b47aa2e", "1738722797197c22ee15"], "custom_css": ["h5 {color: #399e96; text-decoration: underline;}"]}, "17412409682985d1f606": {"disabled": false, "type": "image-with-text", "settings": {"image_height": "auto", "pc_image_width": "small", "pc_image_position": "right", "pc_box_align": "middle", "pc_text_align": "left", "image_overlap_display": false, "color_scheme": "none", "mobile_text_align": "left", "padding_top": 30, "padding_bottom": 30, "image": "shopline://shop_images/2.gif"}, "blocks": {"1736386605394ba433c6": {"type": "content", "settings": {"content": "<strong><span style=\"font-size: 20px;\">②クッション層</span></strong><br><strong>上質な柔らかさで理想な寝心地へ</strong><br><br>CAGUUUはコイルを感じないようすべて5cm以上のクッション層を実現。クッション層が厚ければ厚いほど心地よさが増し、より自然な寝姿勢へ導きます。<br>柔軟性・高弾力性・高反発性を兼ね備えたクッション材で、包み込まれるような寝心地をご体感いただけます。"}, "blockId": "1736386605394ba433c6"}}, "block_order": ["1736386605394ba433c6"], "custom_css": []}, "174124097446920840bf": {"disabled": false, "type": "image-with-text", "settings": {"image_height": "auto", "pc_image_width": "small", "pc_image_position": "right", "pc_box_align": "middle", "pc_text_align": "left", "image_overlap_display": false, "color_scheme": "none", "mobile_text_align": "left", "padding_top": 30, "padding_bottom": 80, "image": "shopline://shop_images/3.gif"}, "blocks": {"1736386605394ba433c6": {"type": "content", "settings": {"content": "<strong><span style=\"font-size: 20px;\">③スプリング層</span></strong><br><strong>ポケットコイルで優れた体圧分散性</strong><br><br>CAGUUUではポケットコイルを採用しています。コイルスプリングが1つひとつ独立した構造で、体を「点」で支えるため、体の凹凸に合わせて沈み込みやすく、体圧分散性が高いのが特徴です。<br>ポケットコイルは動いても横揺れしにくく、夫婦のお互いの寝返りの振動が伝わりにくい設計です。<br>※グランドキッズマットレスのみ、ボンネルコイルを使用しています。"}, "blockId": "1736386605394ba433c6"}}, "block_order": ["1736386605394ba433c6"], "custom_css": []}, "17412421469403bec725": {"disabled": false, "type": "combine-shoppable-image", "settings": {"text_title": "柔らかいマットレスがお好みの方向け", "description": "<p><strong><span data-font-family=\"default\">グランドソフトエアマットレス</span></strong><strong><span data-font-family=\"default\">（</span></strong><span data-font-family=\"default\">Grand SoftAir Mattress</span><strong><span data-font-family=\"default\">）</span></strong></p>\n<p><span data-font-family=\"default\">適度な柔らかさを追求した、ふわふわの触感のマットレス。メモリーフォームを使用し、内側にある2層のフォームに穴を施すことで、熱を効率的に放散してくれます。独立スプリングが体をしっかり支え、朝まで理想な寝姿勢をサポート！<br></span></p>\n<strong><br>▪︎</strong>【硬さ】5、【厚み】23cm、【コイル数】約725個", "button_text": "", "show_columns": "3", "image_ratio": "100%", "anchor_quick_view": false, "anchor_show_type": "fixed", "padding_top": 58, "padding_bottom": 60}, "blocks": {"1738722797197e8e79ba": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 5, "vertical_axis_position1": 5, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p4.jpg", "product1": "16068285104670625068524095"}, "blockId": "1738722797197e8e79ba"}, "1738722797197b47aa2e": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p5.jpg"}, "blockId": "1738722797197b47aa2e"}, "1738722797197c22ee15": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p6.jpg"}, "blockId": "1738722797197c22ee15"}}, "block_order": ["1738722797197e8e79ba", "1738722797197b47aa2e", "1738722797197c22ee15"], "custom_css": ["h5 {color: #399e96; text-decoration: underline;}"]}, "17412423907312713fc3": {"disabled": false, "type": "image-with-text", "settings": {"image_height": "auto", "pc_image_width": "medium", "pc_image_position": "left", "pc_box_align": "middle", "pc_text_align": "left", "image_overlap_display": false, "color_scheme": "none", "mobile_text_align": "left", "padding_top": 30, "padding_bottom": 80, "image": "shopline://shop_images/Grand Form.png"}, "blocks": {"1736386605394ba433c6": {"type": "content", "settings": {"content": "<span style=\"font-size: 17px;\"><strong>①120日無料体験</strong></span><br><span style=\"font-size: 14px;\">お家でじっくり120日間無料でトライアルできます。万が一ご満足いただけない場合は、全額返金いたします。</span><br><span style=\"font-size: 12px;\">※商品到着後からの120日間となります。<br></span><br><span style=\"font-size: 17px;\"><strong>②10年保証</strong></span><br><span style=\"font-size: 14px;\">Grand Formシリーズの全商品には、すべて10年保証が付いています。品質に自信があるからこそ実現できるもので、皆さんにも安心してご購入いただけます。<br></span><br><span style=\"font-size: 17px;\"><strong>③3回分割払いOK</strong></span><br><span style=\"font-size: 14px;\">あと払いや分割払い（分割手数料無料／3回あと払い）が可能です。決済画面にて「ペイディ（Paidy）」を選択してください。</span><br><span style=\"font-size: 14px;\"><br>※詳しい使用方法は<a id=\"17412503364381\" href=\"https://caguuu.com/pages/installment-payment-explanation\" target=\"_blank\" rel=\"noopener\" data-link=\"{&quot;pageType&quot;:-1,&quot;value&quot;:&quot;https://caguuu.com/pages/installment-payment-explanation&quot;,&quot;linkType&quot;:&quot;input&quot;,&quot;label&quot;:&quot;https://caguuu.com/pages/installment-payment-explanation&quot;}\">こちら</a></span>"}, "blockId": "1736386605394ba433c6"}}, "block_order": ["1736386605394ba433c6"], "custom_css": []}, "1741255038180180d560": {"disabled": false, "type": "image-with-text", "settings": {"image_height": "auto", "pc_image_width": "medium", "pc_image_position": "right", "pc_box_align": "middle", "pc_text_align": "left", "image_overlap_display": false, "color_scheme": "none", "mobile_text_align": "center", "padding_top": 60, "padding_bottom": 60, "image": "shopline://shop_images/talkingーicon.png"}, "blocks": {"1736386605394ba433c6": {"type": "content", "settings": {"content": "<h2 id=\"section5\" class=\"title3 image-with-text__title image-with-text__title--size-small\">無料の快眠カウンセラーがマットレス選びをサポート</h2>\n<p><span data-font-family=\"default\">マットレス選びにお悩みでしたら、ぜひ一度無料のオンライン相談サービスをご利用ください！<br></span><span data-font-family=\"default\">当社の<strong>「快眠カウンセラー」</strong>がご相談を承ります。</span></p>"}, "blockId": "1736386605394ba433c6"}, "1741255221801d9505c0": {"type": "button", "settings": {"button_text": "今すぐ相談", "link": "shopline://pages/6889968417593124287"}}}, "block_order": ["1736386605394ba433c6", "1741255221801d9505c0"], "custom_css": []}, "1741326109990e4d9791": {"disabled": false, "type": "combine-shoppable-image", "settings": {"text_title": "寝心地にこだわりたい方向け", "description": "<p><strong><span data-font-family=\"default\">グランドエアグライドマットレス</span></strong><strong><span data-font-family=\"default\">（</span></strong><span data-font-family=\"default\">Grand Air Glide Mattress</span><strong><span data-font-family=\"default\">）</span></strong></p>\n<p><span data-font-family=\"default\">ソフト、ミディアム、ハードの3種類をラインアップしており、BMIに応じて自分にぴったりのマットレスを選べます。自分の体型や好みに合わせて最適な硬さを選ぶことで、より良い睡眠とリラックスを実現。<br><br></span></p>\n<strong>▪︎ソフト：</strong>【硬さ】６、【厚み】25cm、【コイル数】約1706個&nbsp;<br><strong>▪︎ミディアム：</strong>【硬さ】7 、【厚み】26cm、【コイル数】約1706個&nbsp;<br><strong>▪︎ハード：</strong>【硬さ】8、【厚み】25cm、【コイル数】約1706個", "button_text": "", "show_columns": "3", "image_ratio": "100%", "anchor_quick_view": false, "anchor_show_type": "fixed", "padding_top": 58, "padding_bottom": 60}, "blocks": {"1738722797197e8e79ba": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 5, "vertical_axis_position1": 5, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p7.jpg", "product1": "16068417269753223674054095"}, "blockId": "1738722797197e8e79ba"}, "1738722797197b47aa2e": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p8.jpg"}, "blockId": "1738722797197b47aa2e"}, "1738722797197c22ee15": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p9.jpg"}, "blockId": "1738722797197c22ee15"}}, "block_order": ["1738722797197e8e79ba", "1738722797197b47aa2e", "1738722797197c22ee15"], "custom_css": ["h5 {color: #399e96; text-decoration: underline;}"]}, "1741326133957120e25d": {"disabled": false, "type": "combine-shoppable-image", "settings": {"text_title": "", "description": "<p><strong><span data-font-family=\"default\">グランドリュクスマットレスplus</span></strong><strong><span data-font-family=\"default\">（</span></strong><span data-font-family=\"default\">Grand Luxe Mattress Plus</span><strong><span data-font-family=\"default\">）</span></strong></p>\n<p><span data-font-family=\"default\">7層の高品質素材が織りなすプレミアムなマットレス。超軽量のソフトウレタンが体を包み込むような優しさを感じさせ、贅沢な寝心地を体感。パット部分は取り外し可能なので、2つの選べる寝心地からお好みに合わせてお選びいただけます。<br></span></p>\n<strong><br>▪︎マットレス＋パット：</strong>【硬さ】5、【厚み】30cm、【コイル数】約1947個&nbsp;<br><strong>▪︎マットレス：</strong>【硬さ】7.5、　【厚み】35cm、【コイル数】約1947個", "button_text": "", "show_columns": "3", "image_ratio": "100%", "anchor_quick_view": false, "anchor_show_type": "fixed", "padding_top": 0, "padding_bottom": 60}, "blocks": {"1738722797197e8e79ba": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 5, "vertical_axis_position1": 5, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p13.jpg", "product1": "16068415860571266188254095"}, "blockId": "1738722797197e8e79ba"}, "1738722797197b47aa2e": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p14.jpg"}, "blockId": "1738722797197b47aa2e"}, "1738722797197c22ee15": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p15.jpg"}, "blockId": "1738722797197c22ee15"}}, "block_order": ["1738722797197e8e79ba", "1738722797197b47aa2e", "1738722797197c22ee15"], "custom_css": []}, "174132613714072b1668": {"disabled": false, "type": "combine-shoppable-image", "settings": {"text_title": "高級マットレスをお探しの方向け", "description": "<p><strong><span data-font-family=\"default\">グランドリュクスマットレス</span></strong><strong><span data-font-family=\"default\">（</span></strong><span data-font-family=\"default\">Grand Luxe Mattress</span><strong><span data-font-family=\"default\">）</span></strong></p>\n<p><span data-font-family=\"default\">ベカルトテンセル生地を使用したマットレスは、シルクのような贅沢な肌触りで心地よい眠りをサポート。3.5cmの小口径コイルが力を素早く分散し、体圧を均等に分けます。ふんわりとした厚みのあるプレミアムコンフォート層により、コイルの硬さを感じることなく快適な寝心地を実現。<br><br></span></p>\n<strong>▪︎【</strong>硬さ】6.5、【厚み】32cm、【コイル数】約1974個", "button_text": "", "show_columns": "3", "image_ratio": "100%", "anchor_quick_view": false, "anchor_show_type": "fixed", "padding_top": 58, "padding_bottom": 60}, "blocks": {"1738722797197e8e79ba": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 5, "vertical_axis_position1": 5, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p10.jpg", "product1": "16068417184249652277644095"}, "blockId": "1738722797197e8e79ba"}, "1738722797197b47aa2e": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p11.jpg"}, "blockId": "1738722797197b47aa2e"}, "1738722797197c22ee15": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p12.jpg"}, "blockId": "1738722797197c22ee15"}}, "block_order": ["1738722797197e8e79ba", "1738722797197b47aa2e", "1738722797197c22ee15"], "custom_css": ["h5 {color: #399e96; text-decoration: underline;}"]}, "1741326146085f056f83": {"disabled": false, "type": "combine-shoppable-image", "settings": {"text_title": "子供向け", "description": "<p><strong><span data-font-family=\"default\">グランドキッズマットレス</span></strong><strong><span data-font-family=\"default\">（</span></strong><span data-font-family=\"default\">Grand Kids Mattress</span><strong><span data-font-family=\"default\">）</span></strong></p>\n<p><span data-font-family=\"default\">高反発ボンネルコイルを使用した硬めの作りで、背骨をしっかりサポート。最高級のベカルト生地は赤ちゃんの敏感な肌にも優しく、ぐっすりと眠れます。丈夫な作りなのでへたりにくく、成長に合わせて長期間お使いいただける頼れるマットレスです。<br></span></p>\n<strong><br>▪︎ソフト：</strong>【硬さ】8、【厚み】19cm", "button_text": "", "show_columns": "3", "image_ratio": "100%", "anchor_quick_view": false, "anchor_show_type": "fixed", "padding_top": 58, "padding_bottom": 60}, "blocks": {"1738722797197e8e79ba": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 5, "vertical_axis_position1": 5, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p16.jpg", "product1": "16068415969991094538174095"}, "blockId": "1738722797197e8e79ba"}, "1738722797197b47aa2e": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p17.jpg"}, "blockId": "1738722797197b47aa2e"}, "1738722797197c22ee15": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p18.jpg"}, "blockId": "1738722797197c22ee15"}}, "block_order": ["1738722797197e8e79ba", "1738722797197b47aa2e", "1738722797197c22ee15"], "custom_css": ["h5 {color: #399e96; text-decoration: underline;}"]}, "17413261498462b333b3": {"disabled": false, "type": "combine-shoppable-image", "settings": {"text_title": "デスクワークが多い方／シニア向け", "description": "<p><strong><span data-font-family=\"default\">グランドグレースマットレス</span></strong><strong><span data-font-family=\"default\">（</span></strong><span data-font-family=\"default\">Grand Grace Mattress</span><strong><span data-font-family=\"default\">）</span></strong></p>\n<p><span data-font-family=\"default\">ココナッツ繊維のような素材を使用し、適度な硬さと弾力性でスムーズな寝返りをサポートするマットレス。約3690個の通気口を備えたハードウレタンが優れた放熱と通気性を提供し、常に快適な温度で眠れます。厚み9cmまたは23cmの2タイプから選べ、ベッドにぴったり合わせられるのも魅力的なポイントです。<br></span></p>\n<strong><br>▪︎レギャラー：</strong>【硬さ】9、【厚み】23cm　【コイル数】約837個&nbsp;<br><strong>▪︎スリム：</strong>【硬さ】9、【厚み】9cm、【コイル数】約837個", "button_text": "", "show_columns": "3", "image_ratio": "100%", "anchor_quick_view": false, "anchor_show_type": "fixed", "padding_top": 58, "padding_bottom": 60}, "blocks": {"1738722797197e8e79ba": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 5, "vertical_axis_position1": 5, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p19.jpg", "product1": "16068285000722686135394095"}, "blockId": "1738722797197e8e79ba"}, "1738722797197b47aa2e": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p20.jpg"}, "blockId": "1738722797197b47aa2e"}, "1738722797197c22ee15": {"type": "image", "settings": {"product_butotn_text": "View product", "horizontal_axis_position1": 25, "vertical_axis_position1": 50, "horizontal_axis_position2": 50, "vertical_axis_position2": 50, "horizontal_axis_position3": 75, "vertical_axis_position3": 50, "horizontal_axis_position4": 75, "vertical_axis_position4": 25, "horizontal_axis_position5": 50, "vertical_axis_position5": 25, "image": "shopline://shop_images/Memory-Rest-p21.jpg"}, "blockId": "1738722797197c22ee15"}}, "block_order": ["1738722797197e8e79ba", "1738722797197b47aa2e", "1738722797197c22ee15"], "custom_css": ["h5 {color: #399e96; text-decoration: underline;}"]}, "17422192736749ea073b": {"disabled": false, "type": "shopline://apps/高阶组件库/sections/product-comparison/01405986-ae6f-4fcc-adaa-2a21f14efc9f", "settings": {"title": "", "icon_width": "half", "contnet_align": "left", "item1": "価格", "item2": "こんな人におすすめ", "item3": "特徴", "item4": "厚さ", "item5": "硬さ", "item6": "寝心地", "item7": "層構造", "item8": "キルト層", "item9": "クッション層", "item10": "スプリング層", "line_color": "#000000", "line_color_alpha": 10, "pc_show": true, "mb_show": true, "container_max_width": 1500, "container_config": {"pc": {"left": 40, "right": 40, "top": 30, "bottom": 30, "lock": true}, "mobile": {"left": 20, "right": 20, "top": 40, "bottom": 40}}, "container_margin_config": {"pc": {"left": 0, "right": 0, "top": 0, "bottom": 0}, "mobile": {"left": 0, "right": 0, "top": 0, "bottom": 0}}, "body_font_family": "Inter", "body_font_color": "#000000", "body_pc_font_size": 14, "body_m_font_size": 12, "body_letter_spacing": 0, "body_line_height": 1.6, "title_font_family": "Inter:600", "title_font_color": "#000000", "title_pc_font_size": 32, "title_m_font_size": 24, "title_letter_spacing": 0, "title_line_height": 1.2, "sub_title_font_family": "Inter:600", "sub_title_font_color": "#000000", "sub_title_pc_font_size": 14, "sub_title_m_font_size": 14, "sub_title_letter_spacing": 0, "sub_title_line_height": 1.2, "btn_font_family": "Montserrat:500", "btn_font_color": "#000000", "btn_pc_font_size": 13, "btn_m_font_size": 12, "btn_letter_spacing": 0, "btn_line_height": 1.6, "btn_background": "#111111", "btn_style": "secondary", "btn_hover_animation": "1", "btn_border_thickness": 1, "btn_border_opacity": 100, "btn_border_radius": 0, "btn_border_color": "#111111", "btn_shadow_opacity": 0, "btn_shadow_offset_x": 0, "btn_shadow_offset_y": 4, "btn_shadow_blur": 5, "btn_shadow_color": "#000000", "container_bg_color": "#FFFFFF"}, "blocks": {"174221927367475abcf3": {"type": "product", "settings": {"img_ratio": "100%", "title": "グランドエアマットレス", "desc": "", "price_text": "", "btn_text": "", "jump_link": "https://caguuu.com/products/brx02412007", "item1": "39,590円〜", "item2": "はじめてマットレスを購入する方", "item3": "人間工学に基づく4層構造マットレス", "item4": "23cm", "item5": "8 硬め", "item6": "強化されたサポート力", "item7": "4層", "item8": "ファブリック", "item9": "3cm 高反発ウレタン 3cm 3D立体構造ウレタン", "item10": "6巻きポケットコイル", "icon1": "", "icon2": "", "icon3": "", "icon4": "", "icon5": "", "icon6": "", "icon7": "", "icon8": "", "icon9": "", "icon10": "", "image": "shopline://shop_images/Memory-Rest-p1.jpg"}, "blockId": "174221927367475abcf3"}, "1742219763703c8c3f75": {"type": "product", "settings": {"img_ratio": "100%", "title": "グランドエアグライドマットレスHARD", "desc": "", "price_text": "", "btn_text": "", "jump_link": "https://caguuu.com/preview/products/brx02412001", "item1": "68,790円〜", "item2": "自分に合った硬さを選びたい方", "item3": "しっかりとした寝心地で 身体をしっかり支える", "item4": "25cm", "item5": "8 硬め", "item6": "しっかりとした弾力で 包み込む心地よさ", "item7": "4層", "item8": "ファブリック", "item9": "2cm 高反発ウレタン 2cm 低反発ウレタン", "item10": "6巻きポケットコイル", "icon1": "", "icon2": "", "icon3": "", "icon4": "", "icon5": "", "icon6": "", "icon7": "", "icon8": "", "icon9": "", "icon10": "", "image": "shopline://shop_images/Rest-5.jpg"}}, "1742219765050711aebd": {"type": "product", "settings": {"img_ratio": "100%", "title": "グランドエアグライドマットレスMEDIUM", "desc": "", "price_text": "", "btn_text": "", "jump_link": "https://caguuu.com/preview/products/brx02412001", "item1": "82,690円〜", "item2": "自分に合った硬さを選びたい方", "item3": "心安らぐ、リラックスできる眠り", "item4": "26cm", "item5": "7 やや硬め", "item6": "しなやかなサポートで心地よくフィット", "item7": "6層", "item8": "ファブリック", "item9": "2.5cm TPEファイバー 3.5cm 天然キャメル+ハードウレタン 3cm 3D立体構造ウレタン", "item10": "6巻きポケットコイル", "icon1": "", "icon2": "", "icon3": "", "icon4": "", "icon5": "", "icon6": "", "icon7": "", "icon8": "", "icon9": "", "icon10": "", "image": "shopline://shop_images/Rest-4.jpg"}}, "174221976637673c4dfe": {"type": "product", "settings": {"img_ratio": "100%", "title": "グランドエアグライドマットレスSOFT", "desc": "", "price_text": "", "btn_text": "", "jump_link": "https://caguuu.com/preview/products/brx02412001", "item1": "73,390円〜", "item2": "自分に合った硬さを選びたい方", "item3": "適度なサポート力で安心感を提供", "item4": "25cm", "item5": "6柔らかい", "item6": "柔らかさの中にもしっかりとした支えあり", "item7": "5層", "item8": "ファブリック", "item9": "2cm メモリーフォーム 2.5cm TPEファイバー", "item10": "3.5巻き高弾性ミニコイル 6巻きポケットコイル", "icon1": "", "icon2": "", "icon3": "", "icon4": "", "icon5": "", "icon6": "", "icon7": "", "icon8": "", "icon9": "", "icon10": "", "image": "shopline://shop_images/Rest-3.jpg"}}, "17422197675293e63a58": {"type": "product", "settings": {"img_ratio": "100%", "title": "グランドソフトエアマットレス", "desc": "", "price_text": "", "btn_text": "", "jump_link": "https://caguuu.com/products/brx02412008", "item1": "38,690円〜", "item2": "コスパ重視の方", "item3": "ふんわりとした寝心地で 贅沢なリラックスタイム", "item4": "22cm", "item5": "5 柔らかい", "item6": "体に優しくフィットするソフトな寝心地", "item7": "4層", "item8": "ファブリック", "item9": "3cm 多孔形状記憶ウレタン 3cm 多孔硬質ウレタン", "item10": "6巻きポケットコイル", "icon1": "", "icon2": "", "icon3": "", "icon4": "", "icon5": "", "icon6": "", "icon7": "", "icon8": "", "icon9": "", "icon10": "", "image": "shopline://shop_images/Memory-Rest-p4.jpg"}}}, "block_order": ["174221927367475abcf3", "17422197675293e63a58", "174221976637673c4dfe", "1742219765050711aebd", "1742219763703c8c3f75"], "custom_css": []}, "17422766658120ac3c15": {"disabled": false, "type": "shopline://apps/高阶组件库/sections/product-comparison/01405986-ae6f-4fcc-adaa-2a21f14efc9f", "settings": {"title": "", "icon_width": "half", "contnet_align": "left", "item1": "価格", "item2": "こんな人におすすめ", "item3": "特徴", "item4": "厚さ", "item5": "硬さ", "item6": "寝心地", "item7": "層構造", "item8": "キルト層", "item9": "クッション層", "item10": "スプリング層", "line_color": "#000000", "line_color_alpha": 10, "pc_show": true, "mb_show": true, "container_max_width": 1500, "container_config": {"pc": {"left": 40, "right": 40, "top": 60, "bottom": 60}, "mobile": {"left": 20, "right": 20, "top": 40, "bottom": 40}}, "container_margin_config": {"pc": {"left": 0, "right": 0, "top": 0, "bottom": 0}, "mobile": {"left": 0, "right": 0, "top": 0, "bottom": 0}}, "body_font_family": "Inter", "body_font_color": "#000000", "body_pc_font_size": 14, "body_m_font_size": 12, "body_letter_spacing": 0, "body_line_height": 1.6, "title_font_family": "Inter:600", "title_font_color": "#000000", "title_pc_font_size": 22, "title_m_font_size": 24, "title_letter_spacing": 0, "title_line_height": 1.2, "sub_title_font_family": "Inter:600", "sub_title_font_color": "#000000", "sub_title_pc_font_size": 14, "sub_title_m_font_size": 14, "sub_title_letter_spacing": 0, "sub_title_line_height": 1.2, "btn_font_family": "Montserrat:500", "btn_font_color": "#000000", "btn_pc_font_size": 13, "btn_m_font_size": 12, "btn_letter_spacing": 0, "btn_line_height": 1.6, "btn_background": "#111111", "btn_style": "secondary", "btn_hover_animation": "1", "btn_border_thickness": 1, "btn_border_opacity": 100, "btn_border_radius": 0, "btn_border_color": "#111111", "btn_shadow_opacity": 0, "btn_shadow_offset_x": 0, "btn_shadow_offset_y": 4, "btn_shadow_blur": 5, "btn_shadow_color": "#000000", "container_bg_color": "#FFFFFF"}, "blocks": {"174221927367475abcf3": {"type": "product", "settings": {"img_ratio": "100%", "title": "グランドリュクスマットレス", "desc": "", "price_text": "", "btn_text": "", "jump_link": "https://caguuu.com/products/brx02412002", "item1": "108,490円〜", "item2": "ワンランク上の眠りをお求めの方", "item3": "贅沢な寝心地で極上のひとときを", "item4": "32cm", "item5": "6.5 標準的", "item6": "ふんわり雲のような 心地よい柔らかさ", "item7": "9層", "item8": "ベカルト®テンセル生地", "item9": "5.5cm コンフォート層 5cm ダブルTPEファイバー 5cm ハードウレタン", "item10": "14cm小口径ポケットコイル", "icon1": "", "icon2": "", "icon3": "", "icon4": "", "icon5": "", "icon6": "", "icon7": "", "icon8": "", "icon9": "", "icon10": "", "image": "shopline://shop_images/Rest-6.jpg"}, "blockId": "174221927367475abcf3"}, "1742219763703c8c3f75": {"type": "product", "settings": {"img_ratio": "100%", "title": "グランドグレースマットレスSLIM", "desc": "", "price_text": "", "btn_text": "", "jump_link": "https://caguuu.com/products/brx02412006", "item1": "40,390円〜", "item2": "デスクワークが多い方 ご年配の方", "item3": "環境にもやさしい 赤ちゃんでも安心な素材", "item4": "9cm", "item5": "9 硬め", "item6": "しなやかなサポートで心地よくフィット", "item7": "3層", "item8": "ファブリック", "item9": "2cm ハードウレタン 4cm 特殊ポリエステル繊維", "item10": "", "icon1": "", "icon2": "", "icon3": "", "icon4": "", "icon5": "", "icon6": "", "icon7": "", "icon8": "", "icon9": "", "icon10": "", "image": "shopline://shop_images/Rest-10.jpg"}}, "1742219765050711aebd": {"type": "product", "settings": {"img_ratio": "100%", "title": "グランドグレースマットレス", "desc": "", "price_text": "", "btn_text": "", "jump_link": "https://caguuu.com/products/brx02412006", "item1": "65,490円〜", "item2": "デスクワークが多い方 ご年配の方", "item3": "適度な硬さで 快適な寝心地を実現", "item4": "23cm", "item5": "9 硬め", "item6": "弾力のある支えで 快適な眠りを", "item7": "4層", "item8": "ファブリック", "item9": "2cm ハードウレタン 2cm 特殊ポリエステル繊維", "item10": "6巻きボケットコイル", "icon1": "", "icon2": "", "icon3": "", "icon4": "", "icon5": "", "icon6": "", "icon7": "", "icon8": "", "icon9": "", "icon10": "", "image": "shopline://shop_images/Rest-9.jpg"}}, "174221976637673c4dfe": {"type": "product", "settings": {"img_ratio": "100%", "title": "グランドキッズマットレス", "desc": "", "price_text": "", "btn_text": "", "jump_link": "https://caguuu.com/products/brx02412004", "item1": "66,490円〜", "item2": "お子様をお持ちの方", "item3": "ぐっすり眠れる環境が 健やかな成長を支える", "item4": "19cm", "item5": "8 硬め", "item6": "滑らかな肌触り 赤ちゃんにもやさしいファブリック", "item7": "4層", "item8": "ファブリック", "item9": "2.5cm TPEファイバー 3cm ハードウレタン", "item10": "ボンネルコイル", "icon1": "", "icon2": "", "icon3": "", "icon4": "", "icon5": "", "icon6": "", "icon7": "", "icon8": "", "icon9": "", "icon10": "", "image": "shopline://shop_images/Rest-8.jpg"}}, "17422197675293e63a58": {"type": "product", "settings": {"img_ratio": "100%", "title": "グランドリュクスマットレスPLUS", "desc": "", "price_text": "", "btn_text": "", "jump_link": "https://caguuu.com/products/brx02412003", "item1": "131,790円〜", "item2": "ワンランク上の眠りをお求めの方", "item3": "しっかりとした寝心地がお好みの方へ  ", "item4": "30cm", "item5": "7 やや硬め", "item6": "全身をやさしく包み込む快適なフィット感  ", "item7": "9層", "item8": "ベカルト®テンセル生地", "item9": "5.5cm コンフォート層 2cm エアリーフォーム+天然キャメル 5cm ダブルTPEファイバー 4cm ハードウレタン", "item10": "14cm小口径ポケットコイル", "icon1": "", "icon2": "", "icon3": "", "icon4": "", "icon5": "", "icon6": "", "icon7": "", "icon8": "", "icon9": "", "icon10": "", "image": "shopline://shop_images/Rest-7.jpg"}}}, "block_order": ["174221927367475abcf3", "17422197675293e63a58", "174221976637673c4dfe", "1742219765050711aebd", "1742219763703c8c3f75"], "custom_css": []}, "17423762284924b84da7": {"disabled": false, "type": "custom-html", "settings": {"html": "<div class=\"page-width\">\n  <h2 id=\"section4\" class=\"rte rich-text__heading title3\">マットレスの選び方\n</h2>\n</div>", "color_scheme": "none", "padding_top": 0, "padding_bottom": 0}, "blocks": {}, "block_order": [], "custom_css": ["h2 {background-color: #399e96; color: #fff; text-align: center; padding: 10px; border-radius: 10px; font-size: 24px;}", "@media (max-width: 768px) {h2 {font-size: 18px; }}"]}, "1742378666888e4b2478": {"disabled": false, "type": "shopline://apps/高阶组件库/sections/product-category-list/01405986-ae6f-4fcc-adaa-2a21f14efc9f", "settings": {"tab_style": "underline", "tab_color": "#000000", "tab_button_bg_color": "#ffffff", "tab_button_text_color": "#000000", "pc_show": true, "mb_show": true, "products_num": 29, "pc_cols": 5, "sticky": false, "slice_in_pc": false, "mb_layout": 1, "tab_text_align": "left", "text_align": "left", "button_text": "すべて見る", "image_ratio": "100", "product_fill_type": "contain", "product_hover_show_next": true, "product_title_show_type": "full_display", "product_add_to_cart": false, "mobile_quick_add_btn_style": "icon", "quick_add_btn_background": "#000000", "quick_add_btn_font_color": "#FFFFFF", "product_mobile_title_show": true, "product_discount": true, "product_discount_style": "ratio", "product_discount_position": "left_top", "product_discount_radius": 40, "image_width": "50%", "show_touch": false, "horizontal_spacing": 40, "vertical_spacing": 20, "container_bg_color": "#FFFFFF", "grid_horizontal_space": 20, "edges": "rounded", "product_grid_image_margin": "0", "superscript_decimals": false, "color_entry_line": "#EAECEC", "color_page_background": "#FFFCFA", "color_text": "#29252C", "color_sale": "#171F2B", "color_light_text": "#949494", "color_tag_text": "#ffffff", "color_tag_background": "#C20000", "soldOut_color_tag_text": "#000000", "soldOut_color_tag_background": "#ffffff", "color_btn_text": "#ffffff", "color_image_background": "#ffffff", "card_border_thickness": 0, "card_border_radius": 0, "card_color": "#fafafa", "card_style": true, "container_max_width": 1582, "container_config": {"pc": {"left": 40, "right": 40, "top": 0, "bottom": 60, "lock": false}, "mobile": {"left": 20, "right": 20, "top": 0, "bottom": 80, "lock": false}}, "container_margin_config": {"pc": {"left": 0, "right": 0, "top": 0, "bottom": 30, "lock": false}, "mobile": {"left": 0, "right": 0, "top": 0, "bottom": 0}}, "title_font_family": "Inter:600", "title_font_color": "#000000", "title_pc_font_size": 24, "title_m_font_size": 14, "title_letter_spacing": 0, "title_line_height": 0.9, "product_title_font_family": "Inter", "product_title_color": "#000000", "product_title_pc_font_size": 16, "product_title_m_font_size": 12, "product_title_letter_spacing": 0, "product_title_pc_line_height": 1, "product_title_m_line_height": 1.6, "btn_font_family": "Inter", "btn_font_color": "#FFFFFF", "btn_pc_font_size": 13, "btn_m_font_size": 12, "btn_letter_spacing": 0, "btn_line_height": 1.6, "btn_background": "#111111", "btn_style": "primary", "btn_hover_animation": "1", "btn_border_thickness": 1, "btn_border_opacity": 100, "btn_border_radius": 40, "btn_border_color": "#111111", "btn_shadow_opacity": 0, "btn_shadow_offset_x": 0, "btn_shadow_offset_y": 4, "btn_shadow_blur": 5, "btn_shadow_color": "#000000", "product_price_pc_font_family": "Inter", "product_price_m_font_family": "Inter", "product_price_color": "#1A1C1E", "product_price_pc_font_size": 16, "product_price_m_font_size": 14, "product_price_letter_spacing": 0, "product_price_pc_line_height": 1.6, "product_price_m_line_height": 1.6, "product_underlined_price_pc_font_family": "Inter", "product_underlined_price_m_font_family": "Inter", "product_underlined_price_color": "#000000", "product_underlined_price_pc_font_size": 16, "product_underlined_price_m_font_size": 12, "product_underlined_price_letter_spacing": 0, "product_underlined_price_pc_line_height": 1.6, "product_underlined_price_m_line_height": 1.6, "product_card_border_thickness": 0, "product_card_border_opacity": 10, "product_card_border_radius": 0, "product_card_border_color": "#000000", "product_card_shadow_opacity": 0, "product_card_shadow_offset_x": 0, "product_card_shadow_offset_y": 4, "product_card_shadow_blur": 5, "product_card_shadow_color": "#000000", "mobile_quick_add_btn_layout": "normal"}, "blocks": {"1742378666888e4445c2": {"type": "collection", "icon": "productCategories", "settings": {"title": "オリジナルマットレスシリーズ", "category_id": "12269200204903426785804095"}, "blockId": "1742378666888e4445c2", "disabled": false}, "1747718733477575aab3": {"type": "collection", "settings": {"title": "合わせてベッド購入もおすすめ！", "category_id": "12266502787279248480054095"}}}, "block_order": ["1742378666888e4445c2", "1747718733477575aab3"], "custom_css": [".advc-product-item-info {background-color: #fafafa !important;}", ".product-grid-font {display: -webkit-box; -webkit-line-clamp: 1 !important; overflow: hidden; -webkit-box-orient: vertical; margin: 0 !important;}"]}, "1742524768141805b5c4": {"disabled": false, "type": "shopline://apps/高阶组件库/sections/shoppable-image/01405986-ae6f-4fcc-adaa-2a21f14efc9f", "settings": {"title": "", "title_align": "center", "image_full_screen": false, "image_width": "small", "anchor_show_style": "click", "product_button_text": "View product", "enable_anchor_product_quick_view": true, "text_title": "", "description": "<p><span data-font-family=\"default\">マットレス内部は、主に3つのパーツに分かれています。CAGUUUでは、全てにおいて最上級仕様に仕上げているため、ハイクラスな寝心地を実現。<br><br><strong>①キルト層（肌に直接触れる層）</strong><br></span></p>\n<p><strong>②クッション層（コイルの上に敷かれる層）</strong></p>\n<p><strong>③スプリング層（マットレスを支える層）</strong></p>", "button_text": "", "jump_link": "", "text_align": "left", "text_position": "right", "display_style": "right", "container_max_width": 1420, "container_config": {"pc": {"left": 40, "right": 40, "top": 20, "bottom": 20, "lock": true}, "mobile": {"left": 20, "right": 20, "top": 20, "bottom": 20, "lock": true}}, "container_margin_config": {"pc": {"left": 0, "right": 0, "top": 0, "bottom": 0}, "mobile": {"left": 0, "right": 0, "top": 0, "bottom": 0}}, "container_bg_color": "#FFFFFF", "title_font_family": "Inter", "title_font_color": "#000000", "title_pc_font_size": 22, "title_m_font_size": 12, "title_letter_spacing": 0, "title_line_height": 1.2, "sub_title_font_family": "Poppins:600", "sub_title_font_color": "#000000", "sub_title_pc_font_size": 36, "sub_title_m_font_size": 20, "sub_title_letter_spacing": 0, "sub_title_line_height": 0.8, "body_font_family": "Montserrat:400", "body_font_color": "#000000", "body_pc_font_size": 16, "body_m_font_size": 14, "body_letter_spacing": 0, "body_line_height": 1.6, "btn_font_family": "Montserrat:500", "btn_font_color": "#FFFFFF", "btn_pc_font_size": 13, "btn_m_font_size": 12, "btn_letter_spacing": 0, "btn_line_height": 1.6, "btn_background": "#111111", "btn_style": "primary", "btn_hover_animation": "1", "btn_border_thickness": 1, "btn_border_opacity": 100, "btn_border_radius": 0, "btn_border_color": "#111111", "btn_shadow_opacity": 0, "btn_shadow_offset_x": 0, "btn_shadow_offset_y": 4, "btn_shadow_blur": 5, "btn_shadow_color": "#000000", "image_border_thickness": 0, "image_border_opacity": 100, "image_border_radius": 0, "image_border_color": "#000000", "image_shadow_opacity": 0, "image_shadow_offset_x": 0, "image_shadow_offset_y": 4, "image_shadow_blur": 5, "image_shadow_color": "#000000", "image": "shopline://shop_images/Memory-Rest-p23.jpg"}, "blocks": {}, "block_order": [], "custom_css": []}, "17432637058305319391": {"disabled": false, "type": "custom-html", "settings": {"html": "<div class=\"page-width\">\n    <div class=\"image-container\">\n        <img src=\"https://img.myshopline.com/image/store/1726464192427/grandform-compare-0404.jpeg?w=4500&h=2829\" alt=\"示例图片\" id=\"mainImage\">\n    </div>\n\n    <!-- 图片弹窗 -->\n    <div class=\"modal\" id=\"imageModal\"><img src=\"https://img.myshopline.com/image/store/1726464192427/grandform-compare-0404.jpeg?w=4500&h=2829\" alt=\"放大图片\" id=\"modalImage\">\n        <span class=\"close-btn\" id=\"closeModal\">&times;</span>\n    </div>\n</div>\n\n    <script>\n        const mainImage = document.getElementById(\"mainImage\");\n            const modal = document.getElementById(\"imageModal\");\n            const modalImage = document.getElementById(\"modalImage\");\n            const closeModal = document.getElementById(\"closeModal\");\n    \n            let scale = 1;\n            let isDragging = false, startX = 0, imgX = 0;\n            let touchStartDistance = 0;\n    \n            // 打开图片预览\n            mainImage.addEventListener(\"click\", () => {\n                modal.style.display = \"flex\";\n                modalImage.src = mainImage.src;\n                resetImage();\n            });\n    \n            // 关闭弹窗\n            closeModal.addEventListener(\"click\", () => {\n                modal.style.display = \"none\";\n                resetImage();\n            });\n    \n            // **PC 端：滚轮缩放**\n            modalImage.addEventListener(\"wheel\", (event) => {\n                event.preventDefault();\n                let newScale = scale + event.deltaY * -0.001;\n                scale = Math.min(Math.max(0.5, newScale), 2);\n                limitDrag(); // 计算边界限制\n                applyTransform();\n            });\n    \n            // **PC：鼠标拖拽**\n            modalImage.addEventListener(\"mousedown\", (event) => {\n                isDragging = true;\n                startX = event.clientX - imgX;\n                modalImage.style.cursor = \"grabbing\";\n            });\n    \n            window.addEventListener(\"mousemove\", (event) => {\n                if (!isDragging) return;\n                imgX = event.clientX - startX;\n                limitDrag(); // 限制左右边界\n                applyTransform();\n            });\n    \n            window.addEventListener(\"mouseup\", () => {\n                isDragging = false;\n                modalImage.style.cursor = \"grab\";\n            });\n    \n            // **手机端：双指缩放 & 限制左右拖拽**\n            modalImage.addEventListener(\"touchstart\", (event) => {\n                if (event.touches.length === 2) {\n                    touchStartDistance = getDistance(event.touches[0], event.touches[1]);\n                } else if (event.touches.length === 1) {\n                    isDragging = true;\n                    startX = event.touches[0].clientX - imgX;\n                }\n            });\n    \n            modalImage.addEventListener(\"touchmove\", (event) => {\n                event.preventDefault();\n                if (event.touches.length === 2) {\n                    // **双指缩放**\n                    let newDistance = getDistance(event.touches[0], event.touches[1]);\n                    let newScale = scale * (newDistance / touchStartDistance);\n                    scale = Math.min(Math.max(0.5, newScale), 3);\n                    limitDrag(); // 计算边界限制\n                    applyTransform();\n                    touchStartDistance = newDistance;\n                } else if (event.touches.length === 1 && isDragging) {\n                    // **单指仅左右拖拽**\n                    imgX = event.touches[0].clientX - startX;\n                    limitDrag();\n                    applyTransform();\n                }\n            });\n    \n            modalImage.addEventListener(\"touchend\", () => {\n                isDragging = false;\n            });\n    \n            // **计算双指之间的距离**\n            function getDistance(touch1, touch2) {\n                return Math.sqrt(\n                    Math.pow(touch2.clientX - touch1.clientX, 2) +\n                    Math.pow(touch2.clientY - touch1.clientY, 2)\n                );\n            }\n    \n            // **限制拖拽范围（防止图片超出可视区域）**\n            function limitDrag() {\n                let imgWidth = modalImage.naturalWidth * scale;\n                let viewWidth = window.innerWidth;\n                let maxDragX = Math.max(0, (imgWidth - viewWidth) / 2 / scale); // 计算拖拽边界\n    \n                if (imgX > maxDragX) imgX = maxDragX;\n                if (imgX < -maxDragX) imgX = -maxDragX;\n            }\n    \n            // **应用缩放 & 位置**\n            function applyTransform() {\n                modalImage.style.transform = `scale(${scale}) translate(${imgX}px, 0px)`;\n            }\n    \n            // **重置图片**\n            function resetImage() {\n                scale = 1;\n                imgX = 0;\n                applyTransform();\n            }\n    </script>", "color_scheme": "none", "padding_top": 0, "padding_bottom": 60}, "blocks": {}, "block_order": [], "custom_css": [".image-container {cursor: zoom-in;}", ".image-container img {width: 100%; height: auto; display: block;}", ".modal {display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0, 0, 0, 0.8); justify-content: center; align-items: center; z-index: 1000;}", ".modal img {max-width: 100%; cursor: grab;}", ".close-btn {position: absolute; top: 20px; right: 30px; font-size: 30px; color: white; cursor: pointer;}"]}, "17477190401157e945ba": {"disabled": false, "type": "rich-text", "settings": {"desktop_content_position": "left", "color_scheme": "none", "normal_width": true, "show_decoration": false, "padding_top": 0, "padding_bottom": 80}, "blocks": {"1747719040116f202dce": {"type": "text", "settings": {"text": "<p><span style=\"font-size: 14px;\" data-font-family=\"default\">120日間お試しキャンペーンポリシー</span></p>\n<p><span style=\"font-size: 12px;\" data-font-family=\"default\">※商品到着後、120日間ご自宅でじっくりお試しいただけます。使ってみて合わない場合は、全国どこでも無料で返品＆返金可能です。<br></span><span style=\"font-size: 12px;\" data-font-family=\"default\">※お客様のもとで傷・破損・汚損が生じた場合は、返品できません。 万が一、返品(回収)した商品に傷汚れ等が確認された場合、対象外になります。</span><span style=\"font-size: 12px;\" data-font-family=\"default\"><br></span><span style=\"font-size: 12px;\" data-font-family=\"default\">※弊社のご案内に従われない場合は返品受付できませんので、何卒ご了承ください。</span></p>"}, "blockId": "1747719040116f202dce"}}, "block_order": ["1747719040116f202dce"], "custom_css": []}}, "order": ["17363308009519ad3221", "1737341984967efdc505", "1736408247884c0f37f2", "17432637058305319391", "1736491572282e09b871", "1742524768141805b5c4", "173638660539498ef25d", "17412409682985d1f606", "174124097446920840bf", "17382230841188e8055d", "17412423907312713fc3", "173872266024790f92ee", "17387227971964202262", "17412421469403bec725", "1741326109990e4d9791", "174132613714072b1668", "1741326133957120e25d", "1741326146085f056f83", "17413261498462b333b3", "17423762284924b84da7", "17422192736749ea073b", "17422766658120ac3c15", "1741255038180180d560", "1742378666888e4b2478", "17477190401157e945ba"]}